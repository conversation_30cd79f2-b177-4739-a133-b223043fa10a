# app/processing/book_validator.py

"""Централизованный валидатор книг для обнаружения всех типов аномалий.

Этот модуль инкапсулирует всю логику проверки книги на аномалии,
используемую как в основном пайплайне (BookProcessor), так и в
инструментах анализа (run_anomaly_analyzer.py).
"""

import logging
from typing import Optional

from app.processing.anthology_detector import AnthologyDetector
from app.processing.canonical_model import CanonicalBook
from app.processing.dto import BookValidationContext
from app.processing.fragment_detector import FragmentDetector
from app.processing.parsing_report import ParsingReport
from app.processing.small_book_detector import SmallBookDetector

logger = logging.getLogger(__name__)


class BookValidator:
    """Централизованный валидатор для обнаружения всех типов аномалий в книгах.

    Инкапсулирует логику всех детекторов и предоставляет единый интерфейс
    для проверки книг на аномалии. Используется как в основном пайплайне,
    так и в инструментах анализа.
    """

    def __init__(
        self,
        fragment_detector: Optional[FragmentDetector] = None,
        small_book_detector: Optional[SmallBookDetector] = None,
        anthology_detector: Optional[AnthologyDetector] = None,
    ):
        """Инициализирует валидатор с детекторами аномалий.

        Если детекторы не переданы, создает их с параметрами из settings.py.

        Args:
            fragment_detector: Детектор ознакомительных фрагментов
            small_book_detector: Детектор структурных аномалий
            anthology_detector: Детектор антологий/сборников
        """
        # Импорт settings только при необходимости создания детекторов
        if small_book_detector is None or anthology_detector is None:
            from app import settings

        self.fragment_detector = fragment_detector or FragmentDetector()

        # Создаем SmallBookDetector с параметрами из settings
        self.small_book_detector = small_book_detector or SmallBookDetector(
            min_chapters=settings.QUARANTINE_MIN_CHAPTERS,
            min_content_chars=settings.QUARANTINE_MIN_CONTENT_CHARS,
        )

        # Создаем AnthologyDetector с параметрами из settings
        self.anthology_detector = anthology_detector or AnthologyDetector(
            min_authors_threshold=settings.QUARANTINE_ANTHOLOGY_MIN_AUTHORS,
        )

    def validate(
        self,
        canonical_book: CanonicalBook,
        parsing_report: Optional[ParsingReport] = None,
    ) -> list[dict[str, str]]:
        """Проверяет книгу на все типы аномалий.

        ПОЛНАЯ ВАЛИДАЦИЯ для diagnostic_mode и инструментов анализа.
        Выполняет последовательную проверку книги всеми детекторами
        и возвращает список всех найденных аномалий с причинами.

        Используется в:
        - diagnostic_mode BookProcessor для сбора всех аномалий
        - run_anomaly_analyzer.py для полного анализа
        - инструментах, которым нужен полный список аномалий

        Args:
            canonical_book: Каноническая модель книги для проверки
            parsing_report: Отчет о парсинге с диагностической информацией (опционально)

        Returns:
            Список словарей с найденными аномалиями в формате:
            [{"type": "trial_fragments", "reason": "..."}, ...]
            Пустой список если аномалий не найдено.
        """
        # Создаем контекст валидации с предвычисленными метриками
        context = self._create_validation_context(canonical_book)

        anomalies = []

        # Проверка 1: Ознакомительные фрагменты (приоритет 1)
        if self.fragment_detector.is_fragment(context):
            reason = self.fragment_detector.get_fragment_reason(context)
            anomalies.append({"type": "trial_fragments", "reason": reason})

        # Проверка 2: Структурные аномалии (приоритет 2)
        quarantine_type = self.small_book_detector.check_book_structure(context)
        if quarantine_type:
            reason = self.small_book_detector.get_rejection_reason(context)

            # Преобразуем QuarantineType в строковый тип аномалии
            if quarantine_type.value == "small_content":
                anomalies.append({"type": "small_content", "reason": reason})
            elif quarantine_type.value == "few_chapters":
                anomalies.append({"type": "few_chapters", "reason": reason})

        # Проверка 3: Антологии/сборники (приоритет 3)
        if self.anthology_detector.is_anthology(context):
            reason = self.anthology_detector.get_anthology_reason(context)
            anomalies.append({"type": "anthology_books", "reason": reason})

        # Проверка 4: Сломанные сноски (если доступен отчет о парсинге)
        if parsing_report and self._check_footnotes_anomaly(parsing_report):
            anomalies.append(
                {
                    "type": "broken_footnotes",
                    "reason": "Обнаружены нераспарсенные сноски в FB2 файле",
                }
            )

        logger.debug(f"Валидация завершена: найдено {len(anomalies)} аномалий")
        return anomalies

    def get_first_blocking_anomaly(
        self,
        canonical_book: CanonicalBook,
        parsing_report: Optional[ParsingReport] = None,
    ) -> Optional[dict[str, str]]:
        """Возвращает первую блокирующую аномалию для основного пайплайна.

        ОПТИМИЗИРОВАННАЯ ВЕРСИЯ с early exit для production режима.
        Останавливается на первой найденной аномалии, экономя до 85% вычислений.

        Используется в BookProcessor для определения типа карантина.
        Возвращает только первую найденную аномалию согласно приоритету.

        Args:
            canonical_book: Каноническая модель книги для проверки
            parsing_report: Отчет о парсинге с диагностической информацией (опционально)

        Returns:
            Словарь с первой найденной аномалией или None если аномалий нет
        """
        # Создаем контекст валидации с предвычисленными метриками (один раз)
        context = self._create_validation_context(canonical_book)

        # Проверка 1: Ознакомительные фрагменты (приоритет 1)
        # EARLY EXIT: Если найден фрагмент, сразу возвращаем результат
        if self.fragment_detector.is_fragment(context):
            reason = self.fragment_detector.get_fragment_reason(context)
            return {"type": "trial_fragments", "reason": reason}

        # Проверка 2: Структурные аномалии (приоритет 2)
        # EARLY EXIT: Если найдена структурная аномалия, сразу возвращаем результат
        quarantine_type = self.small_book_detector.check_book_structure(context)
        if quarantine_type:
            reason = self.small_book_detector.get_rejection_reason(context)

            # Преобразуем QuarantineType в строковый тип аномалии
            if quarantine_type.value == "small_content":
                return {"type": "small_content", "reason": reason}
            elif quarantine_type.value == "few_chapters":
                return {"type": "few_chapters", "reason": reason}

        # Проверка 3: Антологии/сборники (приоритет 3)
        # EARLY EXIT: Если найдена антология, сразу возвращаем результат
        if self.anthology_detector.is_anthology(context):
            reason = self.anthology_detector.get_anthology_reason(context)
            return {"type": "anthology_books", "reason": reason}

        # Проверка 4: Сломанные сноски (приоритет 4)
        # EARLY EXIT: Если найдены проблемы со сносками, сразу возвращаем результат
        if parsing_report and self._check_footnotes_anomaly(parsing_report):
            return {
                "type": "broken_footnotes",
                "reason": "Обнаружены нераспарсенные сноски в FB2 файле",
            }

        # Если никаких аномалий не найдено
        return None

    def _create_validation_context(self, canonical_book: CanonicalBook) -> BookValidationContext:
        """Создает контекст валидации с предвычисленными метриками.

        Централизует все ресурсоемкие вычисления в одном месте для устранения
        избыточных вычислений в детекторах.

        Args:
            canonical_book: Каноническая модель книги

        Returns:
            BookValidationContext с предвычисленными метриками
        """
        # Базовые метрики
        author_count = len(canonical_book.authors)
        chapter_count = len(canonical_book.chapters)
        has_chapters = chapter_count > 0

        # Вычисляем общий объем контента (используем быструю оценку)
        total_content_length = sum(ch.estimated_content_length for ch in canonical_book.chapters)

        # Средняя длина главы (избегаем деления на ноль)
        avg_chapter_length = total_content_length / chapter_count if chapter_count > 0 else 0.0

        # Извлекаем содержимое последней главы для FragmentDetector
        last_chapter_content = ""
        if canonical_book.chapters:
            last_chapter = canonical_book.chapters[-1]

            # Используем статический метод FragmentDetector для извлечения текста
            # Если последняя глава короткая и есть предпоследняя, объединяем их
            if last_chapter.estimated_content_length < 300 and len(canonical_book.chapters) > 1:
                last_chapter_content = (
                    FragmentDetector._extract_plain_text_from_elements(last_chapter.content_elements)
                    + " "
                    + FragmentDetector._extract_plain_text_from_elements(canonical_book.chapters[-2].content_elements)
                )
            else:
                last_chapter_content = FragmentDetector._extract_plain_text_from_elements(last_chapter.content_elements)

        return BookValidationContext(
            book=canonical_book,
            author_count=author_count,
            chapter_count=chapter_count,
            total_content_length=total_content_length,
            last_chapter_content=last_chapter_content,
            has_chapters=has_chapters,
            avg_chapter_length=avg_chapter_length,
        )

    def _check_footnotes_anomaly(self, parsing_report: ParsingReport) -> bool:
        """Проверяет наличие проблем со сносками в отчете о парсинге.

        Args:
            parsing_report: Отчет о парсинге с диагностической информацией

        Returns:
            True если обнаружены проблемы со сносками
        """
        # Используем официальный API ParsingReport для проверки сломанных сносок
        return parsing_report.has_broken_footnotes()
