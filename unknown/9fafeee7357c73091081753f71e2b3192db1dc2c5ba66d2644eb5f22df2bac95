# app/processing/__init__.py

"""Модуль обработки книг для Ingestion Pipeline.

Содержит все компоненты для воркера-потребителя:
- TaskQueueManager: управление очередями Redis
- BookParser: парсинг книжных файлов
- BookProcessor: высокоуровневая обработка с дедупликацией
- DatabaseSaver: сохранение в PostgreSQL
- BookDataBuilder: подготовка DTO для DatabaseSaver
- BookDTO, BookSourceInfo: объекты передачи данных
- ErrorHandler: обработка ошибок
- TaskMonitor: мониторинг зависших задач
- QuarantineProcessor: обработка файлов в карантине

Примечание: В новой архитектуре файлы больше не перемещаются между директориями.
Архивы читаются потоково через StorageManager с логическими блокировками через Redis.
"""
