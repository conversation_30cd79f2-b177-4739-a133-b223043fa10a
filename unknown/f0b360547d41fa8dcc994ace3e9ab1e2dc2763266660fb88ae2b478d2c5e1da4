# app/processing/parsers/fb2/utils.py

"""
Утилитные функции для FB2 парсера.

Содержит общие функции, используемые различными компонентами парсера
для избежания циклических зависимостей.
"""

import re
from typing import Optional

from .fb2_model import Annotation, Paragraph


def extract_clean_text(title_annotation: Optional[Annotation]) -> str:
    """Извлекает чистый текст из Annotation, удаляя HTML-теги и лишние пробелы.

    Эта функция является общей утилитой для извлечения текста из заголовков
    и других элементов FB2, используемой как MarkerAnalyzer, так и MarkdownRenderer.

    Args:
        title_annotation: Annotation элемент для извлечения текста

    Returns:
        Очищенный текст без HTML-тегов и с нормализованными пробелами
    """
    if not title_annotation:
        return ""

    # Собираем весь текст из элементов
    title_parts = []
    for element in title_annotation.elements:
        if isinstance(element, Paragraph):
            for content_part in element.content:
                if isinstance(content_part, str):
                    title_parts.append(content_part)
                elif hasattr(content_part, "text") and content_part.text:  # Emphasis, Strong, etc.
                    title_parts.append(content_part.text)

    # Объединяем части и очищаем
    result = ". ".join(filter(None, [p.strip() for p in title_parts]))
    return clean_markdown_spacing(result)


def clean_markdown_spacing(text: str) -> str:
    """Очищает избыточные пробелы в тексте.

    Убирает лишние пробелы вокруг форматирования и нормализует множественные пробелы.

    Args:
        text: Исходный текст для очистки

    Returns:
        Очищенный текст с нормализованными пробелами
    """
    # Убираем лишние пробелы вокруг форматирования
    text = re.sub(r"\s+\*\s+", " *", text)  # " * " → " *"
    text = re.sub(r"\s+\*\*\s+", " **", text)  # " ** " → " **"
    text = re.sub(r"\s+\*\*\*\s+", " ***", text)  # " *** " → " ***"
    text = re.sub(r"\*\s+", "*", text)  # "* " → "*"
    text = re.sub(r"\*\*\s+", "**", text)  # "** " → "**"
    text = re.sub(r"\*\*\*\s+", "***", text)  # "*** " → "***"
    text = re.sub(r"\s+\*", " *", text)  # " *" → " *"
    text = re.sub(r"\s+\*\*", " **", text)  # " **" → " **"
    text = re.sub(r"\s+\*\*\*", " ***", text)  # " ***" → " ***"

    # Убираем множественные пробелы
    text = re.sub(r" {2,}", " ", text)
    return text.strip()
