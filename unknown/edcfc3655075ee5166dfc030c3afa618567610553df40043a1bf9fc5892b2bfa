# app/processing/small_book_detector.py

import logging

from .dto import BookValidationContext
from .error_handler import QuarantineType

logger = logging.getLogger(__name__)


class SmallBookDetector:
    """Детектор структурных аномалий книг с настраиваемыми критериями.

    Определяет два типа структурных проблем:
    - Недостаточный объем контента (< min_content_chars) → SMALL_CONTENT
    - Аномально малое количество глав (< min_chapters) → FEW_CHAPTERS

    Логика проверки: сначала проверяется объем контента, затем структура глав.

    Пороги настраиваются централизованно через settings.py и передаются
    в конструктор для обеспечения dependency injection и упрощения тестирования.
    """

    def __init__(self, min_chapters: int = 7, min_content_chars: int = 50000):
        """Инициализирует детектор с настраиваемыми порогами.

        Args:
            min_chapters: Минимальное количество глав
            min_content_chars: Минимальный объем контента в символах
        """
        self.min_chapters = min_chapters
        self.min_content_chars = min_content_chars

        logger.debug(
            f"SmallBookDetector initialized: min_chapters={self.min_chapters}, "
            f"min_content_chars={self.min_content_chars}"
        )

    def _is_small_content(self, context: BookValidationContext) -> bool:
        """Проверяет, имеет ли книга недостаточный объем контента.

        Args:
            context: Контекст валидации с предвычисленными метриками

        Returns:
            True если общий объем контента меньше минимального порога
        """
        if not context.has_chapters:
            return True

        if context.total_content_length < self.min_content_chars:
            logger.debug(f"Недостаточный объем контента: {context.total_content_length} < {self.min_content_chars}")
            return True

        return False

    def _has_few_chapters(self, context: BookValidationContext) -> bool:
        """Проверяет, имеет ли книга аномально малое количество глав.

        Args:
            context: Контекст валидации с предвычисленными метриками

        Returns:
            True если количество глав меньше минимального порога
        """
        if not context.has_chapters:
            return False  # Книги без глав обрабатываются через _is_small_content

        if context.chapter_count < self.min_chapters:
            logger.debug(f"Аномально мало глав: {context.chapter_count} < {self.min_chapters}")
            return True

        return False

    def check_book_structure(self, context: BookValidationContext) -> QuarantineType | None:
        """Проверяет структуру книги и возвращает тип карантина если есть проблемы.

        Args:
            context: Контекст валидации с предвычисленными метриками

        Returns:
            QuarantineType если книга имеет структурные проблемы, None если все в порядке
        """
        # Приоритет 1: Проверка на недостаточный объем контента
        if self._is_small_content(context):
            return QuarantineType.SMALL_CONTENT

        # Приоритет 2: Проверка на малое количество глав (только если объем достаточный)
        if self._has_few_chapters(context):
            return QuarantineType.FEW_CHAPTERS

        return None  # Книга прошла все проверки

    def get_rejection_reason(self, context: BookValidationContext) -> str:
        """Возвращает детальную причину отклонения книги.

        Args:
            context: Контекст валидации с предвычисленными метриками

        Returns:
            Строка с описанием причины отклонения
        """
        quarantine_type = self.check_book_structure(context)

        if quarantine_type == QuarantineType.SMALL_CONTENT:
            if not context.has_chapters:
                return "Недостаточный объем контента: книга без глав"

            return f"Недостаточный объем контента: ~{context.total_content_length} символов < {self.min_content_chars}"

        elif quarantine_type == QuarantineType.FEW_CHAPTERS:
            return f"Аномально мало глав: {context.chapter_count} < {self.min_chapters}"

        else:
            return "Книга соответствует критериям структуры"

    def get_configuration(self) -> dict[str, int]:
        """Возвращает текущую конфигурацию детектора.

        Returns:
            Словарь с настройками
        """
        return {
            "min_chapters": self.min_chapters,
            "min_content_chars": self.min_content_chars,
        }
