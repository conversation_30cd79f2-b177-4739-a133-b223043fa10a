import io
import logging
from typing import Any

from app.database.queries import check_book_duplicates
from app.processing.anthology_detector import AnthologyDetector
from app.processing.artifact_saver import save_artifact
from app.processing.book_data_builder import BookDataBuilder
from app.processing.book_validator import BookValidator
from app.processing.canonical_model import CanonicalBook
from app.processing.database_saver import DatabaseSaver
from app.processing.date_extractor import (
    extract_best_date,
)
from app.processing.error_handler import (
    ProcessingError,
    QuarantineError,
    QuarantineType,
)
from app.processing.fragment_detector import FragmentDetector
from app.processing.hash_computer import HashComputer
from app.processing.parser_dispatcher import ParserDispatcher
from app.processing.parsing_report import ParsingReport
from app.processing.pruner import prune_book
from app.processing.queue_manager import TaskQueueManager
from app.processing.small_book_detector import SmallBookDetector
from app.storage.base import StorageManager


class BookProcessor:
    """Высокоуровневый процессор для обработки книг.

    Инкапсулирует всю бизнес-логику обработки книги из воркера
    """

    def __init__(self, storage_manager: StorageManager, diagnostic_mode: bool = False):
        self.logger = logging.getLogger(__name__)

        # Dependency Injection для StorageManager
        self.storage_manager = storage_manager

        # Режим диагностики для неразрушающего анализа аномалий
        self.diagnostic_mode = diagnostic_mode
        self.detected_anomalies: list = []

        # Импорт settings для настройки детекторов
        from app import settings

        # Инициализация компонентов обработки
        self.parser_dispatcher = ParserDispatcher()
        self.hash_computer = HashComputer()
        self.database_saver = DatabaseSaver()
        self.book_data_builder = BookDataBuilder()
        self.queue_manager = TaskQueueManager()

        # Детекторы качества книг с параметрами из settings
        self.fragment_detector = FragmentDetector()  # Детектор фрагментов
        self.small_book_detector = SmallBookDetector(
            min_chapters=settings.QUARANTINE_MIN_CHAPTERS,
            min_content_chars=settings.QUARANTINE_MIN_CONTENT_CHARS,
        )
        self.anthology_detector = AnthologyDetector(
            min_authors_threshold=settings.QUARANTINE_ANTHOLOGY_MIN_AUTHORS,
        )

        # Централизованный валидатор книг
        self.book_validator = BookValidator(
            fragment_detector=self.fragment_detector,
            small_book_detector=self.small_book_detector,
            anthology_detector=self.anthology_detector,
        )

    def process(self, task_data: dict[str, Any]) -> dict[str, Any]:
        """Выполняет полную обработку книги.

        Args:
            task_data: Данные задачи из очереди с полями:
                - source_type: int
                - source_id: int
                - archive_path: str
                - book_filename: str
                - archive_mtime: float

        Returns:
            Результат обработки с информацией о выполненных этапах

        Raises:
            QuarantineError: При ошибках бизнес-логики
            Exception: При критических системных ошибках

        """
        # Сброс списка аномалий в начале каждой обработки
        self.detected_anomalies = []

        try:
            archive_path = task_data["archive_path"]
            book_filename = task_data["book_filename"]

            self.logger.debug(f"📖 Начинаем обработку: {book_filename} из {archive_path}")

            # Этап 0: Извлечение source_id и source_type из задачи
            source_id = task_data.get("source_id")
            source_type = task_data.get("source_type")

            # Этап 1: Извлечение содержимого из архива
            book_stream = self._extract_content(task_data)

            # Этап 2: Парсинг в каноническую модель
            canonical_book = self._parse_to_canonical(book_stream, task_data)

            # Установка source_info в каноническую модель
            canonical_book.source_id = source_id
            canonical_book.source_type = source_type

            # Этап 2.6: Мониторинг сломанных сносок (только логирование)
            self._log_broken_footnotes_monitoring()

            # Этап 2.5: Проверка на ознакомительный фрагмент
            self._check_fragment(canonical_book)

            # Этап 3: Постобработка канонической модели
            book_id = self._postprocess_canonical_book(canonical_book, task_data)

            # Этап 4: Проверка дубликатов
            duplicate_result = self._check_duplicates(canonical_book)
            if duplicate_result["should_skip"]:
                return {
                    "status": "skipped",
                    "reason": "duplicate",
                }

            # Этап 5: Сохранение в БД и очередь RAG
            self._save_and_enqueue(canonical_book, task_data, book_id, duplicate_result["hashes"])

            self.logger.info(f"✅ Книга успешно обработана: {canonical_book.title} (ID: {book_id})")

            return {
                "status": "success",
                "book_id": book_id,
                "title": canonical_book.title,
            }

        except Exception:
            raise

    def _extract_content(self, task_data: dict[str, Any]) -> io.BytesIO:
        """Извлекает содержимое файла книги из архива через StorageManager."""
        archive_path = task_data["archive_path"]
        book_filename = task_data["book_filename"]

        try:
            book_stream = self.storage_manager.read_file_from_archive(archive_path, book_filename)
            self.logger.debug(f"Файл {book_filename} извлечен из архива {archive_path}")
            return book_stream
        except Exception as e:
            raise QuarantineError(f"Ошибка извлечения файла {book_filename} из архива {archive_path}: {e}") from e

    def _parse_to_canonical(self, book_stream: io.BytesIO, task_data: dict[str, Any]):
        """Парсит поток книги в каноническую модель."""
        book_filename = task_data["book_filename"]
        archive_mtime = task_data["archive_mtime"]

        canonical_book, parsing_report = self.parser_dispatcher.parse_to_canonical(
            source=book_stream, source_filename=book_filename, file_mtime=archive_mtime
        )
        self.logger.debug(f"Книга распарсена: {canonical_book.title}")

        # Сохраняем отчет о парсинге для дальнейшего использования
        self._last_parsing_report = parsing_report

        return canonical_book

    def _log_broken_footnotes_monitoring(self) -> None:
        """Логирует информацию о сломанных сносках для аналитики без отправки в карантин.

        Этот метод предназначен только для мониторинга и сбора аналитики.
        В продакшене broken_footnotes НЕ являются критической ошибкой.
        """
        # Используем официальный ParsingReport вместо хака с _last_transformer
        if hasattr(self, "_last_parsing_report") and self._last_parsing_report:
            parsing_report = self._last_parsing_report
            if parsing_report.has_broken_footnotes():
                broken_footnotes = parsing_report.broken_footnotes
                broken_count = len(broken_footnotes)
                broken_ids = ", ".join(broken_footnotes[:5])  # Показываем первые 5 ID

                # Логируем как INFO для аналитики, НЕ как WARNING/ERROR
                self.logger.info(
                    f"📊 МОНИТОРИНГ: Обнаружены недоступные сноски ({broken_count} шт.): {broken_ids}. "
                    f"Это НЕ критическая ошибка - информация для анализа эвристик извлечения."
                )

    def _determine_quarantine_type(
        self, canonical_book: CanonicalBook, parsing_report: ParsingReport | None = None
    ) -> tuple[QuarantineType, str] | None:
        """Определяет тип карантина для книги на основе централизованного валидатора.

        Args:
            canonical_book: Каноническая модель книги
            parsing_report: Отчет о парсинге с диагностической информацией (опционально)

        Returns:
            Кортеж (тип_карантина, причина) или None если книга прошла все проверки
        """
        # Используем централизованный валидатор для получения первой блокирующей аномалии
        first_anomaly = self.book_validator.get_first_blocking_anomaly(canonical_book, parsing_report)

        if not first_anomaly:
            return None  # Книга прошла все проверки

        anomaly_type = first_anomaly["type"]
        reason = first_anomaly["reason"]

        # Преобразуем строковый тип аномалии в QuarantineType
        if anomaly_type == "trial_fragments":
            # Дополняем причину информацией об авторе для фрагментов
            author_info = self._format_author_info(canonical_book)
            enhanced_reason = (
                f"Обнаружен ознакомительный фрагмент: '{canonical_book.title}' "
                f"от {author_info}. Фрагменты нарушают дедупликацию и блокируют "
                f"загрузку полных версий книг. Детали: {reason}"
            )
            return QuarantineType.TRIAL, enhanced_reason
        elif anomaly_type == "small_content":
            return QuarantineType.SMALL_CONTENT, reason
        elif anomaly_type == "few_chapters":
            return QuarantineType.FEW_CHAPTERS, reason
        elif anomaly_type == "anthology_books":
            return QuarantineType.ANTHOLOGIES, reason
        elif anomaly_type == "broken_footnotes":
            return QuarantineType.FOOTNOTES, reason
        else:
            # Неизвестный тип аномалии - логируем и возвращаем как ERROR
            self.logger.warning(f"Неизвестный тип аномалии: {anomaly_type}")
            return QuarantineType.ERROR, f"Неизвестная аномалия: {reason}"

    def _format_author_info(self, canonical_book: CanonicalBook) -> str:
        """Форматирует информацию об авторах книги."""
        if not canonical_book.authors:
            return "Неизвестный автор"

        authors_list = []
        for author in canonical_book.authors:
            name_parts = [
                author.first_name,
                author.middle_name,
                author.last_name,
            ]
            full_name = " ".join(filter(None, name_parts))
            if author.nickname:
                full_name += f" ({author.nickname})"
            authors_list.append(full_name)
        return ", ".join(authors_list)

    def _check_fragment(self, canonical_book) -> None:
        """Проверяет качество книги и определяет нужен ли карантин.

        Это комплексная проверка для предотвращения попадания некачественных книг в основную коллекцию.
        Включает проверки на фрагменты, маленькие книги и антологии.

        Raises:
            QuarantineError: Если книга должна быть помещена в карантин

        """
        # Используем официальный ParsingReport для проверки сносок
        parsing_report = getattr(self, "_last_parsing_report", None)

        # Определяем тип карантина с помощью централизованного валидатора
        quarantine_result = self._determine_quarantine_type(canonical_book, parsing_report)

        if quarantine_result:
            quarantine_type, reason = quarantine_result

            if self.diagnostic_mode:
                # В режиме диагностики - собираем аномалию и продолжаем
                self.logger.warning(f"DIAGNOSTIC: Обнаружена аномалия {quarantine_type.value}, обработка продолжается.")
                self.detected_anomalies.append(
                    {
                        "type": quarantine_type.value,
                        "reason": reason,
                        "details": {
                            "title": canonical_book.title,
                            "authors": self._format_author_info(canonical_book),
                            "chapters_count": len(canonical_book.chapters),
                            "quarantine_category": quarantine_type.value,
                        },
                    }
                )
            else:
                # В производственном режиме - выбрасываем исключение как и раньше
                self.logger.warning(f"🚫 {reason}")

                # Генерируем типизированную QuarantineError для правильной обработки в воркере
                raise QuarantineError(
                    reason,
                    quarantine_type=quarantine_type,
                    details={
                        "title": canonical_book.title,
                        "authors": self._format_author_info(canonical_book),
                        "chapters_count": len(canonical_book.chapters),
                        "quarantine_category": quarantine_type.value,
                    },
                )

    def _postprocess_canonical_book(self, canonical_book, task_data: dict[str, Any]) -> str:
        """Координирует постобработку канонической модели через специализированные методы.

        Разделен на отдельные ответственности:
        - Генерация уникального ID книги
        - Обогащение модели метаданными генерации
        - Очистка текстового контента
        - Финализация ленивого рендеринга глав
        """
        # Этап 1: Генерация уникального идентификатора книги
        book_id = self._generate_book_id(canonical_book, task_data)

        # Этап 2: Обогащение модели метаданными процесса генерации ID
        self._enrich_with_id_metadata(canonical_book, task_data)

        # Этап 3: Очистка текстового контента от "мусора"
        self._clean_book_content(canonical_book)

        # Этап 4: Финализация ленивого рендеринга для корректной сериализации
        self._finalize_chapter_rendering(canonical_book)

        return book_id

    def _generate_book_id(self, canonical_book, task_data: dict[str, Any]) -> str:
        """Генерирует уникальный идентификатор книги на основе даты и подписи.

        Ответственность: создание стабильного UUIDv7 для книги.
        """
        archive_mtime = task_data["archive_mtime"]
        best_date, _ = extract_best_date(canonical_book.raw_source_model, archive_mtime)

        # Используем централизованные функции генерации UUID v7
        from app.utils.uuid_generator import (
            create_book_signature_for_processor,
            generate_book_uuid7,
        )

        book_signature = create_book_signature_for_processor(canonical_book)
        book_id = generate_book_uuid7(best_date, book_signature)

        self.logger.debug(f"Сгенерирован ID книги (UUIDv7) на основе даты {best_date}: {book_id}")
        return book_id

    def _enrich_with_id_metadata(self, canonical_book, task_data: dict[str, Any]) -> None:
        """Обогащает каноническую модель метаданными процесса генерации ID.

        Ответственность: сохранение информации о том, как и когда был сгенерирован ID.
        """
        archive_mtime = task_data["archive_mtime"]
        best_date, date_source = extract_best_date(canonical_book.raw_source_model, archive_mtime)

        # Сохраняем метаданные процесса генерации ID в модели
        canonical_book.book_id_generation_date = best_date.isoformat()
        canonical_book.book_id_date_source = date_source

        self.logger.debug(f"Источник даты для book_id: {date_source}")

    def _clean_book_content(self, canonical_book) -> None:
        """Очищает текстовый контент книги от OCR-мусора и артефактов.

        Ответственность: нормализация и очистка текстового содержимого.
        """
        prune_book(canonical_book)
        self.logger.debug("Каноническая модель очищена")

    def _finalize_chapter_rendering(self, canonical_book) -> None:
        """Финализирует рендеринг глав канонической модели.

        Ответственность: выполнение ленивого рендеринга для корректной сериализации.
        """
        canonical_book.render_and_finalize_chapters()
        self.logger.debug("Все главы отрендерены и финализированы")

    def _check_duplicates(self, canonical_book) -> dict[str, Any]:
        """Проверяет книгу на дубликаты и принимает бизнес-решения.

        Returns:
            Dict с результатом проверки и хэшами:
            {
                "should_skip": bool,
                "hashes": dict
            }
        """
        try:
            hashes = self.hash_computer.compute_hashes(canonical_book)
            duplicate_db_record = check_book_duplicates(hashes["metadata_hash"])

            should_skip = duplicate_db_record is not None
            if should_skip:
                self.logger.info(
                    f"Дубликат книги '{canonical_book.title}' пропущен. ID существующей записи: {duplicate_db_record.get('existing_book_id') if duplicate_db_record else 'unknown'}"
                )

            return {"hashes": hashes, "should_skip": should_skip}

        except Exception as e:
            # ИСПРАВЛЕНО: Различаем временные ошибки БД от критических
            # для предотвращения массового попадания в карантин при сбоях инфраструктуры

            # Импортируем psycopg для типизированной обработки ошибок
            import psycopg

            if isinstance(e, psycopg.OperationalError):
                # Временные ошибки БД (соединение, блокировки) - следует повторить
                self.logger.warning(f"Временная ошибка БД при проверке дубликатов (будет повторено): {e}")
                from app.processing.error_handler import ErrorType

                raise ProcessingError(
                    f"Временная ошибка БД при проверке дубликатов: {e}",
                    error_type=ErrorType.RETRY,
                    details={
                        "error_category": "connection",
                        "operation": "check_duplicates",
                    },
                ) from e
            else:
                # Остальные ошибки - критические
                self.logger.error(f"Критическая ошибка проверки дубликатов: {e}")
                raise ProcessingError(f"Критическая ошибка доступа к БД при проверке дубликатов: {e}") from e

    def _save_and_enqueue(
        self,
        canonical_book,
        task_data: dict[str, Any],
        book_id: str,
        hashes: dict[str, str],
    ):
        """Двухфазное сохранение книги для обеспечения атомарности данных.

        Фаза 1: Сохранение метаданных в БД (process_status=10)
        Фаза 2: Создание артефакта + обновление статуса (process_status=20)

        Это гарантирует целостность между БД и файловой системой.
        """
        # Подготавливаем DTO для сохранения
        book_dto = self.book_data_builder.build_book_dto_from_canonical(
            canonical_book,
            task_data,
            hashes["metadata_hash"],
        )

        # ФАЗА 1: Сохраняем метаданные в БД со статусом 10 (метаданные сохранены)
        self.database_saver.save_book_metadata_only(book_dto, book_id)
        self.logger.debug(f"Фаза 1: Метаданные книги сохранены в БД (статус 10): {book_id}")

        # ФАЗА 2: Создаем артефакт на диске
        artifact_path = save_artifact(canonical_book, book_id)
        self.logger.debug(f"Фаза 2: Каноническая модель сохранена: {artifact_path}")

        # ФАЗА 2: Обновляем статус на полностью обработано (20)
        self.database_saver.update_book_status(book_id, 20)
        self.logger.debug(f"Фаза 2: Статус книги обновлен на 20 (полностью обработано): {book_id}")

        # Ставим задачу в очередь для RAG-пайплайна
        self.queue_manager.enqueue_rag_task(book_id)
        self.logger.debug(f"Книга поставлена в очередь RAG: {book_id}")
