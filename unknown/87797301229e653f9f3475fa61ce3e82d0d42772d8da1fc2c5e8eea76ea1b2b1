"""
Построитель диагностических отчетов.

Содержит класс для создания структурированных отчетов диагностики
и функции для их сохранения в различных форматах.
"""

import json
import logging
import sys
import time
from pathlib import Path
from typing import Any

# Добавляем корневую директорию для импорта централизованных UUID утилит
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from app.utils.uuid_generator import decode_uuid7_timestamp

from .serialization import canonical_book_to_dict, clean_duplicated_fields

logger = logging.getLogger(__name__)


class DiagnosticReportBuilder:
    """Построитель диагностических отчетов."""

    def __init__(self, input_path: str, tool_version: str = "2.0"):
        """Инициализирует построитель отчетов.

        Args:
            input_path: Путь к входному файлу
            tool_version: Версия диагностического инструмента
        """
        self.start_time = time.time()
        self.report_data: dict[str, Any] = {
            "diagnostic_info": {
                "tool_version": tool_version,
                "input_path": input_path,
                "start_time": self.start_time,
                "processing_stages": [],
                "errors": [],
            },
            "detected_anomalies": [],
            "database_payload": {},
            "artifact_payload": {},
            "summary_stats": {},
        }

    def add_processing_stage(self, stage_description: str) -> None:
        """Добавляет этап обработки в отчет.

        Args:
            stage_description: Описание завершенного этапа
        """
        self.report_data["diagnostic_info"]["processing_stages"].append(stage_description)

    def add_error(self, error_message: str) -> None:
        """Добавляет ошибку в отчет.

        Args:
            error_message: Описание ошибки
        """
        self.report_data["diagnostic_info"]["errors"].append(error_message)

    def set_anomalies(self, anomalies: list[dict[str, Any]]) -> None:
        """Устанавливает список обнаруженных аномалий.

        Args:
            anomalies: Список аномалий
        """
        self.report_data["detected_anomalies"] = anomalies

    def set_database_payload(self, payload: dict[str, Any]) -> None:
        """Устанавливает database payload.

        Args:
            payload: Данные для базы данных
        """
        self.report_data["database_payload"] = payload

    def build_artifact_payload(self, book_id: str, canonical_book, archive_path: str | None, filename: str) -> None:
        """Строит artifact payload.

        Args:
            book_id: Идентификатор книги
            canonical_book: Объект CanonicalBook
            archive_path: Путь к архиву (если есть)
            filename: Имя файла
        """
        try:
            # Сериализуем каноническую книгу
            artifact_content = canonical_book_to_dict(canonical_book)

            # Убираем дублированные поля
            artifact_content = clean_duplicated_fields(artifact_content)

            # Декодируем дату из UUID для верификации
            decoded_uuid_date = decode_uuid7_timestamp(book_id)

            self.report_data["artifact_payload"] = {
                "id": book_id,
                "book_id_info": {
                    "book_id": book_id,
                    "generation_date": canonical_book.book_id_generation_date,
                    "date_source": canonical_book.book_id_date_source,
                    "decoded_date_from_uuid": decoded_uuid_date,
                },
                "source": {
                    "format": canonical_book.source_format,
                    "archive_path": archive_path,
                    "book_filename": filename,
                },
                "content": artifact_content,
            }
        except Exception as e:
            error_msg = f"Ошибка формирования artifact payload: {e}"
            logger.error(error_msg)
            self.add_error(error_msg)

    def set_summary_stats(self, stats: dict[str, Any]) -> None:
        """Устанавливает итоговую статистику.

        Args:
            stats: Словарь со статистикой
        """
        self.report_data["summary_stats"] = stats

    def finalize(self) -> dict[str, Any]:
        """Финализирует отчет и возвращает готовую структуру.

        Returns:
            Готовый диагностический отчет
        """
        end_time = time.time()
        self.report_data["diagnostic_info"]["end_time"] = end_time
        self.report_data["diagnostic_info"]["total_processing_time"] = round(end_time - self.start_time, 2)

        return self.report_data

    def get_anomalies_count(self) -> int:
        """Возвращает количество обнаруженных аномалий.

        Returns:
            Количество аномалий
        """
        return len(self.report_data["detected_anomalies"])

    def get_errors_count(self) -> int:
        """Возвращает количество ошибок.

        Returns:
            Количество ошибок
        """
        return len(self.report_data["diagnostic_info"]["errors"])

    def has_critical_errors(self) -> bool:
        """Проверяет наличие критических ошибок.

        Returns:
            True если есть критические ошибки
        """
        return self.get_errors_count() > 0


def save_report(report_data: dict[str, Any], path: Path) -> None:
    """Сохраняет диагностический отчет в JSON файл.

    Args:
        report_data: Словарь с данными отчета
        path: Путь для сохранения JSON файла
    """
    try:
        # Создаем директорию если не существует
        path.parent.mkdir(parents=True, exist_ok=True)

        # Сохраняем с красивым форматированием и поддержкой кириллицы
        with open(path, "w", encoding="utf-8") as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False, default=str)

        logger.debug(f"📄 Отчет сохранен: {path}")

    except Exception as e:
        logger.error(f"Ошибка сохранения отчета: {e}")


def print_console_summary(report_data: dict[str, Any], input_path: str, report_path: Path) -> None:
    """Выводит краткую сводку отчета в консоль.

    Args:
        report_data: Данные отчета
        input_path: Путь к входному файлу
        report_path: Путь к сохраненному отчету
    """
    print("\n" + "=" * 60)
    print("📋 ДИАГНОСТИЧЕСКИЙ ОТЧЕТ")
    print("=" * 60)

    print(f"📁 Обработанный файл: {input_path}")

    # Информация об аномалиях
    anomalies_count = len(report_data.get("detected_anomalies", []))
    if anomalies_count > 0:
        anomaly_types = [a.get("type", "unknown") for a in report_data.get("detected_anomalies", [])]
        print(f"⚠️  Найдено аномалий: {anomalies_count}")
        print(f"   Типы: {', '.join(anomaly_types)}")
    else:
        print("✅ Аномалий не найдено")

    # Ключевые метрики
    stats = report_data.get("summary_stats", {})
    if stats:
        print(f"📊 Главы: {stats.get('chapters_count', 'N/A')}")
        print(f"📊 Символов в главах: {stats.get('total_chapter_characters', 'N/A'):,}")
        print(f"📊 Длина аннотации: {stats.get('annotation_length', 'N/A')} символов")

        # Статистика по сноскам
        footnotes_total = stats.get("footnotes_total", 0)
        footnotes_broken = stats.get("footnotes_broken", 0)
        footnotes_success_rate = stats.get("footnotes_success_rate", 100.0)

        if footnotes_total > 0:
            if footnotes_broken > 0:
                print(
                    f"📝 Сноски: {footnotes_total} (сломано: {footnotes_broken}, успешность: {footnotes_success_rate}%)"
                )
            else:
                print(f"📝 Сноски: {footnotes_total} (все корректны)")

        print(f"⏱️  Время обработки: {stats.get('processing_time_seconds', 'N/A')} сек")

    # Информация об ошибках
    errors_count = len(report_data.get("diagnostic_info", {}).get("errors", []))
    if errors_count > 0:
        print(f"❌ Ошибок при обработке: {errors_count}")

    print(f"📄 Полный отчет сохранен: {report_path}")
    print("=" * 60)
