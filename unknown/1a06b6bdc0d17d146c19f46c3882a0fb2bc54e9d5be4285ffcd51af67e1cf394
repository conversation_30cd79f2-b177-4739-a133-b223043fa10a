"""
Пакет утилит для анализа и диагностики книг.

Содержит модули для парсинга входных данных, сериализации, работы с UUID,
построения отчетов и других утилит диагностического анализа.
"""

from .input_parser import parse_input_path
from .report_builder import DiagnosticReportBuilder, print_console_summary, save_report
from .serialization import canonical_book_to_dict

__all__ = [
    "parse_input_path",
    "canonical_book_to_dict",
    "DiagnosticReportBuilder",
    "save_report",
    "print_console_summary",
]
