#!/usr/bin/env python3
"""
Скрипт синхронизации книг с сайта SearchFloor.

Создает ZIP-архивы с книгами для последующей обработки существующим пайплайном.
Архивы именуются по диапазонам ID: 1000.zip (ID 1-1000), 2000.zip (ID 1001-2000) и т.д.

Примеры использования:
    # Полная синхронизация всех книг
    python run_05_searchfloor_sync.py --full-sync

    # Синхронизация конкретного диапазона
    python run_05_searchfloor_sync.py --range 5000-6000

    # Тихий режим для cron
    python run_05_searchfloor_sync.py --range 1-1000 --quiet
"""

import argparse
import logging
import sys
from pathlib import Path

# Добавляем корневую директорию в путь
sys.path.insert(0, str(Path(__file__).parent))

from app.ingestion.searchfloor_downloader import SearchFloorDownloader


def setup_logging(quiet: bool = False, debug: bool = False) -> None:
    """Настраивает логирование."""
    if quiet:
        level = logging.WARNING
    elif debug:
        level = logging.DEBUG
    else:
        level = logging.INFO

    logging.basicConfig(
        level=level, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s", datefmt="%Y-%m-%d %H:%M:%S"
    )


def parse_range(range_str: str) -> tuple[int, int]:
    """Парсит строку диапазона вида '1000-2000'."""
    try:
        start_str, end_str = range_str.split("-")
        start_id = int(start_str)
        end_id = int(end_str)

        if start_id <= 0 or end_id <= 0 or start_id > end_id:
            raise ValueError("Некорректный диапазон")

        return start_id, end_id
    except ValueError as e:
        raise argparse.ArgumentTypeError(f"Некорректный формат диапазона '{range_str}': {e}") from e


def main():
    parser = argparse.ArgumentParser(
        description="Синхронизация книг с сайта SearchFloor",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Примеры использования:

  Полная синхронизация всех книг:
    python run_05_searchfloor_sync.py --full-sync

  Синхронизация конкретного диапазона:
    python run_05_searchfloor_sync.py --range 1000-2000

  Параллельное скачивание (10 потоков):
    python run_05_searchfloor_sync.py --range 1-1000 --workers 10

  Тихий режим для cron:
    python run_05_searchfloor_sync.py --range 1-1000 --quiet

  Режим отладки:
    python run_05_searchfloor_sync.py --range 1-100 --debug --workers 2

ВАЖНО: После создания архивов запустите scanner_inventorizer.py
для добавления новых задач в очередь обработки.
        """,
    )

    # Основные режимы работы
    mode_group = parser.add_mutually_exclusive_group(required=False)
    mode_group.add_argument("--full-sync", action="store_true", help="Полная синхронизация всех книг с сайта")
    mode_group.add_argument(
        "--range",
        type=parse_range,
        metavar="START-END",
        help="Синхронизация конкретного диапазона ID (например: 1000-2000)",
    )

    # Настройки
    parser.add_argument(
        "--output-dir", type=Path, help="Директория для сохранения архивов (по умолчанию: zip_searchfloor из настроек)"
    )
    parser.add_argument(
        "--workers", type=int, default=5, help="Количество параллельных потоков для скачивания (по умолчанию: 5)"
    )
    parser.add_argument(
        "--batch-size", type=int, default=100, help="Размер пакета для промежуточного сохранения (по умолчанию: 100)"
    )
    parser.add_argument(
        "--delay",
        type=float,
        default=1.0,
        help="Начальная задержка между запросами в секундах (адаптивно изменяется при ошибках)",
    )
    parser.add_argument("--proxy", action="store_true", help="Использовать ротацию SOCKS прокси из tmp/proxy.txt")
    parser.add_argument(
        "--max-errors",
        type=int,
        default=5,
        help="Максимальное количество последовательных ошибок перед остановкой (по умолчанию: 5)",
    )

    # Логирование
    log_group = parser.add_mutually_exclusive_group()
    log_group.add_argument("--quiet", "-q", action="store_true", help="Тихий режим - только предупреждения и ошибки")
    log_group.add_argument("--debug", action="store_true", help="Режим отладки - подробное логирование")

    args = parser.parse_args()

    # Настраиваем логирование
    setup_logging(quiet=args.quiet, debug=args.debug)
    logger = logging.getLogger(__name__)

    try:
        # Создаем downloader
        downloader = SearchFloorDownloader(
            output_dir=args.output_dir, delay=args.delay, use_proxy=args.proxy, max_consecutive_errors=args.max_errors
        )

        # Выполняем синхронизацию
        if args.full_sync:
            if not args.quiet and not args.debug:
                print("🚀 Запуск полной синхронизации SearchFloor")
            else:
                logger.info("🚀 Запуск полной синхронизации SearchFloor")
            downloader.full_sync(max_workers=args.workers, batch_size=args.batch_size)
            if not args.quiet and not args.debug:
                print("✅ Полная синхронизация завершена")
            else:
                logger.info("✅ Полная синхронизация завершена")

        elif args.range:
            start_id, end_id = args.range
            if not args.quiet and not args.debug:
                print(f"🚀 Запуск синхронизации диапазона {start_id}-{end_id}")
            else:
                logger.info(f"🚀 Запуск синхронизации диапазона {start_id}-{end_id}")
            downloader.sync_range(start_id, end_id, max_workers=args.workers, batch_size=args.batch_size)
            if not args.quiet and not args.debug:
                print(f"✅ Синхронизация диапазона {start_id}-{end_id} завершена")
            else:
                logger.info(f"✅ Синхронизация диапазона {start_id}-{end_id} завершена")

        else:
            # Режим по умолчанию: синхронизация новых книг со страницы is_finished
            downloader.sync_new_books()

        # Убираем лишний подвал - scanner_inventorizer.py это часть pipeline_10

        return 0

    except StopIteration:
        logger.warning("⚠️ Синхронизация остановлена из-за превышения лимита ошибок")
        logger.info("💡 Прогресс сохранен. Запустите команду снова для продолжения.")
        return 2  # Специальный код возврата для остановки из-за ошибок
    except KeyboardInterrupt:
        logger.info("🛑 Синхронизация прервана пользователем")
        return 130
    except Exception as e:
        logger.error(f"💥 Критическая ошибка: {e}", exc_info=True)
        return 1


if __name__ == "__main__":
    exit(main())
