#!/bin/bash
# run_tests.sh - Скрипт для запуска всех тестов проекта
#
# Запускает:
# - Unit-тесты (tests/test_*.py)
# - Регрессионные тесты (tools/test_chapter_parsing_regression.py)
#
# Использование:
#   ./run_tests.sh
#
# Возвращает код 0 если все тесты прошли, 1 если есть ошибки.

set -e  # Остановка при первой ошибке

echo "🧪 Запуск всех тестов проекта..."
echo "=================================="

# Активируем виртуальное окружение если оно есть
if [ -f ".venv/bin/activate" ]; then
    echo "📦 Активация виртуального окружения..."
    source .venv/bin/activate
fi

# Счетчики
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Функция для запуска тестов
run_test_suite() {
    local test_name="$1"
    local test_path="$2"
    
    echo ""
    echo "🔍 Запуск: $test_name"
    echo "---"
    
    if python -m unittest "$test_path" -v; then
        echo "✅ $test_name - ПРОЙДЕН"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo "❌ $test_name - ПРОВАЛЕН"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
}

# Запуск unit-тестов
echo "📋 Unit-тесты:"
run_test_suite "Оптимизация BookValidator" "tests.test_book_validator_optimization"
run_test_suite "Наследование заголовков глав" "tests.test_chapter_title_inheritance"

# Запуск регрессионных тестов
echo ""
echo "📋 Регрессионные тесты:"
echo "---"
echo "🔍 Запуск: Регрессионное тестирование парсинга глав"

if python tools/test_chapter_parsing_regression.py; then
    echo "✅ Регрессионное тестирование парсинга глав - ПРОЙДЕН"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo "❌ Регрессионное тестирование парсинга глав - ПРОВАЛЕН"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

TOTAL_TESTS=$((TOTAL_TESTS + 1))

# Итоговая статистика
echo ""
echo "📊 ИТОГОВАЯ СТАТИСТИКА:"
echo "======================="
echo "Всего тестов: $TOTAL_TESTS"
echo "Пройдено: $PASSED_TESTS"
echo "Провалено: $FAILED_TESTS"

if [ $FAILED_TESTS -eq 0 ]; then
    echo ""
    echo "🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ УСПЕШНО!"
    exit 0
else
    echo ""
    echo "💥 ЕСТЬ ПРОВАЛЕННЫЕ ТЕСТЫ!"
    echo "Пожалуйста, исправьте ошибки перед коммитом."
    exit 1
fi
