"""Утилиты для генерации и декодирования UUID v7 с обработкой специальных случаев.

Данный модуль устраняет дублирование логики работы с book_id между различными
компонентами системы (BookProcessor, диагностические инструменты и др.).

ЗАВИСИМОСТИ:
- Требует пакет "uuid7==0.1.0" (pip install uuid7==0.1.0)
- НЕ использовать: uuid-v7, uuid6, uuid_v7 - они имеют другой API
"""

import hashlib
import logging
import uuid
from datetime import datetime, timedelta, timezone

# ВАЖНО: Использовать именно пакет "uuid7==0.1.0" (НЕ uuid-v7, НЕ uuid6)
# Этот пакет поддерживает параметр ns= для передачи времени в наносекундах
from uuid_extensions import uuid7


def generate_book_uuid7(best_date: datetime, book_signature: str) -> str:
    """Генерирует UUID v7 для книги с обработкой дат до 1970 года.

    UUID v7 по спецификации RFC 9562 использует 48-битный Unix timestamp в миллисекундах,
    который по определению не может быть отрицательным (до 1 января 1970 00:00:00 UTC).

    ПРОБЛЕМА: Многие книги имеют даты публикации до 1970 года (например, классическая
    литература), что делает невозможным прямое использование их дат для UUID v7.

    РЕШЕНИЕ: Для книг с датами до 1970 года создается "синтетическая дата" на основе
    криптографического хеша метаданных книги, размещенная в диапазоне 1970-1980 гг.

    ПРЕИМУЩЕСТВА ДАННОГО ПОДХОДА:
    1. Архитектурная консистентность - все книги используют UUID v7
    2. Извлекаемость даты - из UUID всегда можно получить timestamp
    3. Уникальность - разные книги получают разные синтетические даты
    4. Стабильность - одна книга всегда получает одну и ту же синтетическую дату
    5. Сохранение реальной даты - оригинальная дата сохраняется в метаданных

    Args:
        best_date: Лучшая дата книги из extract_best_date
        book_signature: Уникальная подпись книги (title + authors + source_id)

    Returns:
        Строковое представление UUID v7

    Example:
        >>> from datetime import datetime, timezone
        >>> date = datetime(1950, 1, 1, tzinfo=timezone.utc)  # До 1970
        >>> signature = "Война и мир_1_123456"
        >>> book_id = generate_book_uuid7(date, signature)
        >>> len(book_id)
        36
    """
    logger = logging.getLogger(__name__)

    if best_date.timestamp() < 0:
        # Создаем синтетическую дату на основе хеша подписи книги
        hash_value = int(
            hashlib.md5(book_signature.encode("utf-8"), usedforsecurity=False).hexdigest()[:8],
            16,
        )

        # Создаем дату в диапазоне 1970-1980 на основе хеша (10 лет после эпохи)
        epoch_start = datetime(1970, 1, 1, tzinfo=timezone.utc)
        offset_seconds = hash_value % (365 * 24 * 3600 * 10)  # 10 лет
        uuid_date = epoch_start + timedelta(seconds=offset_seconds)

        logger.debug(f"Дата книги {best_date} до эпохи Unix. Создана уникальная дата для UUID7: {uuid_date}")
    else:
        uuid_date = best_date

    # Преобразуем timestamp в наносекунды для UUIDv7
    timestamp_ns = int(uuid_date.timestamp() * 1e9)
    book_id = str(uuid7(ns=timestamp_ns))

    logger.debug(f"Сгенерирован ID книги (UUIDv7) на основе даты {uuid_date}: {book_id}")

    return book_id


def create_book_signature_for_processor(canonical_book) -> str:
    """Создает уникальную подпись книги для BookProcessor.

    Args:
        canonical_book: Каноническая модель книги

    Returns:
        Строка-подпись книги
    """
    return f"{canonical_book.title}_{len(canonical_book.authors)}_{canonical_book.source_id}"


def create_book_signature_for_diagnostic(canonical_book) -> str:
    """Создает уникальную подпись книги для диагностических инструментов.

    Args:
        canonical_book: Каноническая модель книги

    Returns:
        Строка-подпись книги
    """
    # Для диагностических инструментов не используем source_id (может отсутствовать)
    return f"{canonical_book.title}_{len(canonical_book.authors)}"


def decode_uuid7_timestamp(uuid_str: str) -> str | None:
    """Декодирует timestamp из UUID v7 обратно в дату.

    Согласно диаграмме uuid_extensions.uuid7:
    t1: 32 бита основной части Unix timestamp секунд (биты 127-96)
    t2/t3: unixts(4) | frac_secs(12) | ver(4) | frac_secs(12) (биты 95-64)

    Всего: 36 битов секунд + 24 бита долей секунд

    Args:
        uuid_str: Строковое представление UUID v7

    Returns:
        ISO строка с датой и временем или None при ошибке
    """
    try:
        u = uuid.UUID(uuid_str)

        # t1: основные 32 бита секунд (биты 127-96)
        t1_seconds = (u.int >> 96) & ((1 << 32) - 1)

        # t2/t3: следующие 32 бита (биты 95-64)
        t2_t3 = (u.int >> 64) & ((1 << 32) - 1)

        # Разбираем t2/t3: unixts(4) | frac_secs(12) | ver(4) | frac_secs(12)
        remaining_seconds = (t2_t3 >> 28) & 0xF  # 4 бита секунд (биты 31-28)
        frac_high = (t2_t3 >> 16) & 0xFFF  # 12 битов долей (биты 27-16)
        # version = (t2_t3 >> 12) & 0xF  # 4 бита версии (биты 15-12) - не используется
        frac_low = t2_t3 & 0xFFF  # 12 битов долей (биты 11-0)

        # Собираем полный 36-битный timestamp секунд
        unix_seconds = (t1_seconds << 4) | remaining_seconds

        # Собираем 24-битные доли секунд
        fractional_seconds = (frac_high << 12) | frac_low

        # Конвертируем доли секунд в десятичное значение (24 бита = 2^24 уровней)
        fractional_part = fractional_seconds / (1 << 24)

        # Создаем полный timestamp
        timestamp = unix_seconds + fractional_part

        # Преобразуем в datetime
        dt = datetime.fromtimestamp(timestamp, tz=timezone.utc)

        return dt.isoformat()

    except Exception:
        return None


def validate_uuid_format(uuid_str: str) -> bool:
    """Проверяет корректность формата UUID.

    Args:
        uuid_str: Строковое представление UUID

    Returns:
        True если UUID корректен, False в противном случае
    """
    try:
        uuid.UUID(uuid_str)
        return True
    except (ValueError, TypeError):
        return False


def extract_uuid_version(uuid_str: str) -> int | None:
    """Извлекает версию UUID.

    Args:
        uuid_str: Строковое представление UUID

    Returns:
        Номер версии UUID или None при ошибке
    """
    try:
        u = uuid.UUID(uuid_str)
        return u.version
    except Exception:
        return None
