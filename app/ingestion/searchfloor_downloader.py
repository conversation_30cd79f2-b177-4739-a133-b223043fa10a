# app/ingestion/searchfloor_downloader.py

"""
SearchFloor Downloader - компонент для скачивания книг с сайта searchfloor.org
и создания ZIP-архивов с правильными датами файлов.
вторая страница: &page=2

Архитектурные принципы:
- Минималистичный подход: просто создаем архивы
- HTTP Last-Modified для получения дат файлов
- Именование архивов по диапазонам ID (1000.zip, 2000.zip)
- Потоковая запись в ZIP без промежуточных файлов
- Очистка FB2 от изображений для экономии места
"""

import io
import itertools
import logging
import re
import time
import warnings
import zipfile
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime
from pathlib import Path
from typing import Optional

import redis
import requests
from bs4 import BeautifulSoup

from app import settings

logger = logging.getLogger(__name__)


class SearchFloorDownloader:
    """Компонент для скачивания книг с SearchFloor и создания архивов."""

    def __init__(
        self,
        output_dir: Optional[Path] = None,
        delay: float = 1.0,
        use_proxy: bool = False,
        max_consecutive_errors: int = 5,
    ):
        """
        Args:
            output_dir: Директория для сохранения архивов (по умолчанию zip_searchfloor)
            delay: Задержка между запросами в секундах
            use_proxy: Использовать ротацию прокси из tmp/proxy.txt
            max_consecutive_errors: Максимальное количество последовательных ошибок перед остановкой
        """
        self.output_dir = output_dir or self._get_searchfloor_dir()
        self.delay = delay
        self.use_proxy = use_proxy
        self.max_consecutive_errors = max_consecutive_errors
        self.proxy_list = self._load_proxy_list() if use_proxy else []
        self.proxy_cycle = itertools.cycle(self.proxy_list) if self.proxy_list else None
        self.session = self._create_session()
        self.base_url = "https://searchfloor.org"
        self.redis_client = redis.from_url(settings.REDIS_URL)

        # Счетчики ошибок для мониторинга
        self.consecutive_429_errors = 0
        self.consecutive_critical_errors = 0
        self.current_delay = delay
        self.last_request_time = 0.0

        # Создаем директорию если не существует
        self.output_dir.mkdir(parents=True, exist_ok=True)

        if use_proxy:
            logger.info(f"🔄 Загружено {len(self.proxy_list)} прокси для ротации")

    def _reset_error_counters(self):
        """Сбрасывает счетчики ошибок после успешного запроса."""
        self.consecutive_429_errors = 0
        self.consecutive_critical_errors = 0
        # Постепенно уменьшаем задержку при успешных запросах
        self.current_delay = max(self.delay, self.current_delay * 0.9)

    def _handle_429_error(self, book_id: int, proxy_info: str = "") -> bool:
        """
        Обрабатывает ошибку 429 Too Many Requests.

        Returns:
            True если можно продолжать, False если нужно остановиться
        """
        self.consecutive_429_errors += 1
        # Экспоненциально увеличиваем задержку
        self.current_delay = min(self.current_delay * 2, 60.0)  # Максимум 60 секунд

        if logger.isEnabledFor(logging.DEBUG):
            logger.warning(
                f"ID {book_id} - ⏳ 429 Too Many Requests{proxy_info} "
                f"(#{self.consecutive_429_errors}, delay={self.current_delay:.1f}s)"
            )
        else:
            current_time = datetime.now().strftime("%H:%M:%S")
            print(
                f"{current_time} ID {book_id} - ⏳ 429 Too Many Requests "
                f"(#{self.consecutive_429_errors}, delay={self.current_delay:.1f}s)"
            )

        if self.consecutive_429_errors >= self.max_consecutive_errors:
            if logger.isEnabledFor(logging.DEBUG):
                logger.error(
                    f"🛑 Достигнут лимит последовательных 429 ошибок ({self.max_consecutive_errors}). "
                    f"Остановка скрипта для предотвращения блокировки."
                )
            else:
                print(
                    f"🛑 Достигнут лимит последовательных 429 ошибок ({self.max_consecutive_errors}). "
                    f"Остановка скрипта для предотвращения блокировки."
                )
            return False

        # Применяем увеличенную задержку
        time.sleep(self.current_delay)
        return True

    def _handle_critical_error(self, book_id: int, error: Exception, proxy_info: str = "") -> bool:
        """
        Обрабатывает критические ошибки (сетевые, таймауты и т.д.).

        Returns:
            True если можно продолжать, False если нужно остановиться
        """
        self.consecutive_critical_errors += 1

        if logger.isEnabledFor(logging.DEBUG):
            logger.warning(
                f"ID {book_id} - 🌐 Critical error{proxy_info} (#{self.consecutive_critical_errors}): {error}"
            )
        else:
            current_time = datetime.now().strftime("%H:%M:%S")
            print(f"{current_time} ID {book_id} - 🌐 Critical error (#{self.consecutive_critical_errors}): {error}")

        if self.consecutive_critical_errors >= self.max_consecutive_errors:
            if logger.isEnabledFor(logging.DEBUG):
                logger.error(
                    f"🛑 Достигнут лимит последовательных критических ошибок ({self.max_consecutive_errors}). "
                    f"Остановка скрипта."
                )
            else:
                print(
                    f"🛑 Достигнут лимит последовательных критических ошибок ({self.max_consecutive_errors}). "
                    f"Остановка скрипта."
                )
            return False

        return True

    def _apply_rate_limiting(self):
        """Применяет задержку между запросами для соблюдения rate limiting."""
        if self.last_request_time > 0:
            elapsed = time.time() - self.last_request_time
            if elapsed < self.current_delay:
                sleep_time = self.current_delay - elapsed
                time.sleep(sleep_time)

        self.last_request_time = time.time()

    def _save_progress(self, operation_type: str, start_id: int, end_id: int, current_id: int):
        """Сохраняет прогресс операции в Redis для возможности восстановления."""
        progress_key = f"searchfloor:progress:{operation_type}:{start_id}:{end_id}"
        progress_data = {
            "start_id": start_id,
            "end_id": end_id,
            "current_id": current_id,
            "timestamp": time.time(),
            "operation_type": operation_type,
        }
        try:
            # Сохраняем на 24 часа
            self.redis_client.setex(progress_key, 24 * 3600, str(progress_data))
            logger.debug(f"💾 Прогресс сохранен: {operation_type} {start_id}-{end_id}, текущий: {current_id}")
        except Exception as e:
            logger.warning(f"Ошибка сохранения прогресса: {e}")

    def _load_progress(self, operation_type: str, start_id: int, end_id: int) -> Optional[int]:
        """Загружает сохраненный прогресс операции из Redis."""
        progress_key = f"searchfloor:progress:{operation_type}:{start_id}:{end_id}"
        try:
            progress_str = self.redis_client.get(progress_key)
            if progress_str:
                import ast

                progress_data = ast.literal_eval(progress_str.decode())
                current_id = progress_data.get("current_id")
                timestamp = progress_data.get("timestamp", 0)

                # Проверяем, что прогресс не слишком старый (максимум 24 часа)
                if time.time() - timestamp < 24 * 3600:
                    logger.info(
                        f"🔄 Найден сохраненный прогресс: {operation_type} {start_id}-{end_id}, "
                        f"продолжаем с ID {current_id}"
                    )
                    return current_id
                else:
                    # Удаляем устаревший прогресс
                    self.redis_client.delete(progress_key)

        except Exception as e:
            logger.warning(f"Ошибка загрузки прогресса: {e}")

        return None

    def _clear_progress(self, operation_type: str, start_id: int, end_id: int):
        """Очищает сохраненный прогресс после успешного завершения операции."""
        progress_key = f"searchfloor:progress:{operation_type}:{start_id}:{end_id}"
        try:
            self.redis_client.delete(progress_key)
            logger.debug(f"🗑️ Прогресс очищен: {operation_type} {start_id}-{end_id}")
        except Exception as e:
            logger.warning(f"Ошибка очистки прогресса: {e}")

    def _load_proxy_list(self) -> list[str]:
        """Загружает список прокси из tmp/proxy.txt."""
        proxy_file = Path("tmp/proxy.txt")

        if not proxy_file.exists():
            logger.warning(f"Файл прокси {proxy_file} не найден")
            return []

        try:
            with open(proxy_file, "r", encoding="utf-8") as f:
                proxies = [line.strip() for line in f if line.strip()]

            logger.info(f"📋 Загружено {len(proxies)} прокси из {proxy_file}")
            return proxies

        except Exception as e:
            logger.error(f"Ошибка загрузки прокси из {proxy_file}: {e}")
            return []

    def _get_next_proxy(self) -> Optional[str]:
        """Возвращает следующий прокси из ротации."""
        if self.proxy_cycle:
            return next(self.proxy_cycle)
        return None

    def _get_archive_name_for_id(self, book_id: int) -> str:
        """Возвращает имя архива для книги по ID (группировка по тысячам)."""
        import math

        archive_boundary = math.ceil(book_id / 1000) * 1000
        return f"{archive_boundary}.zip"

    def _get_searchfloor_dir(self) -> Path:
        """Находит директорию zip_searchfloor из настроек."""
        for source_dir in settings.SOURCE_DIRS:
            if source_dir.name == "zip_searchfloor":
                return source_dir
        # Fallback: создаем рядом с первой директорией
        return settings.SOURCE_DIRS[0].parent / "zip_searchfloor"

    def _create_session(self) -> requests.Session:
        """Создает HTTP-сессию с настройками защиты от блокировок."""
        session = requests.Session()

        # Ротация User-Agent'ов
        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (iPhone; CPU iPhone OS 17_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1",
            "Mozilla/5.0 (Linux; Android 13; SM-S908B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
            "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/119.0.6045.109 Mobile/15E148 Safari/604.1",
            "Mozilla/5.0 (Linux; Android 12; Redmi Note 11) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
            "Mozilla/5.0 (iPad; CPU OS 17_0_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1",
            "Mozilla/5.0 (Android 13; Mobile; rv:119.0) Gecko/119.0 Firefox/119.0",
            "Mozilla/5.0 (Linux; Android 13; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/23.0 Chrome/********* Mobile Safari/537.36",
            "Mozilla/5.0 (Linux; Android 14; Pixel 7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
            "Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) FxiOS/119.0 Mobile/15E148 Safari/605.1.15",
            "Mozilla/5.0 (Linux; Android 12; VOG-L29) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
            "Mozilla/5.0 (Linux; Android 10; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.105 Mobile Safari/537.36",
            "Mozilla/5.0 (iPhone; CPU iPhone OS 14_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0.3 Mobile/15E148 Safari/604.1",
            "Mozilla/5.0 (Android 11; Mobile; rv:95.0) Gecko/95.0 Firefox/95.0",
            "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/94.0.4606.76 Mobile/15E148 Safari/604.1",
            "Mozilla/5.0 (Linux; Android 10; ELE-L29) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Mobile Safari/537.36",
            "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-A515F) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/15.0 Chrome/90.0.4430.210 Mobile Safari/537.36",
            "Mozilla/5.0 (iPad; CPU OS 14_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1",
            "Mozilla/5.0 (Linux; Android 11; IN2013) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.164 Mobile Safari/537.36",
            "Mozilla/5.0 (iPhone; CPU iPhone OS 15_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) FxiOS/38.0 Mobile/15E148 Safari/605.1.15",
            "Mozilla/5.0 (Linux; Android 10; Redmi Note 9S) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36 OPR/58.2.2878.53403",
        ]

        # Сохраняем список User-Agent'ов как атрибут класса
        self.user_agents = user_agents

        # Базовые заголовки (User-Agent будет добавляться для каждого запроса)
        session.headers.update(
            {
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "Accept-Language": "ru-RU,ru;q=0.9,en;q=0.8",
                "Accept-Encoding": "gzip, deflate, br",
                "DNT": "1",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1",
            }
        )

        return session

    def _get_random_user_agent(self) -> str:
        """Возвращает случайный User-Agent для каждого запроса."""
        import random

        return random.choice(self.user_agents)

    def get_max_book_id(self) -> int:
        """Определяет максимальный ID книги с главной страницы."""
        url = f"{self.base_url}/?status=is_finished&page=1"

        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, "html.parser")

            # Ищем все кнопки с data-url="/book/{id}"
            buttons = soup.find_all("button", {"data-url": re.compile(r"/book/\d+")})

            max_id = 0
            for button in buttons:
                if hasattr(button, "get"):
                    data_url = button.get("data-url", "")
                    if isinstance(data_url, str):
                        match = re.search(r"/book/(\d+)", data_url)
                        if match:
                            book_id = int(match.group(1))
                            max_id = max(max_id, book_id)

            logger.info(f"Найден максимальный ID книги: {max_id}")
            return max_id

        except Exception as e:
            logger.error(f"Ошибка получения максимального ID: {e}")
            return 0

    def parse_finished_page(self) -> list[int]:
        """Парсит страницу is_finished и возвращает список ID книг."""
        url = f"{self.base_url}/?status=is_finished"

        try:
            # Отключаем сжатие и добавляем новый User-Agent для корректного парсинга
            headers = {"Accept-Encoding": "identity", "User-Agent": self._get_random_user_agent()}
            response = self.session.get(url, timeout=30, headers=headers)
            response.raise_for_status()

            # Принудительно устанавливаем кодировку
            if response.encoding is None or response.encoding == "ISO-8859-1":
                response.encoding = "utf-8"

            soup = BeautifulSoup(response.text, "html.parser")

            # Отладка: сохраняем страницу для анализа
            debug_file = Path("tmp/searchfloor_page.html")
            debug_file.parent.mkdir(exist_ok=True)
            with open(debug_file, "w", encoding="utf-8") as f:
                f.write(response.text)  # Используем .text вместо .content
            logger.debug(f"Страница сохранена в {debug_file}")

            # Отладка: проверяем что получили
            logger.debug(f"Размер ответа: {len(response.content)} байт")
            logger.debug(f"Content-Type: {response.headers.get('Content-Type')}")

            # Ищем все элементы .download-btn с data-url="/book/{id}"
            elements = soup.find_all(class_="download-btn", attrs={"data-url": re.compile(r"/book/\d+")})
            logger.debug(f"Найдено .download-btn элементов с data-url: {len(elements)}")

            # Дополнительная отладка
            all_download_btns = soup.find_all(class_="download-btn")
            logger.debug(f"Всего .download-btn элементов: {len(all_download_btns)}")

            all_data_urls = soup.find_all(attrs={"data-url": re.compile(r"/book/\d+")})
            logger.debug(f"Всего элементов с data-url=/book/: {len(all_data_urls)}")

            book_ids = []
            for element in elements:
                if hasattr(element, "get"):
                    data_url = element.get("data-url", "")
                    if isinstance(data_url, str):
                        match = re.search(r"/book/(\d+)", data_url)
                        if match:
                            book_id = int(match.group(1))
                            book_ids.append(book_id)

            if not logger.isEnabledFor(logging.DEBUG):
                print(f"📋 Найдено {len(book_ids)} книг на странице is_finished")
            return book_ids

        except Exception as e:
            logger.error(f"Ошибка парсинга страницы is_finished: {e}")
            return []

    def parse_finished_page_with_dates(self) -> list[tuple[int, datetime]]:
        """
        Парсит страницу is_finished и возвращает список (ID книги, дата обновления).

        Returns:
            Список кортежей (book_id, last_modified_date)
        """
        url = f"{self.base_url}/?status=is_finished"

        try:
            # Отключаем сжатие и добавляем новый User-Agent для корректного парсинга
            headers = {"Accept-Encoding": "identity", "User-Agent": self._get_random_user_agent()}
            response = self.session.get(url, timeout=30, headers=headers)
            response.raise_for_status()

            # Принудительно устанавливаем кодировку
            if response.encoding is None or response.encoding == "ISO-8859-1":
                response.encoding = "utf-8"

            soup = BeautifulSoup(response.text, "html.parser")

            # Отладка: сохраняем страницу для анализа
            debug_file = Path("tmp/searchfloor_page.html")
            debug_file.parent.mkdir(exist_ok=True)
            with open(debug_file, "w", encoding="utf-8") as f:
                f.write(response.text)
            logger.debug(f"Страница сохранена в {debug_file}")

            books_with_dates = []

            # Ищем все блоки книг по div с id="book{id}"
            book_divs = soup.find_all("div", id=re.compile(r"book\d+"))
            logger.debug(f"Найдено блоков книг: {len(book_divs)}")

            for book_div in book_divs:
                try:
                    # Проверяем что это Tag элемент
                    if not hasattr(book_div, "get") or not hasattr(book_div, "find"):
                        continue

                    # Извлекаем ID из атрибута id="book{id}"
                    div_id = book_div.get("id", "")
                    if not isinstance(div_id, str):
                        continue

                    book_id_match = re.search(r"book(\d+)", div_id)
                    if not book_id_match:
                        continue

                    book_id = int(book_id_match.group(1))

                    # Ищем элемент с датой: span.date с data-date
                    date_element = book_div.find("span", class_="date")
                    if date_element and hasattr(date_element, "get"):
                        date_str = date_element.get("data-date")
                        if isinstance(date_str, str):
                            try:
                                # Парсим дату в формате "2025-07-23 11:01"
                                book_date = datetime.strptime(date_str, "%Y-%m-%d %H:%M")
                                books_with_dates.append((book_id, book_date))
                                logger.debug(f"📅 ID {book_id}: {book_date.strftime('%Y-%m-%d %H:%M')}")
                            except ValueError as e:
                                logger.warning(f"📅 Не удалось парсить дату '{date_str}' для ID {book_id}: {e}")
                                # Fallback: добавляем без даты (будем использовать HEAD запрос)
                                books_with_dates.append((book_id, None))
                        else:
                            logger.debug(f"📅 Дата не найдена для ID {book_id}")
                            # Fallback: добавляем без даты
                            books_with_dates.append((book_id, None))
                    else:
                        logger.debug(f"📅 Элемент даты не найден для ID {book_id}")
                        # Fallback: добавляем без даты
                        books_with_dates.append((book_id, None))

                except Exception as e:
                    logger.warning(f"Ошибка обработки блока книги: {e}")
                    continue

            if not logger.isEnabledFor(logging.DEBUG):
                dates_found = sum(1 for _, date in books_with_dates if date is not None)
                print(f"📋 Найдено {len(books_with_dates)} книг, дат получено: {dates_found}")

            return books_with_dates

        except Exception as e:
            logger.error(f"Ошибка парсинга страницы is_finished с датами: {e}")
            return []

    def _get_book_metadata(self, book_id: int) -> Optional[datetime]:
        """
        Получает время модификации книги через HEAD запрос без скачивания.

        Args:
            book_id: ID книги

        Returns:
            datetime объект времени модификации или None при ошибке
        """
        url = f"{self.base_url}/book/{book_id}"

        try:
            # Используем те же заголовки что и для GET запросов
            headers = {
                "User-Agent": self._get_random_user_agent(),
                "Referer": "https://searchfloor.org/?status=is_finished",
            }

            response = self.session.head(url, timeout=10, headers=headers)

            if response.status_code == 200:
                last_modified_str = response.headers.get("Last-Modified")
                if last_modified_str:
                    return datetime.strptime(last_modified_str, "%a, %d %b %Y %H:%M:%S %Z")

            logger.debug(f"📅 Не удалось получить время для ID {book_id}: статус {response.status_code}")

        except Exception as e:
            logger.debug(f"📅 Ошибка получения метаданных для ID {book_id}: {e}")

        return None

    def _is_book_recently_downloaded(self, book_id: int) -> bool:
        """
        Проверяет, был ли файл недавно скачан (кэш в Redis на 72 часа).

        Args:
            book_id: ID книги

        Returns:
            True если книга была скачана в последние 72 часа
        """
        cache_key = f"searchfloor:downloaded:{book_id}"
        try:
            return self.redis_client.exists(cache_key) > 0
        except Exception as e:
            logger.warning(f"Ошибка проверки кэша для ID {book_id}: {e}")
            return False

    def _mark_book_as_downloaded(self, book_id: int) -> None:
        """
        Отмечает книгу как скачанную в кэше Redis на 72 часа.

        Args:
            book_id: ID книги
        """
        cache_key = f"searchfloor:downloaded:{book_id}"
        try:
            # TTL = 72 часа = 72 * 3600 секунд
            self.redis_client.setex(cache_key, 72 * 3600, "1")
            logger.debug(f"📝 Книга ID {book_id} отмечена как скачанная (TTL: 72ч)")
        except Exception as e:
            logger.warning(f"Ошибка сохранения в кэш для ID {book_id}: {e}")

    def _filter_books_with_dates(self, books_with_dates: list[tuple[int, Optional[datetime]]]) -> list[int]:
        """
        Фильтрует книги с учетом дат со страницы и кэша Redis.
        Возвращает только те книги, которые нужно скачать.

        Args:
            books_with_dates: Список кортежей (book_id, server_date)

        Returns:
            Список ID книг, которые нужно скачать
        """
        books_to_download = []

        # Группируем книги по архивам для эффективной проверки
        books_by_archive: dict[str, list[tuple[int, Optional[datetime]]]] = {}
        for book_id, server_date in books_with_dates:
            archive_name = self._get_archive_name_for_id(book_id)
            if archive_name not in books_by_archive:
                books_by_archive[archive_name] = []
            books_by_archive[archive_name].append((book_id, server_date))

        # Проверяем каждый архив
        for archive_name, archive_books in books_by_archive.items():
            archive_path = self.output_dir / archive_name

            # Читаем существующие файлы с их временами модификации
            existing_files_info = {}
            if archive_path.exists():
                try:
                    with zipfile.ZipFile(str(archive_path), "r") as zip_file:
                        for file_info in zip_file.infolist():
                            if not file_info.is_dir():
                                file_time = datetime(*file_info.date_time)
                                existing_files_info[file_info.filename] = file_time
                except Exception as e:
                    logger.warning(f"📦 Ошибка чтения архива {archive_path}: {e}")
                    # При ошибке считаем все файлы новыми
                    existing_files_info = {}

            # Проверяем каждую книгу
            for book_id, server_date in archive_books:
                filename = f"{book_id}.fb2"

                # 1. Проверяем кэш Redis - если недавно скачивали, пропускаем
                if self._is_book_recently_downloaded(book_id):
                    logger.debug(f"⏭️ {filename} - недавно скачивался (кэш Redis)")
                    continue

                # 2. Проверяем наличие файла в архиве
                if filename not in existing_files_info:
                    # Файла нет - нужно скачать
                    books_to_download.append(book_id)
                    logger.debug(f"📦 {filename} не найден - добавляем для скачивания")
                    continue

                # 3. Файл есть - сравниваем даты
                existing_time = existing_files_info[filename]

                if server_date is not None:
                    # Используем дату со страницы
                    if server_date > existing_time:
                        # Серверная версия новее - нужно обновить
                        books_to_download.append(book_id)
                        logger.debug(
                            f"🔄 {filename} - сервер новее ({existing_time.strftime('%Y-%m-%d %H:%M')} → {server_date.strftime('%Y-%m-%d %H:%M')})"
                        )
                    else:
                        # Локальная версия актуальна - пропускаем
                        logger.debug(
                            f"⏭️ {filename} - локальная версия актуальна ({existing_time.strftime('%Y-%m-%d %H:%M')} >= {server_date.strftime('%Y-%m-%d %H:%M')})"
                        )
                else:
                    # Fallback: дата не получена со страницы - используем HEAD запрос
                    server_time = self._get_book_metadata(book_id)
                    if server_time is None:
                        # Не удалось получить время - скачиваем на всякий случай
                        books_to_download.append(book_id)
                        logger.debug(f"📦 {filename} - не удалось получить время сервера, скачиваем")
                    else:
                        if server_time > existing_time:
                            # Серверная версия новее - нужно обновить
                            books_to_download.append(book_id)
                            logger.debug(
                                f"🔄 {filename} - сервер новее (HEAD: {existing_time.strftime('%Y-%m-%d %H:%M')} → {server_time.strftime('%Y-%m-%d %H:%M')})"
                            )
                        else:
                            # Локальная версия актуальна - пропускаем
                            logger.debug(
                                f"⏭️ {filename} - локальная версия актуальна (HEAD: {existing_time.strftime('%Y-%m-%d %H:%M')} >= {server_time.strftime('%Y-%m-%d %H:%M')})"
                            )

        if not logger.isEnabledFor(logging.DEBUG):
            print(f"📊 Всего книг: {len(books_with_dates)}, новых/обновленных: {len(books_to_download)}")

        return books_to_download

    def _filter_books_with_time_check(self, book_ids: list[int]) -> list[int]:
        """
        Фильтрует книги, проверяя их время модификации через HEAD запросы.
        Возвращает только те книги, которые нужно скачать (новые или обновленные).

        DEPRECATED: Используйте _filter_books_with_dates() для избежания двойных запросов.

        Args:
            book_ids: Список ID книг для проверки

        Returns:
            Список ID книг, которые нужно скачать
        """
        books_to_download = []

        # Группируем книги по архивам для эффективной проверки
        books_by_archive: dict[str, list[int]] = {}
        for book_id in book_ids:
            archive_name = self._get_archive_name_for_id(book_id)
            if archive_name not in books_by_archive:
                books_by_archive[archive_name] = []
            books_by_archive[archive_name].append(book_id)

        # Проверяем каждый архив
        for archive_name, archive_book_ids in books_by_archive.items():
            archive_path = self.output_dir / archive_name

            # Читаем существующие файлы с их временами модификации
            existing_files_info = {}
            if archive_path.exists():
                try:
                    with zipfile.ZipFile(str(archive_path), "r") as zip_file:
                        for file_info in zip_file.infolist():
                            if not file_info.is_dir():
                                file_time = datetime(*file_info.date_time)
                                existing_files_info[file_info.filename] = file_time
                except Exception as e:
                    logger.warning(f"📦 Ошибка чтения архива {archive_path}: {e}")
                    # При ошибке считаем все файлы новыми
                    existing_files_info = {}

            # Проверяем каждую книгу
            for book_id in archive_book_ids:
                filename = f"{book_id}.fb2"

                # 1. Проверяем кэш Redis - если недавно скачивали, пропускаем
                if self._is_book_recently_downloaded(book_id):
                    logger.debug(f"⏭️ {filename} - недавно скачивался (кэш Redis)")
                    continue

                if filename not in existing_files_info:
                    # Файла нет - нужно скачать
                    books_to_download.append(book_id)
                    logger.debug(f"📦 {filename} не найден - добавляем для скачивания")
                else:
                    # Файл есть - проверяем время через HEAD запрос
                    server_time = self._get_book_metadata(book_id)
                    if server_time is None:
                        # Не удалось получить время - скачиваем на всякий случай
                        books_to_download.append(book_id)
                        logger.debug(f"📦 {filename} - не удалось получить время сервера, скачиваем")
                    else:
                        existing_time = existing_files_info[filename]
                        if server_time > existing_time:
                            # Серверная версия новее - нужно обновить
                            books_to_download.append(book_id)
                            logger.debug(
                                f"🔄 {filename} - сервер новее ({existing_time.strftime('%Y-%m-%d %H:%M')} → {server_time.strftime('%Y-%m-%d %H:%M')})"
                            )
                        else:
                            # Локальная версия актуальна - пропускаем
                            logger.debug(
                                f"⏭️ {filename} - локальная версия актуальна ({existing_time.strftime('%Y-%m-%d %H:%M')} >= {server_time.strftime('%Y-%m-%d %H:%M')})"
                            )

        if not logger.isEnabledFor(logging.DEBUG):
            print(f"📊 Всего книг: {len(book_ids)}, новых/обновленных: {len(books_to_download)}")

        return books_to_download

    def download_book(self, book_id: int, max_proxy_retries: int = 3) -> Optional[tuple[bytes, datetime]]:
        """
        Скачивает книгу по ID и возвращает содержимое с датой модификации.

        Args:
            book_id: ID книги для скачивания
            max_proxy_retries: Максимальное количество попыток с разными прокси

        Returns:
            Tuple[bytes, datetime] или None если книга недоступна
            Может вызвать StopIteration при достижении лимита ошибок
        """
        url = f"{self.base_url}/book/{book_id}"

        # Применяем rate limiting перед запросом
        self._apply_rate_limiting()

        # Пробуем с разными прокси
        for attempt in range(max_proxy_retries):
            # Настраиваем прокси для этого запроса
            proxies = {}
            proxy_info = ""

            if self.use_proxy:
                proxy = self._get_next_proxy()
                if proxy:
                    proxies = {"http": f"socks5://{proxy}", "https": f"socks5://{proxy}"}
                    proxy_info = f" via {proxy}"

            try:
                # Добавляем новый User-Agent и Referer для каждого запроса
                headers = {
                    "User-Agent": self._get_random_user_agent(),
                    "Referer": "https://searchfloor.org/?status=is_finished",
                }

                # Таймаут 10 секунд для прокси
                timeout = 10 if self.use_proxy else 30
                response = self.session.get(url, timeout=timeout, proxies=proxies, headers=headers)

                if response.status_code == 404:
                    logger.debug(f"ID {book_id} - 🚫 404 Not Found{proxy_info}")
                    return None
                elif response.status_code == 401:
                    if logger.isEnabledFor(logging.DEBUG):
                        logger.info(f"ID {book_id} - 🔒 401 Unauthorized{proxy_info}")
                    else:
                        current_time = datetime.now().strftime("%H:%M:%S")
                        print(f"{current_time} ID {book_id} - 🔒 401 Unauthorized")
                    return None
                elif response.status_code == 429:
                    # Специальная обработка 429 Too Many Requests
                    if not self._handle_429_error(book_id, proxy_info):
                        raise StopIteration("Достигнут лимит 429 ошибок") from None
                    continue  # Повторяем попытку с задержкой

                response.raise_for_status()

                # Извлекаем дату модификации из заголовков
                last_modified_str = response.headers.get("Last-Modified")
                if last_modified_str:
                    # Парсим дату в формате RFC 2822
                    last_modified = datetime.strptime(last_modified_str, "%a, %d %b %Y %H:%M:%S %Z")
                else:
                    # Fallback на текущее время
                    last_modified = datetime.now()

                # Определяем формат контента по Content-Type
                content_type = response.headers.get("Content-Type", "")

                if "application/zip" in content_type:
                    # ZIP-архив - извлекаем FB2
                    content = self._extract_fb2_from_zip(response.content)
                    if content is None:
                        logger.warning(f"ID {book_id} - 📦 не удалось извлечь FB2 из ZIP{proxy_info}")
                        return None
                elif "application/x-fictionbook+xml" in content_type:
                    # Сырой FB2
                    content = response.content
                else:
                    logger.warning(f"ID {book_id} - ❓ неизвестный Content-Type: {content_type}{proxy_info}")
                    return None

                # Очищаем FB2 от изображений
                content = self._clean_fb2_content(content)

                # Успешный запрос - сбрасываем счетчики ошибок
                self._reset_error_counters()

                # Отмечаем книгу как скачанную в кэше Redis
                self._mark_book_as_downloaded(book_id)

                size_kb = len(content) / 1024
                if logger.isEnabledFor(logging.DEBUG):
                    logger.info(
                        f"ID {book_id} - OK ({size_kb:.0f} KB, {last_modified.strftime('%Y-%m-%d %H:%M')}){proxy_info}"
                    )
                else:
                    # Простой вывод: время ID результат
                    current_time = datetime.now().strftime("%H:%M:%S")
                    print(f"{current_time} ID {book_id} - ✅ OK ({size_kb:.0f} KB)")
                return content, last_modified

            except (
                requests.exceptions.Timeout,
                requests.exceptions.ProxyError,
                requests.exceptions.ConnectionError,
            ) as e:
                # Ошибки прокси/таймаута - пробуем следующий прокси
                if attempt < max_proxy_retries - 1 and self.use_proxy:
                    logger.debug(f"ID {book_id} - ⚡ Proxy timeout/error{proxy_info}, trying next proxy...")
                    continue
                else:
                    # Проверяем лимит критических ошибок
                    if not self._handle_critical_error(book_id, e, proxy_info):
                        raise StopIteration("Достигнут лимит критических ошибок") from e
                    return None

            except requests.exceptions.RequestException as e:
                # Другие HTTP ошибки - проверяем лимит
                if not self._handle_critical_error(book_id, e, proxy_info):
                    raise StopIteration("Достигнут лимит критических ошибок") from e
                return None

            except Exception as e:
                logger.error(f"ID {book_id} - 💥 CRITICAL: {e}{proxy_info}")
                if not self._handle_critical_error(book_id, e, proxy_info):
                    raise StopIteration("Достигнут лимит критических ошибок") from e
                return None

        # Если дошли сюда - все попытки неудачны
        logger.warning(f"ID {book_id} - ❌ все попытки с прокси неудачны")
        return None

    def _extract_fb2_from_zip(self, zip_content: bytes) -> Optional[bytes]:
        """Извлекает FB2 файл из ZIP-архива."""
        try:
            with zipfile.ZipFile(io.BytesIO(zip_content)) as zip_file:
                # Ищем FB2 файл внутри архива
                for filename in zip_file.namelist():
                    if filename.endswith(".fb2"):
                        return zip_file.read(filename)

                logger.warning("📦 FB2 файл не найден в ZIP-архиве")
                return None

        except Exception as e:
            logger.warning(f"📦 Ошибка извлечения FB2 из ZIP: {e}")
            return None

    def _clean_fb2_content(self, content: bytes) -> bytes:
        """Удаляет изображения из FB2 для экономии места."""
        try:
            # Простая очистка через регулярные выражения
            text = content.decode("utf-8", errors="ignore")

            # Удаляем binary теги с изображениями
            text = re.sub(r"<binary[^>]*>.*?</binary>", "", text, flags=re.DOTALL | re.IGNORECASE)

            # Удаляем image теги
            text = re.sub(r"<image[^>]*/?>", "", text, flags=re.IGNORECASE)

            return text.encode("utf-8")

        except Exception as e:
            logger.warning(f"🧹 Ошибка очистки FB2: {e}, возвращаем оригинал")
            return content

    def download_books_parallel(self, book_ids: list[int], max_workers: int = 5) -> list[tuple[int, bytes, datetime]]:
        """
        Параллельно скачивает список книг.

        Args:
            book_ids: Список ID книг для скачивания
            max_workers: Количество параллельных потоков

        Returns:
            Список успешно скачанных книг (book_id, content, date)

        Raises:
            StopIteration: При достижении лимита ошибок
        """
        successful_downloads = []

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Запускаем все задачи
            future_to_id = {executor.submit(self.download_book, book_id): book_id for book_id in book_ids}

            # Собираем результаты по мере завершения
            for future in as_completed(future_to_id):
                book_id = future_to_id[future]
                try:
                    result = future.result()
                    if result is not None:
                        content, last_modified = result
                        successful_downloads.append((book_id, content, last_modified))
                except StopIteration as e:
                    # Достигнут лимит ошибок - прерываем обработку
                    logger.error(f"🛑 Остановка параллельного скачивания: {e}")
                    # Отменяем оставшиеся задачи
                    for remaining_future in future_to_id:
                        remaining_future.cancel()
                    raise
                except Exception as e:
                    logger.error(f"ID {book_id} - 💥 CRITICAL: {e}")

        return successful_downloads

    def _append_to_archive(self, archive_path: Path, books_data: list[tuple[int, bytes, datetime]]) -> None:
        """
        Добавляет книги в существующий архив или создает новый.
        Заменяет существующие файлы если новые файлы моложе.

        Args:
            archive_path: Путь к архиву
            books_data: Список (book_id, content, last_modified)
        """
        # Подавляем warnings от zipfile о дублирующихся именах файлов
        with warnings.catch_warnings():
            warnings.filterwarnings("ignore", category=UserWarning, module="zipfile")

            if archive_path.exists():
                # Читаем существующие файлы с их временами модификации
                existing_files_info = {}
                files_to_replace = []
                files_to_add = []

                try:
                    with zipfile.ZipFile(str(archive_path), "r") as zip_file:
                        for file_info in zip_file.infolist():
                            if not file_info.is_dir():
                                # Конвертируем время из tuple в datetime для сравнения
                                file_time = datetime(*file_info.date_time)
                                existing_files_info[file_info.filename] = file_time
                except Exception as e:
                    logger.warning(f"📦 Ошибка чтения существующего архива {archive_path}: {e}")
                    # Если не можем прочитать архив, пересоздаем его
                    existing_files_info = {}

                # Анализируем какие файлы нужно добавить/заменить
                for book_id, content, last_modified in books_data:
                    filename = f"{book_id}.fb2"

                    if filename not in existing_files_info:
                        # Новый файл - добавляем
                        files_to_add.append((book_id, content, last_modified))
                        logger.debug(f"📦 Добавляем новый файл {filename}")
                    else:
                        # Файл существует - сравниваем время
                        existing_time = existing_files_info[filename]
                        if last_modified > existing_time:
                            # Новый файл моложе - заменяем, НО сохраняем оригинальную дату
                            files_to_replace.append((book_id, content, existing_time))  # Используем existing_time!
                            logger.debug(
                                f"🔄 Заменяем {filename}: обновляем содержимое, сохраняем дату {existing_time.strftime('%Y-%m-%d %H:%M')} (сервер: {last_modified.strftime('%Y-%m-%d %H:%M')})"
                            )
                        else:
                            # Существующий файл новее или равен - пропускаем
                            logger.debug(
                                f"⏭️ Пропускаем {filename}: существующий файл новее ({existing_time.strftime('%Y-%m-%d %H:%M')} >= {last_modified.strftime('%Y-%m-%d %H:%M')})"
                            )

                # Если есть файлы для замены, нужно пересоздать архив
                if files_to_replace:
                    self._recreate_archive_with_replacements(archive_path, files_to_replace, files_to_add)
                elif files_to_add:
                    # Только новые файлы - можем просто добавить
                    with zipfile.ZipFile(str(archive_path), "a", compression=zipfile.ZIP_DEFLATED) as zip_file:
                        for book_id, content, last_modified in files_to_add:
                            zip_info = zipfile.ZipInfo(filename=f"{book_id}.fb2")
                            zip_info.date_time = last_modified.timetuple()[:6]
                            zip_file.writestr(zip_info, content)
            else:
                # Создаем новый архив
                with zipfile.ZipFile(str(archive_path), "w", compression=zipfile.ZIP_DEFLATED) as zip_file:
                    for book_id, content, last_modified in books_data:
                        zip_info = zipfile.ZipInfo(filename=f"{book_id}.fb2")
                        zip_info.date_time = last_modified.timetuple()[:6]
                        zip_file.writestr(zip_info, content)

    def _recreate_archive_with_replacements(
        self,
        archive_path: Path,
        files_to_replace: list[tuple[int, bytes, datetime]],
        files_to_add: list[tuple[int, bytes, datetime]],
    ) -> None:
        """
        Пересоздает архив с заменой файлов.

        Args:
            archive_path: Путь к архиву
            files_to_replace: Список файлов для замены
            files_to_add: Список новых файлов для добавления
        """
        # Создаем множество имен файлов для замены
        replace_filenames = {f"{book_id}.fb2" for book_id, _, _ in files_to_replace}

        # Создаем временный архив
        temp_archive = archive_path.with_suffix(".tmp")

        try:
            with zipfile.ZipFile(str(temp_archive), "w", compression=zipfile.ZIP_DEFLATED) as new_zip:
                # Копируем существующие файлы (кроме тех что заменяем)
                try:
                    with zipfile.ZipFile(str(archive_path), "r") as old_zip:
                        for file_info in old_zip.infolist():
                            if not file_info.is_dir() and file_info.filename not in replace_filenames:
                                # Копируем файл как есть
                                file_data = old_zip.read(file_info.filename)
                                new_zip.writestr(file_info, file_data)
                except Exception as e:
                    logger.warning(f"📦 Ошибка копирования из старого архива: {e}")

                # Добавляем файлы для замены
                for book_id, content, last_modified in files_to_replace:
                    zip_info = zipfile.ZipInfo(filename=f"{book_id}.fb2")
                    zip_info.date_time = last_modified.timetuple()[:6]
                    new_zip.writestr(zip_info, content)

                # Добавляем новые файлы
                for book_id, content, last_modified in files_to_add:
                    zip_info = zipfile.ZipInfo(filename=f"{book_id}.fb2")
                    zip_info.date_time = last_modified.timetuple()[:6]
                    new_zip.writestr(zip_info, content)

            # Атомарно заменяем старый архив новым
            temp_archive.replace(archive_path)
            logger.debug(f"📦 Архив пересоздан с {len(files_to_replace)} заменами и {len(files_to_add)} новыми файлами")

        except Exception as e:
            # Очищаем временный файл при ошибке
            if temp_archive.exists():
                temp_archive.unlink()
            raise e

    def create_archive(self, start_id: int, end_id: int, max_workers: int = 5, batch_size: int = 100) -> Path:
        """
        Создает ZIP-архив с книгами в указанном диапазоне ID с промежуточными сохранениями.

        Args:
            start_id: Начальный ID (включительно)
            end_id: Конечный ID (включительно)
            max_workers: Количество параллельных потоков для скачивания
            batch_size: Размер пакета для промежуточного сохранения

        Returns:
            Path к созданному архиву
        """
        # Используем правильную логику именования архивов (тысячные границы)
        archive_name = self._get_archive_name_for_id(end_id)
        archive_path = self.output_dir / archive_name

        # НЕ удаляем архив - работаем в режиме добавления/обновления

        if logger.isEnabledFor(logging.DEBUG):
            logger.info(f"🚀 Начинаем создание архива {archive_name} для ID {start_id}-{end_id}")
        else:
            print(f"🚀 Начинаем создание архива {archive_name} для ID {start_id}-{end_id}")

        # Генерируем список ID для скачивания
        book_ids = list(range(start_id, end_id + 1))
        total_books = len(book_ids)
        total_saved = 0

        start_time = time.time()

        # Обрабатываем пакетами
        for i in range(0, len(book_ids), batch_size):
            batch_ids = book_ids[i : i + batch_size]
            batch_num = i // batch_size + 1
            total_batches = (len(book_ids) + batch_size - 1) // batch_size

            if logger.isEnabledFor(logging.DEBUG):
                logger.info(
                    f"📦 Пакет {batch_num}/{total_batches}: скачиваем ID {batch_ids[0]}-{batch_ids[-1]} "
                    f"({len(batch_ids)} книг, {max_workers} потоков)"
                )
            else:
                print(
                    f"📦 Пакет {batch_num}/{total_batches}: скачиваем ID {batch_ids[0]}-{batch_ids[-1]} "
                    f"({len(batch_ids)} книг, {max_workers} потоков)"
                )

            # Скачиваем пакет параллельно
            batch_start = time.time()
            try:
                successful_downloads = self.download_books_parallel(batch_ids, max_workers)
                download_time = time.time() - batch_start

                if successful_downloads:
                    # Сохраняем пакет в архив
                    save_start = time.time()
                    self._append_to_archive(archive_path, successful_downloads)
                    save_time = time.time() - save_start

                    total_saved += len(successful_downloads)
                    success_rate = len(successful_downloads) / len(batch_ids) * 100

                    if logger.isEnabledFor(logging.DEBUG):
                        logger.info(
                            f"✅ Пакет {batch_num}: {len(successful_downloads)}/{len(batch_ids)} книг "
                            f"({success_rate:.1f}%) за {download_time:.1f}с + {save_time:.1f}с сохранение\n"
                        )
                    else:
                        print(
                            f"✅ Пакет {batch_num}: {len(successful_downloads)}/{len(batch_ids)} книг "
                            f"({success_rate:.1f}%) за {download_time:.1f}с + {save_time:.1f}с сохранение\n"
                        )
                else:
                    if logger.isEnabledFor(logging.DEBUG):
                        logger.warning(f"❌ Пакет {batch_num}: не удалось скачать ни одной книги")
                    else:
                        print(f"❌ Пакет {batch_num}: не удалось скачать ни одной книги")

            except StopIteration as e:
                # Достигнут лимит ошибок - прерываем создание архива
                if logger.isEnabledFor(logging.DEBUG):
                    logger.error(f"🛑 Создание архива прервано: {e}")
                else:
                    print(f"🛑 Создание архива прервано: {e}")
                break

        total_time = time.time() - start_time
        overall_success_rate = total_saved / total_books * 100

        if logger.isEnabledFor(logging.DEBUG):
            logger.info(
                f"🎉 Архив {archive_name} создан: {total_saved}/{total_books} книг "
                f"({overall_success_rate:.1f}%) за {total_time:.1f} сек\n"
            )
        else:
            print(
                f"🎉 Архив {archive_name} создан: {total_saved}/{total_books} книг "
                f"({overall_success_rate:.1f}%) за {total_time:.1f} сек\n\n"
            )

        return archive_path

    def sync_range(self, start_id: int, end_id: int, max_workers: int = 5, batch_size: int = 100) -> None:
        """
        Синхронизирует диапазон ID, создавая архивы по 1000 книг.

        Args:
            start_id: Начальный ID
            end_id: Конечный ID
            max_workers: Количество параллельных потоков
        """
        if logger.isEnabledFor(logging.DEBUG):
            logger.info(f"🚀 Начинаем синхронизацию диапазона {start_id}-{end_id}")
        else:
            print(f"🚀 Начинаем синхронизацию диапазона {start_id}-{end_id}")

        # Проверяем, есть ли сохраненный прогресс
        saved_current_id = self._load_progress("sync_range", start_id, end_id)
        current_id = saved_current_id if saved_current_id is not None else start_id

        if saved_current_id is not None:
            if logger.isEnabledFor(logging.DEBUG):
                logger.info(f"🔄 Продолжаем с сохраненного прогресса: ID {current_id}")
            else:
                print(f"🔄 Продолжаем с сохраненного прогресса: ID {current_id}")

        archive_count = 0

        try:
            while current_id <= end_id:
                # Сохраняем прогресс перед обработкой каждого архива
                self._save_progress("sync_range", start_id, end_id, current_id)

                # Определяем границы текущего архива (по 1000 книг)
                archive_end = min(current_id + 999, end_id)
                archive_boundary = ((current_id - 1) // 1000 + 1) * 1000
                actual_end = min(archive_boundary, archive_end)

                archive_count += 1
                if logger.isEnabledFor(logging.DEBUG):
                    logger.info(f"📦 Архив {archive_count}: обрабатываем ID {current_id}-{actual_end}")
                else:
                    print(f"📦 Архив {archive_count}: обрабатываем ID {current_id}-{actual_end}")

                # Создаем архив с параллельным скачиванием и пакетным сохранением
                self.create_archive(current_id, actual_end, max_workers, batch_size)

                current_id = archive_boundary + 1

            # Успешное завершение - очищаем прогресс
            self._clear_progress("sync_range", start_id, end_id)

        except StopIteration as e:
            # Остановка из-за лимита ошибок - прогресс уже сохранен
            if logger.isEnabledFor(logging.DEBUG):
                logger.error(f"🛑 Синхронизация диапазона прервана: {e}")
                logger.info("💾 Прогресс сохранен. Для продолжения запустите команду снова.")
            else:
                print(f"🛑 Синхронизация диапазона прервана: {e}")
                print("💾 Прогресс сохранен. Для продолжения запустите команду снова.")
            raise

        if logger.isEnabledFor(logging.DEBUG):
            logger.info(f"🎉 Синхронизация диапазона {start_id}-{end_id} завершена (создано архивов: {archive_count})")
        else:
            print(f"🎉 Синхронизация диапазона {start_id}-{end_id} завершена (создано архивов: {archive_count})")

    def full_sync(self, max_workers: int = 5, batch_size: int = 100) -> None:
        """
        Выполняет полную синхронизацию всех книг с сайта.

        Args:
            max_workers: Количество параллельных потоков
        """
        if logger.isEnabledFor(logging.DEBUG):
            logger.info("🚀 Начинаем полную синхронизацию SearchFloor")
        else:
            print("🚀 Начинаем полную синхронизацию SearchFloor")

        max_id = self.get_max_book_id()
        self.sync_range(1, max_id, max_workers, batch_size)

        if logger.isEnabledFor(logging.DEBUG):
            logger.info("🎉 Полная синхронизация завершена")
        else:
            print("🎉 Полная синхронизация завершена")

    def sync_new_books(self):
        """Синхронизирует новые книги со страницы is_finished БЕЗ прокси.

        Проверяет наличие книг в архивах и скачивает только отсутствующие.
        """
        # Принудительно отключаем прокси для новых книг
        old_proxy_setting = self.use_proxy
        self.use_proxy = False

        try:
            # Парсим страницу is_finished с получением дат
            books_with_dates = self.parse_finished_page_with_dates()
            if not books_with_dates:
                if logger.isEnabledFor(logging.DEBUG):
                    logger.info("📭 На странице is_finished книг не найдено")
                else:
                    print("📭 На странице is_finished книг не найдено")
                return

            # Фильтруем книги с учетом дат со страницы и кэша Redis
            books_to_download = self._filter_books_with_dates(books_with_dates)
            if not books_to_download:
                if not logger.isEnabledFor(logging.DEBUG):
                    print("✅ Все книги со страницы актуальны")
                return

            # Убираем лишнее логирование - только результаты скачивания

            # Группируем книги по архивам
            books_by_archive: dict[str, list[int]] = {}
            for book_id in books_to_download:
                archive_name = self._get_archive_name_for_id(book_id)
                if archive_name not in books_by_archive:
                    books_by_archive[archive_name] = []
                books_by_archive[archive_name].append(book_id)

            # Скачиваем и добавляем в соответствующие архивы
            total_downloaded = 0
            try:
                for archive_name, book_ids_for_archive in books_by_archive.items():
                    # Скачиваем книги
                    successful_downloads = self.download_books_parallel(book_ids_for_archive, max_workers=1)

                    if successful_downloads:
                        # Добавляем в архив
                        archive_path = self.output_dir / archive_name
                        self._append_to_archive(archive_path, successful_downloads)

                        total_downloaded += len(successful_downloads)

            except StopIteration as e:
                # Достигнут лимит ошибок - прерываем синхронизацию
                if logger.isEnabledFor(logging.DEBUG):
                    logger.error(f"🛑 Синхронизация новых книг прервана: {e}")
                    if total_downloaded > 0:
                        logger.info(f"📊 Успешно скачано до остановки: {total_downloaded} книг")
                else:
                    print(f"🛑 Синхронизация новых книг прервана: {e}")
                    if total_downloaded > 0:
                        print(f"📊 Успешно скачано до остановки: {total_downloaded} книг")

            if not logger.isEnabledFor(logging.DEBUG) and total_downloaded > 0:
                print(f"🎉 Синхронизация завершена: скачано {total_downloaded} новых книг")

        finally:
            # Восстанавливаем настройку прокси
            self.use_proxy = old_proxy_setting
