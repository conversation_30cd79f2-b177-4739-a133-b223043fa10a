# app/processing/parsing_report.py

from dataclasses import dataclass
from typing import Any


@dataclass
class ParsingReport:
    """Отчет о парсинге содержащий диагностическую информацию.

    Предоставляет официальный контракт для передачи диагностических данных
    от парсеров к процессорам, заменяя хак с _last_transformer.
    """

    # Информация о сломанных сносках
    broken_footnotes: list[str]

    # Общие метрики парсинга
    total_footnotes: int
    total_chapters: int

    # Формат исходного файла
    source_format: str

    # Дополнительные диагностические данные (расширяемо)
    diagnostics: dict[str, Any]

    def has_broken_footnotes(self) -> bool:
        """Проверяет наличие сломанных сносок."""
        return len(self.broken_footnotes) > 0

    def get_broken_footnotes_count(self) -> int:
        """Возвращает количество сломанных сносок."""
        return len(self.broken_footnotes)

    def add_diagnostic(self, key: str, value: Any) -> None:
        """Добавляет диагностическую информацию."""
        self.diagnostics[key] = value
