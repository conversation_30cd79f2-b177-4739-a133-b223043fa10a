# app/processing/parsers/fb2/markdown_renderer.py

import logging
from typing import Union

from .fb2_model import (
    Annotation,
    BoldItalic,
    Cite,
    Code,
    Emphasis,
    Link,
    Note,
    Paragraph,
    Poem,
    Strikethrough,
    Strong,
    Style,
    Subscript,
    Superscript,
)

logger = logging.getLogger(__name__)


class MarkdownRenderer:
    """Отвечает за рендеринг FB2 элементов в Markdown.

    Инкапсулирует всю логику преобразования FB2 форматирования в Markdown,
    включая обработку сносок, встраивание ссылок и очистку форматирования.
    """

    def __init__(self):
        # Кэш для встроенных элементов (защита от циклических ссылок)
        self._inline_stack: set[str] = set()
        self._inline_cache: dict[str, str] = {}

        # Карта ID элементов и реестр встроенных ID для обработки сносок
        self.id_map: dict[str, Paragraph] = {}
        self.inlined_ids: set[str] = set()

        # Словарь сносок и список сломанных сносок
        self.footnotes: dict[str, str] = {}
        self.broken_footnotes: list[str] = []

    def set_id_map(self, id_map: dict[str, Paragraph]) -> None:
        """Устанавливает карту ID элементов для обработки сносок."""
        self.id_map = id_map
        self.inlined_ids = set()
        self._inline_cache = {}

    def set_footnotes(self, footnotes: dict[str, str]) -> None:
        """Устанавливает словарь сносок для обработки."""
        self.footnotes = footnotes
        self.broken_footnotes = []  # Сбрасываем список сломанных сносок

    def render_content_to_markdown(
        self,
        content_item: Union[
            str,
            Emphasis,
            Strong,
            BoldItalic,
            Link,
            Subscript,
            Superscript,
            Strikethrough,
            Code,
            Style,
            Note,
            Paragraph,
        ],
    ) -> str:
        """Рекурсивно рендерит один элемент в Markdown-строку с полной поддержкой FB2 форматирования."""
        if isinstance(content_item, str):
            return content_item

        # Комбинированное форматирование
        if isinstance(content_item, BoldItalic):
            return f" ***{content_item.text.strip()}*** " if content_item.text else ""

        # Базовое форматирование
        if isinstance(content_item, Emphasis):
            return f" *{content_item.text.strip()}* " if content_item.text else ""
        if isinstance(content_item, Strong):
            return f" **{content_item.text.strip()}** " if content_item.text else ""

        # Специальное форматирование
        if isinstance(content_item, Subscript):
            # Markdown не поддерживает подстрочник нативно, используем HTML
            return f"<sub>{content_item.text.strip()}</sub>" if content_item.text else ""
        if isinstance(content_item, Superscript):
            # Markdown не поддерживает надстрочник нативно, используем HTML
            return f"<sup>{content_item.text.strip()}</sup>" if content_item.text else ""
        if isinstance(content_item, Strikethrough):
            return f" ~~{content_item.text.strip()}~~ " if content_item.text else ""
        if isinstance(content_item, Code):
            return f" `{content_item.text.strip()}` " if content_item.text else ""

        # Стилизованный текст (специальный FB2 тег)
        if isinstance(content_item, Style):
            # Определяем тип стилизации или используем emphasis по умолчанию
            if content_item.name == "strong":
                return f" **{content_item.text.strip()}** " if content_item.text else ""
            else:
                return f" *{content_item.text.strip()}* " if content_item.text else ""

        # Сноски - встраиваем содержимое из словаря footnotes
        if isinstance(content_item, Note):
            return self._render_footnote(content_item)

        # Цитаты
        if isinstance(content_item, Cite):
            # Собираем содержимое цитаты
            cite_content = []
            for item in content_item.paragraphs:
                cite_content.append(self.render_content_to_markdown(item))
            cite_text = "".join(cite_content).strip()
            return f"\n> {cite_text}\n"

        # Ссылки
        if isinstance(content_item, Link):
            # Внутренняя ссылка (href начинается с "#")
            if content_item.href and content_item.href.startswith("#"):
                target_id = content_item.href.lstrip("#")

                # 1. Быстрый путь: есть готовый результат в кэше
                if target_id in self._inline_cache:
                    rendered_inline = self._inline_cache[target_id]
                # 2. Защита от самоссылок / циклов
                elif target_id in self._inline_stack:
                    rendered_inline = ""
                # 3. Рендерим параграф, если он есть в id_map
                elif target_id in self.id_map:
                    self._inline_stack.add(target_id)
                    target_paragraph = self.id_map[target_id]
                    self.inlined_ids.add(target_id)
                    rendered_inline = self.render_content_to_markdown(target_paragraph).strip()
                    self._inline_cache[target_id] = rendered_inline
                    self._inline_stack.remove(target_id)
                else:
                    rendered_inline = ""

                link_text = (content_item.text or "").strip()
                if link_text:
                    return f" {link_text} ({rendered_inline}) " if rendered_inline else f" {link_text} "
                return f" {rendered_inline} " if rendered_inline else ""

            # Внешняя ссылка или непонятный href – стандартный Markdown
            text = (content_item.text or content_item.href or "").strip()
            return f"[{text}]({content_item.href})" if content_item.href else ""

        # Рекурсивная обработка параграфов
        if isinstance(content_item, Paragraph):
            return "".join(self.render_content_to_markdown(p) for p in content_item.content)

        # Неизвестный тип элемента - возвращаем как есть
        return str(content_item)

    def _render_footnote(self, note: Note) -> str:
        """Обрабатывает сноску - встраивает содержимое или помечает как сломанную.

        Args:
            note: Объект сноски с href и текстом

        Returns:
            Отформатированная строка со сноской
        """
        if not note.href:
            return f" [{note.text}] " if note.text else " [СНОСКА] "

        # Убираем '#' из начала href, если он есть
        footnote_id = note.href.lstrip("#")

        # Если нет словаря сносок - это не ошибка, просто возвращаем текст ссылки
        if not self.footnotes:
            return f" [{note.text}] " if note.text else " [СНОСКА] "

        # Ищем сноску в словаре footnotes (приоритет 1)
        if footnote_id in self.footnotes:
            footnote_content = self.footnotes[footnote_id].strip()
            note_text = (note.text or "").strip()

            # Извлекаем номер сноски из footnote_id (n_4 -> "4", note_3 -> "3")
            import re

            note_number_match = re.search(r"(\d+)$", footnote_id)
            note_number = note_number_match.group(1) if note_number_match else footnote_id

            # Очищаем содержимое сноски от ведущего номера ("4 текст" -> "текст")
            import re

            cleaned_content = re.sub(r"^\s*\d+\s*\.?\s*:?\s*", "", footnote_content).strip()

            if note_text and cleaned_content:
                return f" {note_text} (Сноска {note_number}: {cleaned_content}) "
            elif cleaned_content:
                return f" (Сноска {note_number}: {cleaned_content}) "
            elif note_text:
                return f" {note_text} "
            else:
                return " [СНОСКА] "

        # Fallback: ищем параграф с таким ID в id_map (приоритет 2)
        elif footnote_id in self.id_map:
            note_text = (note.text or "").strip()

            # Используем ту же логику встраивания, что и для Link
            if footnote_id in self._inline_cache:
                rendered_inline = self._inline_cache[footnote_id]
            elif footnote_id in self._inline_stack:
                rendered_inline = ""  # Защита от циклов
            else:
                self._inline_stack.add(footnote_id)
                target_paragraph = self.id_map[footnote_id]
                self.inlined_ids.add(footnote_id)
                rendered_inline = self.render_content_to_markdown(target_paragraph).strip()
                self._inline_cache[footnote_id] = rendered_inline
                self._inline_stack.remove(footnote_id)

            # Извлекаем номер для форматирования
            import re

            note_number_match = re.search(r"(\d+)$", footnote_id)
            note_number = note_number_match.group(1) if note_number_match else footnote_id

            if note_text and rendered_inline:
                return f" {note_text} (Сноска {note_number}: {rendered_inline}) "
            elif rendered_inline:
                return f" (Сноска {note_number}: {rendered_inline}) "
            elif note_text:
                return f" {note_text} "
            else:
                return " [СНОСКА] "

        else:
            # Сноска не найдена ни в footnotes, ни в id_map - добавляем в список сломанных
            if footnote_id not in self.broken_footnotes:
                self.broken_footnotes.append(footnote_id)
                logger.debug(f"Сноска не найдена ни в footnotes, ни в id_map: {footnote_id}")

            # Возвращаем текст ссылки, если есть (без дублирования скобок)
            return f" {note.text} " if note.text else " [СНОСКА] "

    def clean_markdown_spacing(self, text: str) -> str:
        """Очищает избыточные пробелы в Markdown тексте."""
        import re

        # Убираем лишние пробелы вокруг форматирования
        text = re.sub(r"\s+\*\s+", " *", text)  # " * " → " *"
        text = re.sub(r"\s+\*\*\s+", " **", text)  # " ** " → " **"
        text = re.sub(r"\s+\*\*\*\s+", " ***", text)  # " *** " → " ***"
        text = re.sub(r"\*\s+", "*", text)  # "* " → "*"
        text = re.sub(r"\*\*\s+", "**", text)  # "** " → "**"
        text = re.sub(r"\*\*\*\s+", "***", text)  # "*** " → "***"
        text = re.sub(r"\s+\*", " *", text)  # " *" → " *"
        text = re.sub(r"\s+\*\*", " **", text)  # " **" → " **"
        text = re.sub(r"\s+\*\*\*", " ***", text)  # " ***" → " ***"

        # Убираем множественные пробелы
        text = re.sub(r" {2,}", " ", text)
        return text.strip()

    def convert_elements_to_markdown(self, elements: list) -> str:
        """Преобразует список FB2 элементов в Markdown строку."""
        md_parts = []

        for element in elements:
            if isinstance(element, Paragraph):
                # Пропускаем параграфы, которые уже были встроены как сноски
                if element.id and element.id in self.inlined_ids:
                    logger.debug(f"Пропускаем дублирование параграфа с ID {element.id}")
                    continue

                paragraph_text = self.render_content_to_markdown(element)
                paragraph_text = self.clean_markdown_spacing(paragraph_text.strip())
                md_parts.append(paragraph_text)
            elif isinstance(element, Poem):
                # Собираем содержимое поэмы
                poem_lines = []
                if element.title:
                    poem_lines.append(f"**{element.title}**")

                for stanza in element.stanzas:
                    stanza_lines = []
                    for line in stanza.lines:
                        # Защита от None в line.text
                        line_text = line.text or ""
                        if line_text.strip():  # Только непустые строки
                            stanza_lines.append(f"*{line_text.strip()}*")  # Курсив для стихов

                    if stanza_lines:  # Добавляем строфу только если есть содержимое
                        poem_lines.extend(stanza_lines)
                        poem_lines.append("")  # Пустая строка между строфами

                if element.text_author:
                    poem_lines.append(f"— {element.text_author}")

                # Убираем последнюю пустую строку и добавляем только если есть содержимое
                if poem_lines and poem_lines[-1] == "":
                    poem_lines.pop()

                if poem_lines:
                    md_parts.append("\n".join(poem_lines))

            elif isinstance(element, Cite):
                # Собираем содержимое цитаты
                cite_content = []
                for item in element.paragraphs:
                    cite_content.append(self.render_content_to_markdown(item))
                cite_text = "".join(cite_content).strip()
                if cite_text:
                    md_parts.append(f"> {cite_text}")

        # Объединяем все части через двойной перенос строки для правильного Markdown
        return "\n\n".join(filter(None, md_parts))

    def extract_clean_title(self, title_annotation: Annotation) -> str:
        """Извлекает заголовок и ОЧИЩАЕТ его от HTML-тегов."""
        from .utils import extract_clean_text

        return extract_clean_text(title_annotation)
