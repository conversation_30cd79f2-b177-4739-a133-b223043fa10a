# app/processing/parsers/fb2/fb2_repairer.py

"""
FB2Repairer - компонент для исправления поврежденного XML в FB2 файлах.

Отвечает за предварительную обработку и исправление различных проблем XML:
- Проблемы с namespace и кодировкой
- Невалидные символы в XML
- Несоответствие открывающих/закрывающих тегов
- Неправильная вложенность параграфов
- Невалидные атрибуты
- Структурные проблемы

Используется FB2Parser перед основным парсингом XML.
"""

import io
import logging
import re
import subprocess
import tempfile
from pathlib import Path
from typing import Any, Union

# Импортируем lxml для профессионального исправления XML
try:
    from lxml import etree

    LXML_AVAILABLE = True
except ImportError:
    LXML_AVAILABLE = False


class FB2Repairer:
    """Компонент для исправления поврежденного XML в FB2 файлах."""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # Статистика XML исправлений для отчетности
        self.xml_fixes_stats: dict[str, Any] = {
            "total_fixes_applied": 0,
            "paragraph_fixes": 0,
            "structural_fixes": 0,
            "attribute_fixes": 0,
            "lxml_recover_fixes": 0,  # Исправления через lxml recover
            "fixes_details": [],
        }

    def reset_xml_fixes_stats(self) -> None:
        """Сбрасывает статистику XML исправлений перед обработкой нового файла."""
        self.xml_fixes_stats = {
            "total_fixes_applied": 0,
            "paragraph_fixes": 0,
            "structural_fixes": 0,
            "attribute_fixes": 0,
            "lxml_recover_fixes": 0,  # Исправления через lxml recover
            "fixes_details": [],
        }

    def get_xml_fixes_stats(self) -> dict[str, Any]:
        """Возвращает статистику примененных XML исправлений."""
        return self.xml_fixes_stats.copy()

    def repair(self, source: Union[Path, io.BytesIO]) -> bytes:
        """Публичный интерфейс для исправления поврежденного XML.

        Использует профессиональный подход:
        1. Превентивная regex очистка типовых FB2 ошибок
        2. lxml XMLParser(recover=True) - основной метод
        3. Fallback: xmllint --recover (если доступен)
        4. Крайний случай: возврат исходного XML

        Args:
            source: Путь к файлу или поток с XML данными

        Returns:
            Исправленный XML в виде bytes
        """
        self.reset_xml_fixes_stats()
        return self._repair_with_professional_approach(source)

    def _repair_with_professional_approach(self, source: Union[Path, io.BytesIO]) -> bytes:
        """Профессиональное исправление XML с многоуровневым fallback подходом."""
        try:
            # Читаем исходный XML
            if isinstance(source, Path):
                with open(source, "rb") as f:
                    original_content = f.read()
            else:
                source.seek(0)
                original_content = source.read()
                source.seek(0)

            # Конвертируем в строку для обработки
            try:
                xml_str = original_content.decode("utf-8")
            except UnicodeDecodeError:
                # Пробуем другие кодировки
                for encoding in ["cp1251", "iso-8859-1", "latin1"]:
                    try:
                        xml_str = original_content.decode(encoding)
                        self.logger.info(f"Использована кодировка {encoding}")
                        break
                    except UnicodeDecodeError:
                        continue
                else:
                    xml_str = original_content.decode("utf-8", errors="replace")
                    self.logger.warning("Использована замена некорректных символов")

            # Этап 1: Превентивная regex очистка
            cleaned_xml = self._preventive_regex_cleanup(xml_str)

            # Этап 2: lxml XMLParser(recover=True)
            repaired_xml = self._repair_with_lxml_recover(cleaned_xml)
            if repaired_xml:
                return repaired_xml.encode("utf-8")

            # Этап 3: Fallback - xmllint --recover
            xmllint_result = self._repair_with_xmllint(original_content)
            if xmllint_result:
                return xmllint_result

            # Этап 4: Крайний случай - возвращаем очищенный XML
            # TODO: Добавить html5lib + ручную реконструкцию как последний fallback
            # html5lib может справиться с совсем сломанным XML, но потребует
            # ручной реконструкции FB2 структуры (description, body, binary)
            self.logger.warning("Все методы исправления не сработали, возвращаем очищенный XML")
            return cleaned_xml.encode("utf-8")

        except Exception as e:
            self.logger.error(f"Критическая ошибка при исправлении XML: {e}")
            return original_content

    def _preventive_regex_cleanup(self, xml_str: str) -> str:
        """Превентивная очистка типовых FB2 ошибок с помощью регулярных выражений."""
        fixes_count = 0

        # Паттерны для типовых FB2 ошибок (на основе советов экспертов)
        CLEANUP_PATTERNS = [
            # 1. Голые амперсанды (не экранированные &)
            (r"&(?!(?:amp|lt|gt|quot|apos|#\d+|#x[0-9a-fA-F]+);)", "&amp;"),
            # 2. Неправильные теги <em> вместо <emphasis>
            (r"<(/?)em>", r"<\1emphasis>"),
            # 3. Неправильные теги <strong> вместо <strong> (FB2 использует <strong>)
            (r"<(/?)b>", r"<\1strong>"),
            # 4. Удаление непарных открывающих тегов (упрощенная версия)
            (r"<(p|emphasis|strong|title)\s*(?:[^>]*?)(?<!/)>(?=\s*<(?:section|/body|/FictionBook))", r""),
            # 5. Исправление самозакрывающихся тегов которые не должны быть самозакрывающимися
            (r"<(p|emphasis|strong|title|section)([^>]*)/>", r"<\1\2></\1>"),
            # 6. Удаление невалидных символов управления (кроме разрешенных)
            (r"[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]", ""),
            # 7. СПЕЦИФИЧНОЕ ИСПРАВЛЕНИЕ: </emphasis></p> → </emphasis></section>
            # Исправляет конкретную ошибку "section line 31 and p, line 35"
            (r"</emphasis></p>", "</emphasis></section>"),
        ]

        for pattern, replacement in CLEANUP_PATTERNS:
            old_xml = xml_str
            xml_str = re.sub(pattern, replacement, xml_str, flags=re.IGNORECASE | re.MULTILINE)
            if xml_str != old_xml:
                fixes_count += 1

        if fixes_count > 0:
            self.logger.info(f"Превентивная regex очистка: {fixes_count} исправлений")
            self.xml_fixes_stats["preventive_regex_fixes"] = fixes_count

        return xml_str

    def _repair_with_lxml_recover(self, xml_str: str) -> str:
        """Исправление XML с помощью lxml XMLParser(recover=True)."""
        if not LXML_AVAILABLE:
            self.logger.warning("lxml недоступен, пропускаем lxml recover")
            return None

        try:
            # Создаем парсер с режимом восстановления (как советуют эксперты)
            parser = etree.XMLParser(
                recover=True,  # Основная магия - восстановление структуры
                no_network=True,  # Безопасность - не загружать внешние DTD
                resolve_entities=False,  # Безопасность - не разрешать сущности
                huge_tree=True,  # FB2 часто содержит большие <binary>
                encoding="utf-8",
            )

            # Парсим XML
            root = etree.fromstring(xml_str.encode("utf-8"), parser)

            # Получаем исправленный XML (без xml_declaration для unicode)
            repaired_xml = etree.tostring(root, encoding="unicode")

            # Базовая проверка что получился валидный XML
            if len(repaired_xml) < 100:  # Минимальная длина для FB2
                self.logger.warning("lxml recover вернул слишком короткий XML, отклоняем результат")
                return None

            self.logger.info("✅ lxml XMLParser(recover=True) успешно исправил XML")
            self.xml_fixes_stats["lxml_recover_success"] = True
            return repaired_xml

        except Exception as e:
            self.logger.warning(f"lxml XMLParser(recover=True) не смог исправить XML: {e}")
            self.xml_fixes_stats["lxml_recover_failed"] = str(e)
            return None

    def _repair_with_xmllint(self, xml_content: bytes) -> bytes:
        """Fallback исправление с помощью xmllint --recover."""
        try:
            # Создаем временный файл
            with tempfile.NamedTemporaryFile(mode="wb", suffix=".xml", delete=False) as temp_file:
                temp_file.write(xml_content)
                temp_path = temp_file.name

            try:
                # Запускаем xmllint --recover
                cmd = ["xmllint", "--encode", "utf-8", "--recover", temp_path]
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    timeout=30,  # Таймаут 30 секунд
                    check=False,  # Не падать при ненулевом коде возврата
                )

                if result.stdout and len(result.stdout) > 100:  # Минимальная проверка
                    self.logger.info("✅ xmllint --recover успешно исправил XML")
                    self.xml_fixes_stats["xmllint_recover_success"] = True
                    return result.stdout
                else:
                    self.logger.warning("xmllint --recover вернул слишком короткий результат")
                    return None

            finally:
                # Удаляем временный файл
                Path(temp_path).unlink(missing_ok=True)

        except subprocess.TimeoutExpired:
            self.logger.warning("xmllint --recover превысил таймаут")
            return None
        except FileNotFoundError:
            self.logger.debug("xmllint не найден в системе")
            return None
        except Exception as e:
            self.logger.warning(f"Ошибка xmllint --recover: {e}")
            return None

    def _preprocess_xml(self, source: Union[Path, io.BytesIO]) -> bytes:
        """УСТАРЕЛ: Переадресация на новый профессиональный подход."""
        return self._repair_with_professional_approach(source)

    # УДАЛЕНЫ: Все старые regex методы заменены на профессиональный подход
