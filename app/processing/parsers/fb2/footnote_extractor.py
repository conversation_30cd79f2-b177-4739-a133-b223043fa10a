# app/processing/parsers/fb2/footnote_extractor.py

"""
FootnoteExtractor - компонент для извлечения сносок из FB2 файлов.

Отвечает за извлечение сносок из различных форматов разметки FB2:
- Стандартная разметка <body name="notes"> с секциями
- Сноски в виде <p id="..."> внутри блока сносок
- Поиск по заголовку секции "Примечания/Notes/Сноски"
- Поиск по паттерну ID сносок во всем документе
- Поиск сносок как последовательности параграфов

Используется FB2Parser после основного парсинга XML.
"""

import logging
import re

# Используем lxml для работы с XML элементами
try:
    from lxml import etree as ET
except ImportError:
    # Fallback на defusedxml для безопасности
    import defusedxml.ElementTree as ET


class FootnoteExtractor:
    """Компонент для извлечения сносок из FB2 файлов."""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # Цепочка стратегий для извлечения сносок (в порядке приоритета)
        self._footnote_strategies = [
            self._extract_footnotes_standard,  # 1. Стандарт: <section id="...">
            self._extract_footnotes_from_paragraphs_in_body,  # 2. <p id="..."> внутри блока сносок
            self._extract_footnotes_by_section_title,  # 3. Поиск по заголовку "Примечания"
            self._extract_footnotes_by_id_pattern,  # 4. Поиск секций с id="n_X" во всём документе
            self._extract_footnotes_by_paragraph_sequence,  # 5. Последовательность параграфов с номерами
        ]

    def extract(self, root_element: ET.Element) -> dict[str, str]:
        """Публичный интерфейс для извлечения сносок из XML элемента.

        Args:
            root_element: Корневой XML элемент FB2 документа

        Returns:
            Словарь {note_id: текст_сноски}
        """
        return self._extract_footnotes(root_element)

    def _get_full_text_content(self, element: ET.Element) -> str:
        """Рекурсивно извлекает весь текстовый контент из элемента, включая вложенные теги."""
        text_parts = []

        # Добавляем текст самого элемента
        if element.text:
            text_parts.append(element.text.strip())

        # Рекурсивно обрабатываем всех потомков
        for child in element:
            child_text = self._get_full_text_content(child)
            if child_text:
                text_parts.append(child_text)

            # Добавляем tail текст (текст после закрывающего тега потомка)
            if child.tail:
                text_parts.append(child.tail.strip())

        return " ".join(filter(None, text_parts))

    def _extract_footnotes(self, root: ET.Element) -> dict[str, str]:
        """Извлекает сноски используя цепочку стратегий.

        Применяет стратегии в порядке приоритета до первого успешного результата.
        Это обеспечивает обработку как стандартных, так и нестандартных схем разметки сносок.

        Returns:
            Словарь {note_id: текст_сноски}
        """
        for strategy in self._footnote_strategies:
            footnotes = strategy(root)
            if footnotes:
                return footnotes
        return {}

    def _extract_footnotes_standard(self, root: ET.Element) -> dict[str, str]:
        """Стратегия №1: Стандартная разметка <body name="notes"> или <body name="footnotes">.

        Ищет все <section id="note_X"> внутри <body name="notes"> или <body name="footnotes">.
        """
        footnotes = {}

        # Ищем все <body name="notes"> и <body name="footnotes">
        footnote_body_names = {"notes", "footnotes"}
        for body_name in footnote_body_names:
            for notes_body in root.findall(f".//body[@name='{body_name}']"):
                # В каждом теле сносок ищем секции с id
                # Добавляем ".//" для рекурсивного поиска на любую глубину вложенности
                for section in notes_body.findall(".//section"):
                    section_id = section.get("id")
                    if section_id:
                        # Извлекаем весь текстовый контент из секции
                        footnote_text = self._get_full_text_content(section)
                        if footnote_text:
                            footnotes[section_id] = footnote_text

        return footnotes

    def _extract_footnotes_from_paragraphs_in_body(self, root: ET.Element) -> dict[str, str]:
        """Стратегия №1.5: Сноски в виде <p id="..."> внутри <body name="notes/footnotes">.

        Обрабатывает нестандартную разметку, где каждая сноска является параграфом с атрибутом id.
        """
        footnotes: dict[str, str] = {}

        footnote_body_names = {"notes", "footnotes"}

        for body_name in footnote_body_names:
            # Ищем блоки с заданным именем
            for notes_body in root.findall(f".//body[@name='{body_name}']"):
                # Ищем все параграфы, которые имеют атрибут id
                for p_tag in notes_body.findall(".//p[@id]"):
                    note_id = p_tag.get("id")
                    if not note_id:
                        continue

                    # Извлекаем полный текст параграфа
                    footnote_text = self._get_full_text_content(p_tag)
                    if not footnote_text:
                        continue

                    # Очищаем ведущий номер ("1. " или "1 ") при наличии
                    cleaned_text = re.sub(r"^\s*\d+\s*\.?:?\s*", "", footnote_text).strip()

                    if cleaned_text:
                        footnotes[note_id] = cleaned_text

        return footnotes

    def _extract_footnotes_by_section_title(self, root: ET.Element) -> dict[str, str]:
        """Стратегия №2: Поиск по заголовку секции "Примечания/Notes".

        Ищет секции с заголовками типа "Примечания", "Notes", "Сноски" и извлекает
        из них вложенные секции с id как сноски.
        """
        footnotes = {}

        # Паттерны заголовков секций примечаний (регистронезависимо)
        notes_patterns = [
            re.compile(r"примечания", re.IGNORECASE),
            re.compile(r"сноски", re.IGNORECASE),
            re.compile(r"notes", re.IGNORECASE),
            re.compile(r"footnotes", re.IGNORECASE),
            re.compile(r"комментарии", re.IGNORECASE),
        ]

        # Ищем все секции во всех body (кроме notes)
        for body in root.findall("body"):
            if body.get("name") == "notes":
                continue  # Пропускаем стандартные блоки сносок

            for section in body.findall(".//section"):
                # Проверяем заголовок секции
                if section.find("title") is not None:
                    title_text = self._get_full_text_content(section.find("title")).lower()

                    # Проверяем соответствие паттернам примечаний
                    if any(pattern.search(title_text) for pattern in notes_patterns):
                        # Нашли секцию примечаний, ищем в ней вложенные секции с id
                        for subsection in section.findall(".//section"):
                            section_id = subsection.get("id")
                            if section_id:
                                footnote_text = self._get_full_text_content(subsection)
                                if footnote_text:
                                    footnotes[section_id] = footnote_text

        return footnotes

    def _extract_footnotes_by_id_pattern(self, root: ET.Element) -> dict[str, str]:
        """Стратегия №3: Поиск по паттерну id сносок во всем документе.

        Ищет все секции с id соответствующими паттернам сносок (n_X, note_X, fn_X).
        """
        footnotes = {}

        # Регулярные выражения для id сносок
        footnote_id_patterns = [
            re.compile(r"^n_\d+$"),  # n_1, n_2, ...
            re.compile(r"^note_?\d+$"),  # note_1, note1, ...
            re.compile(r"^fn_?\d+$"),  # fn_1, fn1, ...
            re.compile(r"^sn_?\d+$"),  # sn_1, sn1, ... (сноска)
        ]

        # Ищем все секции во всех body (кроме уже обработанных notes)
        for body in root.findall("body"):
            if body.get("name") == "notes":
                continue  # Пропускаем стандартные блоки сносок

            for section in body.findall(".//section"):
                section_id = section.get("id")
                if section_id:
                    # Проверяем соответствие паттернам id сносок
                    if any(pattern.match(section_id) for pattern in footnote_id_patterns):
                        footnote_text = self._get_full_text_content(section)
                        if footnote_text:
                            footnotes[section_id] = footnote_text

        return footnotes

    def _extract_footnotes_by_paragraph_sequence(self, root: ET.Element) -> dict[str, str]:
        """Стратегия №4: Поиск сносок как последовательности параграфов.

        Ищет заголовок "notes/сноски/примечания" в <p><strong>, затем извлекает
        последовательность: номер в <strong> -> текст сноски -> номер -> текст...
        """
        footnotes: dict[str, str] = {}

        # Паттерны заголовков сносок
        header_patterns = [
            re.compile(r"^notes$", re.IGNORECASE),
            re.compile(r"^сноски$", re.IGNORECASE),
            re.compile(r"^примечания$", re.IGNORECASE),
            re.compile(r"^footnotes$", re.IGNORECASE),
        ]

        # Паттерн для номеров сносок (только цифры в <strong>)
        number_pattern = re.compile(r"^\s*(\d+)\s*$")

        # Ищем во всех body (кроме notes)
        for body in root.findall("body"):
            if body.get("name") == "notes":
                continue

            # Получаем все параграфы из всех секций этого body
            all_paragraphs = []
            for section in body.findall(".//section"):
                for p in section.findall(".//p"):
                    all_paragraphs.append(p)

            # Ищем заголовок блока сносок
            header_found = False
            for i, p in enumerate(all_paragraphs):
                # Проверяем есть ли <strong> с заголовком сносок
                strong_elem = p.find("strong")
                if strong_elem is not None:
                    strong_text = self._get_full_text_content(strong_elem).strip().lower()

                    if any(pattern.match(strong_text) for pattern in header_patterns):
                        header_found = True
                        # Начинаем поиск сносок после заголовка
                        self._parse_footnote_sequence(all_paragraphs[i + 1 :], footnotes, number_pattern)
                        break

            if header_found:
                break  # Нашли блок сносок, не ищем в других body

        return footnotes

    def _parse_footnote_sequence(
        self,
        paragraphs: list[ET.Element],
        footnotes: dict[str, str],
        number_pattern: re.Pattern[str],
    ) -> None:
        """Вспомогательный метод для парсинга последовательности сносок."""
        i = 0
        while i < len(paragraphs):
            p = paragraphs[i]

            # Пропускаем empty-line и другие нетекстовые элементы
            if p.tag != "p":
                i += 1
                continue

            # Проверяем, есть ли номер сноски в <strong>
            strong_elem = p.find("strong")
            if strong_elem is not None:
                strong_text = self._get_full_text_content(strong_elem).strip()
                match = number_pattern.match(strong_text)

                if match:
                    footnote_number = match.group(1)

                    # Ищем текст сноски в следующих параграфах
                    footnote_text_parts = []
                    j = i + 1

                    while j < len(paragraphs):
                        next_p = paragraphs[j]

                        # Если встретили следующий номер в <strong>, останавливаемся
                        if next_p.tag == "p":
                            next_strong = next_p.find("strong")
                            if next_strong is not None:
                                next_strong_text = self._get_full_text_content(next_strong).strip()
                                if number_pattern.match(next_strong_text):
                                    break

                            # Добавляем текст параграфа к сноске
                            p_text = self._get_full_text_content(next_p).strip()
                            if p_text:
                                footnote_text_parts.append(p_text)

                        j += 1

                    # Сохраняем сноску если есть текст
                    if footnote_text_parts:
                        footnote_id = f"n_{footnote_number}"
                        footnotes[footnote_id] = " ".join(footnote_text_parts)

                    i = j  # Переходим к следующему номеру
                    continue

            i += 1
