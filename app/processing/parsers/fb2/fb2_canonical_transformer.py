# app/processing/parsers/fb2/fb2_canonical_transformer.py

import logging
from datetime import date

from app.processing.canonical_model import (
    CanonicalAuthor,
    CanonicalBook,
    CanonicalChapter,
    CanonicalSequence,
)
from app.processing.parsing_report import ParsingReport

from .chapter_aggregator import ChapterAggregator
from .fb2_model import Body, FB2Book, Paragraph, Section
from .markdown_renderer import MarkdownRenderer

logger = logging.getLogger(__name__)


class FB2CanonicalTransformer:
    """Главный класс для трансформации FB2-модели в каноническую.

    Координирует работу MarkdownRenderer и ChapterAggregator для создания CanonicalBook.
    Ответственен за трансформацию метаданных и общую логику преобразования.
    """

    # Константа для фильтрации пустых body
    MIN_BODY_TEXT_LENGTH = 200

    def __init__(self, min_chapters_threshold: int = 4, min_chapter_length: int = 200):
        """Инициализирует трансформер.

        Args:
            min_chapters_threshold: Минимальный порог глав для применения эвристик (для больших книг)
            min_chapter_length: Минимальная длина главы в символах текстового контента
        """
        self.footnotes: dict[str, str] = {}  # Кэш сносок для текущей книги
        self.broken_footnotes: list[str] = []  # Список сломанных сносок для текущей книги

        # Сохраняем настройки для создания адаптивного ChapterAggregator
        self.default_min_chapters_threshold = min_chapters_threshold
        self.min_chapter_length = min_chapter_length

        # Компоненты трансформации
        self.markdown_renderer = MarkdownRenderer()

    def transform(
        self, fb2_book: FB2Book, publication_date: date, xml_fixes_stats: dict = None
    ) -> tuple[CanonicalBook, ParsingReport]:
        """Трансформирует FB2-модель в каноническую CanonicalBook с отчетом о парсинге."""
        # Инициализируем кэш сносок для текущей книги
        self.footnotes = fb2_book.footnotes
        self.broken_footnotes = []  # Сбрасываем список сломанных сносок

        # Шаг 1: Вычисляем размер книги ПЕРЕД агрегацией глав
        total_text_length = 0
        for body in fb2_book.bodies:
            # Собираем все элементы body для оценки размера (используем тот же подход что и ниже)
            body_size_elements: list = []
            if body.epigraphs:
                body_size_elements.extend(body.epigraphs)
            if body.sections:
                body_size_elements.extend(body.sections)
            total_text_length += CanonicalChapter._fast_text_length_estimation(None, body_size_elements)

        # Шаг 2: Определяем адаптивный порог для ChapterAggregator
        adaptive_threshold = self._get_adaptive_chapters_threshold(total_text_length)

        # Шаг 3: Создаем ChapterAggregator с адаптивным порогом
        self.chapter_aggregator = ChapterAggregator(
            min_chapters_threshold=adaptive_threshold,
            min_chapter_length=self.min_chapter_length,
            markdown_renderer=self.markdown_renderer,
        )

        logger.debug(f"📏 Размер книги: {total_text_length} символов, адаптивный порог: {adaptive_threshold} глав")

        # Строим карту всех элементов с ID перед началом обработки
        id_map = self._build_id_map(fb2_book.bodies)
        self.markdown_renderer.set_id_map(id_map)

        # Передаем сноски в MarkdownRenderer
        self.markdown_renderer.set_footnotes(self.footnotes)

        logger.debug(f"Загружено {len(self.footnotes)} сносок для обработки")
        logger.debug(f"Построена карта из {len(id_map)} элементов с ID")

        title_info = fb2_book.description.title_info
        if not title_info:
            raise ValueError("Отсутствует обязательная секция title_info в FB2")

        # Преобразуем аннотацию в Markdown
        annotation_md = ""
        if title_info.annotation:
            annotation_md = self.markdown_renderer.convert_elements_to_markdown(title_info.annotation.elements)

        # НОВАЯ ЛОГИКА: обрабатываем все body и выбираем стратегию от body с максимальным количеством глав
        chapters = []
        body_results = []

        for body in fb2_book.bodies:
            # НОВАЯ ПРОВЕРКА: фильтруем пустые body
            # Используем статический метод CanonicalChapter для подсчета длины текста
            body_elements: list = []
            if body.epigraphs:
                body_elements.extend(body.epigraphs)
            if body.sections:
                body_elements.extend(body.sections)

            text_length = CanonicalChapter._fast_text_length_estimation(None, body_elements)
            if text_length < self.MIN_BODY_TEXT_LENGTH:
                logger.debug(f"⏭️ Пропускаем body с {text_length} символами (< {self.MIN_BODY_TEXT_LENGTH})")
                continue

            # ФИЛЬТРАЦИЯ СЕКЦИЙ: удаляем служебные секции целиком перед обработкой
            filtered_sections = self.chapter_aggregator.filter_service_sections(body.sections)

            # Собираем корневые элементы body для обработки
            root_elements = []

            # Добавляем эпиграфы body, если есть
            if body.epigraphs:
                root_elements.extend(body.epigraphs)

            # Добавляем отфильтрованные секции как корневые элементы
            # State Machine сам обработает их рекурсивно
            root_elements.extend(filtered_sections)

            # Применяем ЕДИНЫЙ State Machine алгоритм
            body_chapters = self.chapter_aggregator.aggregate_chapters_from_elements(root_elements)
            chapters.extend(body_chapters)

            # Сохраняем результаты этого body для выбора лучшей стратегии
            body_name = getattr(body, "name", "None")
            body_results.append(
                {
                    "body_name": body_name,
                    "chapters_count": len(body_chapters),
                    "strategy": self.chapter_aggregator.get_last_used_heuristic(),
                    "detailed_strategy": self.chapter_aggregator.get_detailed_heuristic_info(),
                    "text_length": text_length,
                }
            )

            logger.debug(
                f"📊 Body '{body_name}': {len(body_chapters)} глав, стратегия: {self.chapter_aggregator.get_last_used_heuristic()}"
            )

        # Выбираем стратегию от body с максимальным количеством глав
        if body_results:
            best_body = max(body_results, key=lambda x: x["chapters_count"])
            used_heuristic = best_body["strategy"]
            detailed_heuristic = best_body["detailed_strategy"]
            logger.debug(
                f"🏆 Выбрана стратегия от body '{best_body['body_name']}' с {best_body['chapters_count']} главами: {used_heuristic}"
            )
        else:
            used_heuristic = None
            detailed_heuristic = "unknown"

        # СТРУКТУРНАЯ ФИЛЬТРАЦИЯ: удаляем служебные разделы после формирования всех глав
        chapters = self.chapter_aggregator.filter_service_chapters(chapters)

        # Фильтрация маленьких глав
        chapters = self.chapter_aggregator.filter_small_chapters(chapters)

        # Извлекаем метаданные
        genres = [genre.text for genre in title_info.genres if genre.text]
        keywords = []
        if title_info.keywords:
            keywords = [kw.strip() for kw in title_info.keywords.split(",") if kw.strip()]

        translators = [
            CanonicalAuthor(t.first_name, t.middle_name, t.last_name, t.nickname) for t in title_info.translators
        ]

        # Создаем raw_metadata из description
        raw_metadata = {
            "title_info": {
                "book_title": title_info.book_title,
                "lang": title_info.lang,
                "src_lang": title_info.src_lang,
                "keywords": title_info.keywords,
                "genres": [{"text": g.text, "match": g.match} for g in title_info.genres],
                "sequences": [{"name": s.name, "number": str(s.number or 0)} for s in title_info.sequences],
            }
        }

        # Добавляем publish_info если есть
        if fb2_book.description.publish_info:
            pub_info = fb2_book.description.publish_info
            raw_metadata["publish_info"] = {
                "book_name": pub_info.book_name,
                "publisher": pub_info.publisher,
                "city": pub_info.city,
                "year": str(pub_info.year) if pub_info.year else "",
                "isbn": pub_info.isbn,
            }

        canonical_book = CanonicalBook(
            title=title_info.book_title or "Без названия",
            lang=title_info.lang or "ru",
            source_format="fb2",
            authors=[CanonicalAuthor(a.first_name, a.middle_name, a.last_name, a.nickname) for a in title_info.authors],
            translators=translators,
            sequences=[CanonicalSequence(s.name, s.number or 0) for s in title_info.sequences],
            publication_date=publication_date,
            genres=genres,
            keywords=keywords,
            raw_metadata=raw_metadata,
            annotation_md=annotation_md,
            chapters=chapters,
        )

        # Создаем отчет о парсинге с диагностической информацией
        # Собираем сломанные сноски из markdown_renderer после обработки
        broken_footnotes = []
        if hasattr(self.markdown_renderer, "broken_footnotes"):
            broken_footnotes = self.markdown_renderer.broken_footnotes

        # Подготавливаем диагностические данные
        diagnostics = {}
        if xml_fixes_stats:
            diagnostics["xml_fixes_stats"] = xml_fixes_stats

        parsing_report = ParsingReport(
            broken_footnotes=broken_footnotes,
            total_footnotes=len(self.footnotes),
            total_chapters=len(chapters),
            source_format="fb2",
            diagnostics=diagnostics,
        )

        # Добавляем диагностическую информацию о использованной эвристике
        if used_heuristic:
            parsing_report.add_diagnostic("chapter_heuristic", used_heuristic)

        # Добавляем детализированную информацию о стратегии и маркерах
        # Используем уже вычисленную информацию от лучшего body
        parsing_report.add_diagnostic("detailed_chapter_heuristic", detailed_heuristic)

        return canonical_book, parsing_report

    def _get_adaptive_chapters_threshold(self, total_text_length: int) -> int:
        """Определяет адаптивный порог глав на основе размера книги.

        Архитектурное решение: маленькие книги (< 50K символов) физически
        не могут иметь 7 полноценных глав. Для них используем порог 2 главы.

        Args:
            total_text_length: Общий размер текста книги в символах

        Returns:
            Адаптивный порог количества глав
        """
        if total_text_length < 50000:  # Маленькие книги (рассказы, новеллы)
            return 2
        else:  # Большие книги (повести, романы)
            return self.default_min_chapters_threshold

    def _build_id_map(self, bodies: list) -> dict[str, Paragraph]:
        """Строит карту всех параграфов с ID для быстрого поиска при встраивании сносок."""
        id_map: dict[str, Paragraph] = {}

        def _scan_section(section: Section) -> None:
            """Рекурсивно сканирует секцию на предмет параграфов с ID."""
            for item in section.content:
                if isinstance(item, Section):
                    _scan_section(item)  # Рекурсивный обход вложенных секций
                elif isinstance(item, Paragraph) and item.id:
                    id_map[item.id] = item
                    logger.debug(f"Добавлен в карту ID: {item.id}")

        # Сканируем все bodies
        for body in bodies:
            if isinstance(body, Body):
                for section in body.sections:
                    _scan_section(section)

        return id_map

    # Публичные методы для совместимости с существующим API
    def get_broken_footnotes(self) -> list[str]:
        """Возвращает список ID сломанных сносок из последней обработанной книги."""
        if hasattr(self.markdown_renderer, "broken_footnotes"):
            return getattr(self.markdown_renderer, "broken_footnotes", [])
        return []

    def has_broken_footnotes(self) -> bool:
        """Проверяет, есть ли сломанные сноски в последней обработанной книге."""
        return len(self.get_broken_footnotes()) > 0

    # DEPRECATED: Методы add_chapter_heuristic и add_chapter_pattern удалены.
    # Для кастомизации паттернов модифицируйте MarkerAnalyzer в chapter_state_machine.py
