# app/processing/quarantine_processor.py

import logging

from app.database.queries import add_to_quarantine

from .error_handler import QuarantineType


class QuarantineProcessor:
    """Логический процессор карантина - "Аудиторский след".

    Новая архитектура: карантин - это не физическое место, а неизменяемая запись
    о событии в PostgreSQL. Исходные файлы остаются нетронутыми.

    Философия:
    - PostgreSQL - единственный источник правды о карантинном статусе
    - Никаких файловых операций (копирование, перемещение)
    - Атомарная запись в БД через одну транзакцию
    - Аналитическая гибкость через JSONB детали
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.logger.info("QuarantineProcessor инициализирован (PostgreSQL-режим)")

    def process(self, task_data: dict, quarantine_type: QuarantineType, reason: str) -> None:
        """Обрабатывает книгу для карантина - записывает в PostgreSQL.

        Единственный публичный метод для отправки книги в карантин.
        Извлекает source_type/source_id из task_data и создает запись в quarantined_books.

        Args:
            task_data: Данные задачи с полями source_type, source_id, archive_path, book_filename
            quarantine_type: Тип карантина (TRIAL, SMALL_CONTENT, ANTHOLOGY, etc.)
            reason: Человекочитаемая причина помещения в карантин

        Raises:
            ProcessingError: При ошибках записи в БД (пробрасывается из add_to_quarantine)
        """
        # Извлекаем обязательные поля из task_data
        source_type = task_data.get("source_type")
        source_id = task_data.get("source_id")

        if source_type is None or source_id is None:
            self.logger.error(
                f"❌ Некорректные данные задачи для карантина: source_type={source_type}, source_id={source_id}"
            )
            raise ValueError("task_data должен содержать source_type и source_id")

        # Формируем детали для сохранения в JSONB
        details = {
            "archive_path": task_data.get("archive_path"),
            "book_filename": task_data.get("book_filename"),
            "archive_mtime": task_data.get("archive_mtime"),
            "detected_anomalies": [{"type": quarantine_type.value, "reason": reason}],
        }

        # Записываем в PostgreSQL
        add_to_quarantine(
            source_type=source_type,
            source_id=source_id,
            quarantine_type=quarantine_type.value,
            reason=reason,
            details=details,
        )

        self.logger.info(
            f"🚫 Книга {source_type}:{source_id} отправлена в карантин (тип: {quarantine_type.value}): {reason}"
        )
