# app/processing/anthology_detector.py

import logging
import re
from typing import Any

from .dto import BookValidationContext

logger = logging.getLogger(__name__)


class AnthologyDetector:
    """Детектор антологий и сборников произведений разных авторов.

    Использует шесть правил для выявления сборников:
    1. Множественные авторы в метаданных (>= min_authors_threshold)
    2. Сильные ключевые слова в названии (strong_title_keywords)
    3. Обычные ключевые слова в названии книги (keyword_in_title)
    4. Ключевые слова в аннотации (keyword_in_annotation)
    5. Ключевые слова в тегах/keywords (keyword_in_keywords)
    6. Жанры антологий (genres)

    ЛОГИКА ПРИНЯТИЯ РЕШЕНИЙ (комбинированные правила):

    Точные признаки антологий (срабатывают):
    - strong_title_keywords (самостоятельный признак)
    - multiple_authors + keyword_in_title
    - multiple_authors + keyword_in_keywords
    - multiple_authors + keyword_in_annotation
    - multiple_authors + genres
    - keyword_in_title + keyword_in_annotation + keyword_in_keywords
    - genres + keyword_in_title

    Сомнительные признаки (отключены для тюнинга):
    - genres + keyword_in_keywords
    - genres + keyword_in_annotation

    ПРИМЕРЫ СИЛЬНЫХ КЛЮЧЕВЫХ СЛОВ:
    - "сборник рассказов", "сборник повестей", "сборник стихов"
    - "(сборник)", "[сборник]"
    - "антология рассказов", "собрание сочинений"
    - "избранные произведения", "collection of stories"

    ПРИМЕРЫ ЖАНРОВ АНТОЛОГИЙ:
    - prose, prose_classic, prose_history
    - poetry, poetry_classic, poetry_contemporary
    - sf_fantasy, folklore, child_tale

    Детектор возвращает детализированную информацию о местоположении
    найденных ключевых слов и жанров для улучшения отчетности и анализа.
    """

    def __init__(self, min_authors_threshold: int = 4):
        """Инициализирует детектор антологий с настраиваемым порогом авторов.

        Args:
            min_authors_threshold: Пороговое значение для правила "Много авторов"
        """
        self.min_authors_threshold = min_authors_threshold

        # Ключевые слова указывающие на сборник (с границами слов для точности)
        anthology_keywords_raw = [
            "сборник",
            "сборника",
            "рассказ",
            "повесть",
            "сказка",
            "сказки",
            "стихов",
            "стихи",
            "эссе",
            "tale",
            "очерк",
            "short",
            "collection",
            "новелла",
            "антология",
            "памфлет",
            "коллекция",
            "альманах",
            "зарисовка",
            "anthology",
            "миниатюра",
            "omnibus",
            "novelette",
            "эскиз",
            "басня",
            "sketch",
            "собрание",
            "пьеса",
            "этюд",
            "пьеса",
            "статьи",
            "фельетоны",
            "story",
            "stories",
            "стихотворен",
        ]

        # Компилируем регулярные выражения с границами слов
        self.anthology_keyword_patterns = [
            re.compile(rf"\b{re.escape(keyword)}\b", re.IGNORECASE) for keyword in anthology_keywords_raw
        ]

        # Жанры, которые могут указывать на сборник
        # Используются в комбинированных правилах с другими признаками
        anthology_genres_raw = [
            # Проза (часто содержит сборники рассказов)
            "prose",
            "prose_classic",
            "prose_history",
            "prose_contemporary",
            "prose_counter",
            "prose_rus_classic",
            "prose_su_classics",
            # Поэзия (часто сборники стихов)
            "poetry",
            "poetry_classic",
            "poetry_for_children",
            "poetry_rus_classic",
            "poetry_contemporary",
            # Драматургия (сборники пьес)
            "dramaturgy",
            "dramaturgy_classic",
            # Детская литература (часто сборники сказок)
            "child_tale",
            "child_prose",
            "child_poetry",
            # Научная фантастика (часто антологии)
            "sf_fantasy",
            "sf_space",
            "sf_social",
            # Фольклор (сборники народных произведений)
            "folklore",
            "folklore_song",
            "folklore_tale",
        ]
        self.anthology_genre_patterns = [
            re.compile(rf"^{re.escape(genre)}$", re.IGNORECASE) for genre in anthology_genres_raw
        ]

        # Сильные ключевые слова для заголовков (самостоятельный признак антологии)
        # Эти фразы в названии книги однозначно указывают на сборник
        strong_title_keywords_raw = [
            # Русские фразы - точные указания на сборники
            "сборник рассказов",
            "сборник повестей",
            "сборник стихов",
            "сборник произведений",
            "сборник сочинений",
            "сборник новелл",
            "сборник эссе",
            "сборник очерков",
            "сборник пьес",
            "сборник статей",
            # Сборник в скобках (часто в названиях)
            "(сборник)",
            "[сборник]",
            # Антологии
            "антология рассказов",
            "антология произведений",
            "антология поэзии",
            # Собрания и избранное
            "собрание сочинений",
            "избранные произведения",
            "избранные рассказы",
            "избранные стихи",
            "полное собрание",
            # Английские эквиваленты
            "collection of stories",
            "collection of poems",
            "anthology of",
            "selected works",
            "complete works",
            "collected stories",
            "collected poems",
        ]

        # Компилируем регулярные выражения для сильных ключевых слов
        # Используем границы слов для точного поиска фраз
        self.strong_title_keyword_patterns = [
            re.compile(rf"\b{re.escape(keyword)}\b", re.IGNORECASE) for keyword in strong_title_keywords_raw
        ]

    def detect_triggers(self, context: BookValidationContext) -> list[dict[str, Any]]:
        """Находит все сработавшие триггеры антологии.

        Args:
            context: Контекст валидации с предвычисленными метриками

        Returns:
            Список словарей с информацией о сработавших триггерах:
            [{'rule': 'multiple_authors', 'value': 4},
             {'rule': 'strong_title_keywords', 'value': 'сборник рассказов'},
             {'rule': 'keyword_in_title', 'value': 'сборник'},
             {'rule': 'keyword_in_annotation', 'value': 'рассказ'},
             {'rule': 'keyword_in_keywords', 'value': 'антология'},
             {'rule': 'genres', 'value': 'prose_classic'}]
        """
        triggers = []

        # Правило 1: Множественные авторы
        if context.author_count >= self.min_authors_threshold:
            triggers.append({"rule": "multiple_authors", "value": context.author_count})

        # Правило 2: Сильные ключевые слова в названии (приоритетная проверка)
        if context.book.title:
            for pattern in self.strong_title_keyword_patterns:
                match = pattern.search(context.book.title)
                if match:
                    triggers.append(
                        {
                            "rule": "strong_title_keywords",
                            "value": match.group().lower(),
                        }
                    )
                    break  # Первое совпадение достаточно

        # Правило 3: Обычные ключевые слова в названии
        if context.book.title:
            for pattern in self.anthology_keyword_patterns:
                match = pattern.search(context.book.title)
                if match:
                    triggers.append({"rule": "keyword_in_title", "value": match.group().lower()})
                    break  # Первое совпадение достаточно

        # Правило 4: Ключевые слова в аннотации
        if context.book.annotation_md:
            for pattern in self.anthology_keyword_patterns:
                match = pattern.search(context.book.annotation_md)
                if match:
                    triggers.append(
                        {
                            "rule": "keyword_in_annotation",
                            "value": match.group().lower(),
                        }
                    )
                    break  # Первое совпадение достаточно

        # Правило 5: Ключевые слова в keywords
        if context.book.keywords:
            # Объединяем все ключевые слова в одну строку для поиска
            keywords_text = " ".join(context.book.keywords)
            for pattern in self.anthology_keyword_patterns:
                match = pattern.search(keywords_text)
                if match:
                    triggers.append({"rule": "keyword_in_keywords", "value": match.group().lower()})
                    break  # Первое совпадение достаточно

        # Правило 6: Жанры антологий
        if context.book.genres:
            for genre in context.book.genres:
                for pattern in self.anthology_genre_patterns:
                    if pattern.match(genre):
                        triggers.append({"rule": "genres", "value": genre.lower()})
                        break  # Первое совпадение достаточно
                else:
                    continue  # Продолжаем поиск по другим жанрам
                break  # Найден жанр антологии, выходим из внешнего цикла

        return triggers

    def is_anthology(self, context: BookValidationContext) -> bool:
        """Проверяет, является ли книга антологией/сборником.

        Использует комбинированные правила для точной детекции:
        - Точные признаки: strong_title_keywords, multiple_authors + (keywords/annotation/genres),
          keyword_in_title + keyword_in_annotation + keyword_in_keywords, genres + keyword_in_title
        - Сомнительные признаки пока отключены для тюнинга

        Args:
            context: Контекст валидации с предвычисленными метриками

        Returns:
            True если книга является антологией, False иначе
        """
        triggers = self.detect_triggers(context)
        trigger_rules = {t["rule"] for t in triggers}

        # ТОЧНЫЕ ПРИЗНАКИ АНТОЛОГИЙ (высокая уверенность)

        # 1. Сильные ключевые слова в названии - самостоятельный признак
        # Примеры: "сборник рассказов", "(сборник)", "anthology of"
        if "strong_title_keywords" in trigger_rules:
            return True

        # 2. Множественные авторы + любой дополнительный признак
        # Логика: если много авторов И есть хотя бы один признак сборника
        if "multiple_authors" in trigger_rules:
            if any(
                rule in trigger_rules
                for rule in [
                    "keyword_in_title",
                    "keyword_in_keywords",
                    "keyword_in_annotation",
                    "genres",
                ]
            ):
                return True

        # 3. Тройная комбинация ключевых слов
        # Логика: ключевые слова одновременно в названии, аннотации и тегах
        if (
            "keyword_in_title" in trigger_rules
            and "keyword_in_annotation" in trigger_rules
            and "keyword_in_keywords" in trigger_rules
        ):
            return True

        # 4. Жанры + ключевые слова в названии
        # Логика: жанр антологии + ключевое слово в названии
        if "genres" in trigger_rules and "keyword_in_title" in trigger_rules:
            return True

        # СОМНИТЕЛЬНЫЕ ПРИЗНАКИ (отключены для тюнинга)
        # Эти комбинации могут давать ложные срабатывания:
        # - genres + keyword_in_keywords (жанр + ключевые слова в тегах)
        # - genres + keyword_in_annotation (жанр + ключевые слова в аннотации)
        # if ("genres" in trigger_rules and
        #     ("keyword_in_keywords" in trigger_rules or "keyword_in_annotation" in trigger_rules)):
        #     return True

        return False

    def get_anthology_reason(self, context: BookValidationContext) -> str:
        """Возвращает детальную причину, почему книга считается антологией.

        Показывает как отдельные триггеры, так и логику комбинированных правил.

        Args:
            context: Контекст валидации с предвычисленными метриками

        Returns:
            Строка с описанием причины
        """
        triggers = self.detect_triggers(context)
        trigger_rules = {t["rule"]: t["value"] for t in triggers}

        if not triggers:
            return "Книга не является антологией"

        # Определяем тип обнаруженной антологии
        if "strong_title_keywords" in trigger_rules:
            return f"Антология: сильный признак в названии ({trigger_rules['strong_title_keywords']})"

        # Проверяем комбинированные правила
        reasons = []

        # Множественные авторы + дополнительные признаки
        if "multiple_authors" in trigger_rules:
            additional_signs = []
            if "keyword_in_title" in trigger_rules:
                additional_signs.append(f"ключевое слово в названии ({trigger_rules['keyword_in_title']})")
            if "keyword_in_keywords" in trigger_rules:
                additional_signs.append(f"ключевое слово в тегах ({trigger_rules['keyword_in_keywords']})")
            if "keyword_in_annotation" in trigger_rules:
                additional_signs.append(f"ключевое слово в аннотации ({trigger_rules['keyword_in_annotation']})")
            if "genres" in trigger_rules:
                additional_signs.append(f"жанр антологии ({trigger_rules['genres']})")

            if additional_signs:
                reasons.append(f"много авторов ({trigger_rules['multiple_authors']}) + {', '.join(additional_signs)}")
            else:
                reasons.append(f"много авторов ({trigger_rules['multiple_authors']})")

        # Тройная комбинация ключевых слов
        if (
            "keyword_in_title" in trigger_rules
            and "keyword_in_annotation" in trigger_rules
            and "keyword_in_keywords" in trigger_rules
        ):
            if "multiple_authors" not in trigger_rules:  # Избегаем дублирования
                reasons.append("тройная комбинация ключевых слов (название + аннотация + теги)")

        # Жанры + ключевые слова в названии
        if "genres" in trigger_rules and "keyword_in_title" in trigger_rules:
            if "multiple_authors" not in trigger_rules:  # Избегаем дублирования
                reasons.append(
                    f"жанр антологии ({trigger_rules['genres']}) + ключевое слово в названии ({trigger_rules['keyword_in_title']})"
                )

        # Если нет комбинированных правил, показываем отдельные триггеры
        if not reasons:
            for trigger in triggers:
                if trigger["rule"] == "multiple_authors":
                    reasons.append(f"много авторов ({trigger['value']})")
                elif trigger["rule"] == "keyword_in_title":
                    reasons.append(f"ключевое слово в названии ({trigger['value']})")
                elif trigger["rule"] == "keyword_in_annotation":
                    reasons.append(f"ключевое слово в аннотации ({trigger['value']})")
                elif trigger["rule"] == "keyword_in_keywords":
                    reasons.append(f"ключевое слово в тегах ({trigger['value']})")
                elif trigger["rule"] == "genres":
                    reasons.append(f"жанр антологии ({trigger['value']})")

        return f"Антология: {', '.join(reasons)}"

    def get_anthology_triggers_string(self, context: BookValidationContext) -> str:
        """Возвращает строку с комбинированными правилами антологии для отчетов.

        Формирует машиночитаемую строку, показывающую какие именно комбинированные
        правила сработали для определения антологии.

        Args:
            context: Контекст валидации с предвычисленными метриками

        Returns:
            Строка в формате комбинированных правил:
            - "strong_title_keywords=найденное_слово"
            - "multiple_authors=N+keyword_in_title=слово"
            - "multiple_authors=N+genres=жанр"
            - "keyword_in_title+keyword_in_annotation+keyword_in_keywords=слова"
            - "genres=жанр+keyword_in_title=слово"
            Пустая строка, если книга не является антологией.
        """
        if not self.is_anthology(context):
            return ""

        triggers = self.detect_triggers(context)
        trigger_rules = {t["rule"]: t["value"] for t in triggers}

        # 1. Сильные ключевые слова в названии - самостоятельный признак
        if "strong_title_keywords" in trigger_rules:
            return f"strong_title_keywords={trigger_rules['strong_title_keywords']}"

        # 2. Множественные авторы + дополнительный признак
        if "multiple_authors" in trigger_rules:
            multiple_authors_value = trigger_rules["multiple_authors"]

            # Проверяем дополнительные признаки в порядке приоритета
            if "keyword_in_title" in trigger_rules:
                return f"multiple_authors={multiple_authors_value}+keyword_in_title={trigger_rules['keyword_in_title']}"
            elif "genres" in trigger_rules:
                return f"multiple_authors={multiple_authors_value}+genres={trigger_rules['genres']}"
            elif "keyword_in_keywords" in trigger_rules:
                return f"multiple_authors={multiple_authors_value}+keyword_in_keywords={trigger_rules['keyword_in_keywords']}"
            elif "keyword_in_annotation" in trigger_rules:
                return f"multiple_authors={multiple_authors_value}+keyword_in_annotation={trigger_rules['keyword_in_annotation']}"

        # 3. Тройная комбинация ключевых слов
        if (
            "keyword_in_title" in trigger_rules
            and "keyword_in_annotation" in trigger_rules
            and "keyword_in_keywords" in trigger_rules
        ):
            # Объединяем все найденные ключевые слова
            words = [
                trigger_rules["keyword_in_title"],
                trigger_rules["keyword_in_annotation"],
                trigger_rules["keyword_in_keywords"],
            ]
            return f"keyword_in_title+keyword_in_annotation+keyword_in_keywords={','.join(words)}"

        # 4. Жанры + ключевые слова в названии
        if "genres" in trigger_rules and "keyword_in_title" in trigger_rules:
            return f"genres={trigger_rules['genres']}+keyword_in_title={trigger_rules['keyword_in_title']}"

        # Fallback: если антология определена, но не попала в основные правила
        # (не должно происходить при корректной логике)
        trigger_parts = []
        for rule, value in trigger_rules.items():
            trigger_parts.append(f"{rule}={value}")
        return ";".join(trigger_parts)

    def get_configuration(self) -> dict:
        """Возвращает текущую конфигурацию детектора.

        Returns:
            Словарь с настройками
        """
        return {
            "min_authors_threshold": self.min_authors_threshold,
            "anthology_keywords_count": len(self.anthology_keyword_patterns),
            "anthology_genres_count": len(self.anthology_genre_patterns),
            "strong_title_keywords_count": len(self.strong_title_keyword_patterns),
        }
