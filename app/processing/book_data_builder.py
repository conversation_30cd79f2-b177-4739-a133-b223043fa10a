# app/processing/book_data_builder.py

import logging
from typing import Any

from .canonical_model import CanonicalBook
from .dto import AuthorDTO, BookDTO, BookSourceInfo, is_author_empty


class BookDataBuilder:
    """Строитель для создания BookDTO из канонической модели книги.
    Инкапсулирует логику извлечения и подготовки данных из CanonicalBook.
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def build_book_dto_from_canonical(
        self,
        canonical_book: CanonicalBook,
        task_data: dict[str, Any],
        metadata_hash: str,
    ) -> BookDTO:
        """Создает BookDTO напрямую из CanonicalBook и данных задачи.

        Args:
            canonical_book: Каноническая модель книги
            task_data: Данные задачи из очереди
            metadata_hash: Хэш метаданных

        Returns:
            Полностью подготовленный BookDTO
        """
        # Создаем информацию об источнике из CanonicalBook
        source_info = self._build_source_info_from_canonical(task_data, canonical_book)

        # Фильтруем пустых авторов и преобразуем в типизированные AuthorDTO
        authors_data = []
        for author in canonical_book.authors:
            # Пропускаем пустых авторов
            if not is_author_empty(author):
                authors_data.append(
                    AuthorDTO(
                        first_name=author.first_name or None,
                        last_name=author.last_name or None,
                        middle_name=author.middle_name or None,
                        nickname=None,  # TODO: добавить поддержку nickname в CanonicalAuthor
                    )
                )

        # Извлекаем серию из sequences с приоритизацией по номеру
        series_name = None
        series_number = None
        if canonical_book.sequences:
            selected_sequence = self._select_best_sequence(canonical_book.sequences)
            series_name = selected_sequence.name
            series_number = selected_sequence.number

        # 1. Собираем только РЕАЛЬНО дополнительные метаданные
        extra_metadata = {}
        # Словарь, который сделал трансформер
        raw_meta = canonical_book.raw_metadata

        if raw_meta.get("document_info"):
            extra_metadata["document_info"] = raw_meta["document_info"]

        if raw_meta.get("publish_info"):
            extra_metadata["publish_info"] = raw_meta["publish_info"]

        # Добавляем keywords, если они есть
        if raw_meta.get("title_info", {}).get("keywords"):
            extra_metadata["keywords"] = raw_meta["title_info"]["keywords"]

        # 2. Создаем DTO с очищенными метаданными
        book_dto = BookDTO(
            title=canonical_book.title,
            lang=canonical_book.lang,
            authors=authors_data,
            series=series_name,
            series_number=series_number,
            genres=canonical_book.genres,
            annotation=canonical_book.annotation_md,
            file_format=canonical_book.source_format,
            keywords=canonical_book.keywords,
            raw_metadata=extra_metadata,
            metadata_hash=metadata_hash,
            source_info=source_info,
        )

        return book_dto

    def _select_best_sequence(self, sequences):
        """Выбирает лучшую серию из списка с приоритизацией по номеру.

        Логика приоритизации:
        1. Предпочитаем серии с номером > 0 (реальные номера книг в серии)
        2. Если все серии имеют номер 0 или None - берем первую
        3. Логируем информацию о множественных сериях для мониторинга

        Args:
            sequences: Список CanonicalSequence

        Returns:
            CanonicalSequence: Выбранная серия
        """
        if len(sequences) == 1:
            return sequences[0]

        # Логируем информацию о множественных сериях
        series_info = [(s.name, s.number) for s in sequences]
        self.logger.debug(f"Найдено {len(sequences)} серий: {series_info}")

        # Ищем серии с номером > 0
        numbered_series = [s for s in sequences if s.number and s.number > 0]

        if numbered_series:
            selected = numbered_series[0]  # Берем первую с номером > 0
            self.logger.debug(f"Выбрана серия с номером > 0: '{selected.name}' (номер: {selected.number})")

            # Логируем потерянные серии для мониторинга
            lost_series = [s for s in sequences if s != selected]
            if lost_series:
                lost_info = [(s.name, s.number) for s in lost_series]
                self.logger.debug(f"Потеряны серии: {lost_info}")

            return selected
        else:
            # Все серии имеют номер 0 или None - берем первую
            selected = sequences[0]
            self.logger.debug(
                f"Все серии имеют номер 0/None, выбрана первая: '{selected.name}' (номер: {selected.number})"
            )

            # Логируем потерянные серии
            lost_series = sequences[1:]
            if lost_series:
                lost_info = [(s.name, s.number) for s in lost_series]
                self.logger.debug(f"Потеряны серии: {lost_info}")

            return selected

    def _build_source_info_from_canonical(
        self, task_data: dict[str, Any], canonical_book: CanonicalBook
    ) -> BookSourceInfo:
        """Создает BookSourceInfo из данных задачи и канонической модели"""
        return BookSourceInfo(
            source_type=task_data.get("source_type", 0),
            source_id=task_data.get("source_id", 0),
            file_path=task_data.get("file_path", ""),
            processed_at=task_data.get("_claimed_at"),
            file_format=canonical_book.source_format,
        )
