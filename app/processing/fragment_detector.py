# app/processing/fragment_detector.py

import logging
import re

from .dto import BookValidationContext

logger = logging.getLogger(__name__)

# Список ключевых фраз для обнаружения. Легко расширяется.
# Все фразы приводятся к нижнему регистру для регистронезависимого поиска.
FRAGMENT_MARKERS = [
    "конец ознакомительного фрагмента",
    "прочитайте эту книгу целиком",
    "текст предоставлен правообладателем",
    "конец бесплатного фрагмента",
    "купить полную версию",
    "полная версия книги",
    "ознакомительный фрагмент",
]


class FragmentDetector:
    """Компонент для обнаружения ознакомительных фрагментов книг."""

    def __init__(self):
        # Компилируем регулярное выражение для очистки текста перед проверкой
        self._clean_regex = re.compile(r"[\s\.,!?-]+")
        # Сохраняем последний найденный маркер для debug режима
        self._last_found_marker = None

    def _normalize_text(self, text: str) -> str:
        """Приводит текст к единому виду для надежного поиска."""
        # Убираем все знаки препинания и лишние пробелы, приводим к нижнему регистру
        return self._clean_regex.sub("", text.lower())

    @staticmethod
    def _extract_plain_text_from_elements(elements: list) -> str:
        """
        Максимально оптимизированное извлечение простого текста из FB2 элементов.

        Использует iterative подход вместо рекурсии для лучшей производительности
        и избежания stack overflow на глубоких структурах.

        Args:
            elements: Список FB2 элементов из content_elements

        Returns:
            Строка с извлеченным текстом
        """
        text_parts = []
        stack = list(elements)  # Создаем стек для итеративного обхода

        while stack:
            element = stack.pop()

            if isinstance(element, str):
                # Обычный текст
                text_parts.append(element)
            elif hasattr(element, "text") and isinstance(element.text, str):
                # Элементы форматирования с text атрибутом (Strong, Emphasis, Link, Note и т.д.)
                text_parts.append(element.text)
            elif hasattr(element, "content") and isinstance(element.content, list):
                # Paragraph, Subtitle, Section - добавляем content в стек
                stack.extend(element.content)
            elif hasattr(element, "elements") and isinstance(element.elements, list):
                # Cite, Poem, Annotation - добавляем elements в стек
                stack.extend(element.elements)
            # Игнорируем неизвестные типы элементов и Image

        return " ".join(filter(None, text_parts))

    def is_fragment(self, context: BookValidationContext) -> bool:
        """Проверяет, является ли книга фрагментом, анализируя последнюю главу.

        Args:
            context: Контекст валидации с предвычисленными метриками

        Returns:
            True если книга является фрагментом
        """
        if not context.has_chapters:
            return False

        # Используем предвычисленное содержимое последней главы из контекста
        content_to_check = context.last_chapter_content

        # Нормализуем текст для поиска
        normalized_content = self._normalize_text(content_to_check)

        # Ищем любую из маркерных фраз
        for marker in FRAGMENT_MARKERS:
            normalized_marker = self._normalize_text(marker)
            if normalized_marker in normalized_content:
                logger.debug(f"Найден маркер фрагмента: '{marker}'")
                self._last_found_marker = marker
                return True

        return False

    def get_fragment_reason(self, context: BookValidationContext) -> str:
        """Возвращает детальную причину, почему книга считается фрагментом.

        Args:
            context: Контекст валидации с предвычисленными метриками

        Returns:
            Строка с описанием причины
        """
        # Сначала проверяем является ли книга фрагментом
        if not self.is_fragment(context):
            return "Книга не является фрагментом"

        # Если проверка была выполнена ранее, используем сохраненный маркер
        if self._last_found_marker:
            return f"Фрагмент: найден маркер '{self._last_found_marker}'"

        return "Фрагмент: обнаружен неизвестный маркер"
