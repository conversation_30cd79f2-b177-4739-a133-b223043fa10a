# app/processing/parser_dispatcher.py

"""
🚨 ВАЖНО: Диспетчер парсеров УЖЕ ПОЛНОСТЬЮ РЕАЛИЗОВАН!

ParserDispatcher - центральная точка парсинга всех форматов книг.
- Определяет формат файла (расширение + magic bytes)
- Вызывает соответствующий парсер + трансформер
- Возвращает универсальную CanonicalBook модель

Текущий статус:
✅ FB2: Полностью реализован (fb2_parser + fb2_transformer)
🚧 EPUB: Планируется (заглушка готова)
🚧 MOBI: Планируется (заглушка готова)

НЕ ПЕРЕПИСЫВАЙ! Если нужны новые форматы - добавляй по аналогии с FB2.

См. doc/ПАРСИНГ_УЖЕ_РЕАЛИЗОВАН.md для деталей.
"""

import io
import logging
from pathlib import Path
from typing import Optional, Union

from .canonical_model import CanonicalBook
from .date_extractor import extract_best_date
from .error_handler import QuarantineError
from .parsers.fb2.fb2_canonical_transformer import FB2CanonicalTransformer
from .parsers.fb2.fb2_parser import FB2Parser
from .parsing_report import ParsingReport


class ParserDispatcher:
    """Определяет формат файла и вызывает соответствующий пайплайн
    (парсер + трансформер) для получения канонической модели книги.

    Архитектура:
    1. Определение формата файла (_detect_format)
    2. Вызов специализированного парсера для конкретного формата
    3. Трансформация в каноническую модель CanonicalBook
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # Импорт settings для настройки трансформеров
        from app import settings

        # Инициализируем парсеры и трансформеры для поддерживаемых форматов
        self.fb2_parser = FB2Parser()
        self.fb2_transformer = FB2CanonicalTransformer(
            min_chapters_threshold=settings.QUARANTINE_TRANSFORMER_HEURISTIC_THRESHOLD,
            min_chapter_length=settings.MIN_CHAPTER_LENGTH,
        )

        # В будущем здесь будут парсеры для других форматов:
        # self.epub_parser = EpubParser()
        # self.epub_transformer = EpubTransformer()
        # self.mobi_parser = MobiParser()
        # self.mobi_transformer = MobiTransformer()

    def parse_to_canonical(
        self,
        source: Union[Path, io.BytesIO],
        source_filename: Optional[str] = None,
        file_mtime: Optional[float] = None,
    ) -> tuple[CanonicalBook, ParsingReport]:
        """Главный метод парсинга. Принимает путь к файлу и возвращает
        универсальную CanonicalBook модель с отчетом о парсинге.

        Args:
            source: Путь к файлу книги или поток BytesIO
            source_filename: Имя файла (опционально)
            file_mtime: Время модификации файла (опционально)

        Returns:
            Tuple[CanonicalBook, ParsingReport]: Каноническая модель книги и отчет о парсинге

        Raises:
            QuarantineError: Если формат не поддерживается или файл поврежден

        """
        try:
            # Определяем имя источника и mtime
            if source_filename is None and isinstance(source, Path):
                source_filename = source.name

            display_name = source_filename or (source.name if isinstance(source, Path) else "<stream>")
            self.logger.debug(f"📖 Начинаем парсинг источника: {display_name}")

            # Если mtime не передан и источник — файл, определяем его
            if file_mtime is None and isinstance(source, Path):
                try:
                    file_mtime = source.stat().st_mtime
                except Exception:
                    file_mtime = None

            # Шаг 1: Определяем формат файла/потока
            file_format = self._detect_format(source, source_filename or display_name)
            self.logger.info(f"✅ Обнаружен формат: {file_format.upper()}. Запускаем соответствующий парсер.")

            # Шаг 2: Вызываем парсер для конкретного формата
            if file_format == "fb2":
                # FB2: парсим в специфичную модель, затем трансформируем в каноническую
                fb2_model = self.fb2_parser.parse(source)

                # Получаем статистику XML исправлений
                xml_fixes_stats = self.fb2_parser.get_xml_fixes_stats()

                # Извлекаем реальную дату публикации из FB2 метаданных
                publication_date_dt, _ = extract_best_date(fb2_model, file_mtime)
                publication_date = publication_date_dt.date()

                canonical_book, parsing_report = self.fb2_transformer.transform(
                    fb2_model, publication_date, xml_fixes_stats
                )

                # Сохраняем сырую FB2 модель для date_extractor
                canonical_book.raw_source_model = fb2_model

                self.logger.info(f"🎯 FB2 книга успешно преобразована в каноническую модель: {canonical_book.title}")
                return canonical_book, parsing_report

            # Заглушки для будущих форматов:
            # elif file_format == 'epub':
            #     epub_model = self.epub_parser.parse(file_path)
            #     canonical_book = self.epub_transformer.transform(epub_model)
            #     return canonical_book
            #
            # elif file_format == 'mobi':
            #     mobi_model = self.mobi_parser.parse(file_path)
            #     canonical_book = self.mobi_transformer.transform(mobi_model)
            #     return canonical_book

            else:
                raise QuarantineError(
                    f"Формат {file_format.upper()} распознан, но парсер еще не реализован. Поддерживаемые форматы: FB2"
                )

        except QuarantineError:
            raise
        except Exception as e:
            self.logger.error(f"❌ Критическая ошибка парсинга {source}: {e}", exc_info=True)
            raise QuarantineError(f"Не удалось распарсить файл: {e}") from e

    def _detect_format(self, source: Union[Path, io.BytesIO], filename: str) -> str:
        """Определяет формат файла книги по расширению и магическим байтам.

        Args:
            file_path: Путь к файлу

        Returns:
            str: Формат файла ('fb2', 'epub', 'mobi')

        Raises:
            QuarantineError: Если формат не определен или не поддерживается

        """
        extension = Path(filename).suffix.lower()

        # Маппинг расширений файлов
        ext_map = {
            ".fb2": "fb2",
            ".epub": "epub",
            ".mobi": "mobi",
            ".azw": "mobi",  # Amazon Kindle
            ".azw3": "mobi",  # Amazon Kindle
        }

        # Сначала проверяем по расширению
        if extension in ext_map:
            detected_format = ext_map[extension]
            self.logger.debug(f"🔍 Формат определен по расширению: {detected_format}")
            return detected_format

        # Если расширение не помогло, проверяем магические байты
        try:
            if isinstance(source, Path):
                with open(source, "rb") as f:
                    header = f.read(512)
            else:
                current_pos = source.tell()
                header = source.read(512)
                source.seek(current_pos)

                # FB2 (XML с FictionBook)
                if b"<?xml" in header and b"FictionBook" in header:
                    self.logger.debug("🔍 Формат определен по магическим байтам: FB2 (XML)")
                    return "fb2"

                # EPUB (ZIP архив с META-INF)
                if header.startswith(b"PK") and b"META-INF" in header:
                    self.logger.debug("🔍 Формат определен по магическим байтам: EPUB (ZIP)")
                    return "epub"

                # MOBI (Amazon Kindle)
                if b"BOOKMOBI" in header or b"TPZ" in header:
                    self.logger.debug("🔍 Формат определен по магическим байтам: MOBI")
                    return "mobi"

        except Exception as e:
            self.logger.warning(f"⚠️ Ошибка определения формата по содержимому файла {filename}: {e}")

        # Если ничего не определилось - ошибка
        raise QuarantineError(
            f"Не удалось определить формат файла: {filename}. Поддерживаемые расширения: {', '.join(ext_map.keys())}"
        )

    def get_supported_formats(self) -> list[str]:
        """Возвращает список поддерживаемых форматов.

        Returns:
            list[str]: Список форматов

        """
        return ["fb2"]  # В будущем: ['fb2', 'epub', 'mobi']

    def is_format_supported(self, file_format: str) -> bool:
        """Проверяет, поддерживается ли указанный формат.

        Args:
            file_format: Формат файла

        Returns:
            bool: True если формат поддерживается

        """
        return file_format.lower() in self.get_supported_formats()
