# app/processing/canonical_model.py

"""
🚨 ВАЖНО: Каноническая модель УЖЕ ПОЛНОСТЬЮ РЕАЛИЗОВАНА!

Эта модель является универсальным представлением книги для всех форматов (FB2/EPUB/MOBI).
Используется во всем пайплайне после парсинга.

НЕ ПЕРЕПИСЫВАЙ! Если нужны изменения - дорабатывай существующее.

Архитектура:
- CanonicalBook: основная модель с метаданными и главами
- CanonicalChapter: глава с заголовком и Markdown контентом
- CanonicalAuthor: информация об авторе
- CanonicalSequence: информация о серии

См. doc/ПАРСИНГ_УЖЕ_РЕАЛИЗОВАН.md для деталей.
"""

from dataclasses import dataclass, field
from datetime import date
from typing import TYPE_CHECKING, Any, Optional

# Предотвращаем циклический импорт с помощью TYPE_CHECKING
if TYPE_CHECKING:
    from .parsers.fb2.markdown_renderer import MarkdownRenderer

# --- Вспомогательные структуры для метаданных ---
# Эти классы остаются, так как они содержат базовую информацию об авторе.


@dataclass
class CanonicalAuthor:
    """Универсальное представление автора."""

    first_name: Optional[str]
    middle_name: Optional[str]
    last_name: Optional[str]
    nickname: Optional[str] = None


@dataclass
class CanonicalSequence:
    """Информация о серии книг."""

    name: str
    number: Optional[int] = None


# --- Ключевая структура для глав ---
# Реализует ленивый рендеринг: храним сырые элементы и рендерер,
# сам рендеринг происходит по требованию при обращении к content_md.


@dataclass
class CanonicalChapter:
    """Каноническое представление одной главы с ленивым рендерингом.

    Содержит заголовок и элементы для рендеринга. Фактический Markdown
    генерируется только при первом обращении к content_md и кэшируется.
    """

    title: str

    # Новые поля для ленивого рендеринга
    content_elements: list[Any] = field(default_factory=list, repr=False)
    _renderer: Optional["MarkdownRenderer"] = field(default=None, repr=False)

    # Кэш для результата рендеринга
    _md_cache: Optional[str] = field(default=None, init=False, repr=False)

    @property
    def content_md(self) -> str:
        """
        Ленивое свойство. Рендеринг в Markdown происходит только
        при первом обращении и результат кэшируется.
        """
        if self._md_cache is None:
            if self._renderer is not None:
                # Рендеринг вызывается здесь, по требованию!
                raw_content = self._renderer.convert_elements_to_markdown(self.content_elements)
                # Убираем дублирование заголовка в начале содержимого
                self._md_cache = self._remove_title_duplication(raw_content)
            else:
                # Fallback если нет рендерера
                self._md_cache = ""
        return self._md_cache

    @content_md.setter
    def content_md(self, value: str) -> None:
        """
        Setter для content_md. Обновляет кэш напрямую.
        Используется в pruner.py и других местах где нужно изменить содержимое.
        """
        self._md_cache = value

    def _remove_title_duplication(self, content: str) -> str:
        """
        Убирает дублирование заголовка главы в начале содержимого.

        Если содержимое начинается с точного совпадения заголовка,
        удаляет его для избежания избыточности.

        Args:
            content: Исходное содержимое главы в Markdown

        Returns:
            Очищенное содержимое без дублирования заголовка
        """
        if not content or not self.title:
            return content

        # Нормализуем заголовок для сравнения
        title_normalized = self.title.strip()
        if not title_normalized:
            return content

        # Проверяем, начинается ли содержимое с заголовка
        content_lines = content.split("\n")
        if not content_lines:
            return content

        first_line = content_lines[0].strip()

        # Если первая строка точно совпадает с заголовком
        if first_line == title_normalized:
            # Удаляем первую строку и следующую пустую строку (если есть)
            remaining_lines = content_lines[1:]

            # Убираем пустую строку после заголовка, если она есть
            if remaining_lines and remaining_lines[0].strip() == "":
                remaining_lines = remaining_lines[1:]

            return "\n".join(remaining_lines)

        return content

    def _fast_text_length_estimation(self, elements: list[Any]) -> int:
        """
        Максимально оптимизированная оценка длины текстового контента.

        Использует iterative подход вместо рекурсии и оптимизированную
        логику обхода для минимизации накладных расходов.

        Returns:
            Приблизительная длина текстового контента
        """
        total_length = 0
        stack = list(elements)  # Создаем стек для итеративного обхода

        while stack:
            element = stack.pop()

            if isinstance(element, str):
                # Обычный текст - добавляем длину
                total_length += len(element.strip())
            elif hasattr(element, "text") and isinstance(element.text, str):
                # Элементы с text атрибутом (Strong, Emphasis, Link, Note и т.д.)
                total_length += len(element.text.strip())
            elif hasattr(element, "content") and isinstance(element.content, list):
                # Paragraph, Subtitle, Section - добавляем content в стек
                stack.extend(element.content)
            elif hasattr(element, "elements") and isinstance(element.elements, list):
                # Cite, Annotation - добавляем elements в стек
                stack.extend(element.elements)
            elif hasattr(element, "stanzas") and isinstance(element.stanzas, list):
                # Poem - обрабатываем строфы
                for stanza in element.stanzas:
                    if hasattr(stanza, "lines") and isinstance(stanza.lines, list):
                        for line in stanza.lines:
                            if hasattr(line, "text") and isinstance(line.text, str):
                                total_length += len(line.text.strip())
                # Добавляем заголовок и автора стихотворения если есть
                if hasattr(element, "title") and element.title:
                    total_length += len(element.title.strip())
                if hasattr(element, "text_author") and element.text_author:
                    total_length += len(element.text_author.strip())
            elif hasattr(element, "paragraphs") and isinstance(element.paragraphs, list):
                # Cite - добавляем параграфы в стек
                stack.extend(element.paragraphs)
            elif hasattr(element, "lines") and isinstance(element.lines, list):
                # PoemStanza - обрабатываем строки стихотворения
                for line in element.lines:
                    if hasattr(line, "text") and isinstance(line.text, str):
                        total_length += len(line.text.strip())
            # Игнорируем неизвестные типы элементов и Image

        return total_length

    @property
    def estimated_content_length(self) -> int:
        """
        Быстрая оценка длины контента без рендеринга Markdown.

        Извлекает текст напрямую из FB2 элементов используя оптимизированный
        iterative алгоритм без накладных расходов на кэширование.

        Returns:
            Приблизительная длина текстового контента в символах
        """
        return self._fast_text_length_estimation(self.content_elements)

    @property
    def content_length(self) -> int:
        """Возвращает длину отрендеренного Markdown-контента.

        ВНИМАНИЕ: Это свойство вызывает дорогостоящий рендеринг!
        Для быстрой оценки используйте estimated_content_length.
        """
        return len(self.content_md)


# --- Основной канонический DTO ---
# Корневой объект, который будет сериализован в JSON.
# Содержит только согласованные нами поля.


@dataclass
class CanonicalBook:
    """Единое, универсальное представление книги, сфокусированное на текстовом
    содержимом для RAG-пайплайна. Поля упорядочены для оптимальной сериализации.
    """

    # --- Основные метаданные (в начале) ---
    title: str
    lang: str
    authors: list[CanonicalAuthor] = field(default_factory=list)
    translators: list[CanonicalAuthor] = field(default_factory=list)
    sequences: list[CanonicalSequence] = field(default_factory=list)
    publication_date: Optional[date] = None
    genres: list[str] = field(default_factory=list)
    keywords: list[str] = field(default_factory=list)

    # --- Информация об источнике ---
    source_id: Optional[int] = None
    source_type: Optional[int] = None

    # --- Дополнительные метаданные ---
    source_format: str = ""
    raw_metadata: dict[str, Any] = field(default_factory=dict)
    raw_source_model: Optional[Any] = None  # Сырая модель для date_extractor

    # --- Информация о дате для генерации book_id ---
    book_id_generation_date: Optional[str] = None  # Дата использованная для генерации book_id
    book_id_date_source: Optional[str] = None  # Источник даты (title-info, publish-info, document-info, file_mtime)

    # --- Содержимое (в конце) ---
    annotation_md: str = ""
    chapters: list[CanonicalChapter] = field(default_factory=list)

    def render_and_finalize_chapters(self) -> None:
        """Принудительно рендерит все главы книги в Markdown и кэширует результаты.

        ИСПОЛЬЗОВАНИЕ: Вызывается в ProductionBookProcessor для форсирования
        рендеринга всех глав перед сохранением в БД и создания артефактов.
        Обеспечивает корректную сериализацию и отсутствие lazy dependencies.

        ПРОИЗВОДИТЕЛЬНОСТЬ: Не влияет на аналитические инструменты, так как они
        используют get_canonical_book_for_anomaly_detection() без этого метода.
        """
        for chapter in self.chapters:
            # Принудительно вызываем рендеринг для каждой главы
            _ = chapter.content_md
