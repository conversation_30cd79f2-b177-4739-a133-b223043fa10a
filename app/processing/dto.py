from dataclasses import dataclass
from typing import Any, Optional

# Импорт для BookValidationContext
from .canonical_model import CanonicalBook


@dataclass
class AuthorDTO:
    """Типизированная структура данных автора.

    Заменяет нетипизированные словари для обеспечения типобезопасности
    на всем пути следования данных от парсинга до сохранения в БД.
    """

    first_name: Optional[str] = None
    last_name: Optional[str] = None
    middle_name: Optional[str] = None
    nickname: Optional[str] = None

    def get_full_name(self) -> str:
        """Возвращает полное имя автора."""
        parts = [self.last_name, self.first_name, self.middle_name]
        return " ".join(filter(None, parts)).strip()

    def get_metadata_dict(self) -> dict[str, Any]:
        """Возвращает метаданные автора для сохранения в БД."""
        metadata = {}

        full_name = self.get_full_name()
        if full_name:
            metadata["full_name"] = full_name

        if self.nickname:
            metadata["nickname"] = self.nickname.strip()

        return metadata


@dataclass
class BookSourceInfo:
    """Информация об источнике книги."""

    source_type: int
    source_id: int
    file_path: str
    processed_at: Optional[float] = None
    file_format: Optional[str] = None

    def to_metadata_dict(self) -> dict[str, Any]:
        """Возвращает метаданные источника как словарь для сериализации."""
        return {
            "file_path": self.file_path,
            "processed_at": self.processed_at,
            "file_format": self.file_format,
        }


@dataclass
class BookDTO:
    """Data Transfer Object для книги, используемый при сохранении в БД.

    Содержит все поля, необходимые для сохранения в базе данных.
    Использует типизированные структуры данных для обеспечения безопасности типов.
    """

    # Основные обязательные поля
    title: str
    lang: str
    authors: list[AuthorDTO]
    metadata_hash: str

    # Опциональные поля для серий
    series: Optional[str] = None
    series_number: Optional[int] = None

    # Дополнительные метаданные
    genres: Optional[list[str]] = None
    annotation: Optional[str] = None
    file_format: Optional[str] = None
    keywords: Optional[list[str]] = None
    raw_metadata: Optional[dict[str, Any]] = None

    # Информация об источнике
    source_info: Optional[BookSourceInfo] = None


def is_author_empty(author) -> bool:
    """Проверяет, является ли автор пустым.

    Автор считается пустым, если все его поля (first_name, last_name,
    middle_name, nickname) равны None или являются пустыми строками
    после очистки от пробелов.

    Args:
        author: Объект CanonicalAuthor или AuthorDTO

    Returns:
        True если автор пуст, False в противном случае
    """
    # Получаем значения полей и нормализуем их
    first_name = getattr(author, "first_name", None)
    last_name = getattr(author, "last_name", None)
    middle_name = getattr(author, "middle_name", None)
    nickname = getattr(author, "nickname", None)

    # Проверяем, что все поля пустые (None или пустые строки после strip)
    fields = [first_name, last_name, middle_name, nickname]
    for field in fields:
        if field is not None and field.strip():
            return False

    return True


@dataclass(frozen=True)
class BookValidationContext:
    """Контекст валидации книги с предвычисленными метриками.

    Этот объект создается один раз перед началом всех проверок и содержит
    как ссылку на исходную книгу, так и все предварительно вычисленные
    метрики для устранения избыточных вычислений в детекторах.

    Принцип "Вычислить один раз, использовать многократно":
    - Каждая метрика вычисляется только один раз в BookValidator
    - Все детекторы получают готовые данные через этот контекст
    - Неизменяемость (frozen=True) гарантирует целостность данных

    Attributes:
        book: Ссылка на исходную каноническую модель книги
        author_count: Количество авторов книги
        chapter_count: Количество глав в книге
        total_content_length: Общий объем контента всех глав (в символах)
        last_chapter_content: Текстовое содержимое последней главы для FragmentDetector
    """

    # Ссылка на исходную книгу
    book: CanonicalBook

    # Предвычисленные метрики для оптимизации
    author_count: int
    chapter_count: int
    total_content_length: int
    last_chapter_content: str

    # Дополнительные метрики для будущих детекторов
    has_chapters: bool
    avg_chapter_length: float

    @property
    def is_empty_book(self) -> bool:
        """Проверяет, является ли книга пустой (без глав)."""
        return self.chapter_count == 0

    @property
    def is_single_chapter(self) -> bool:
        """Проверяет, содержит ли книга только одну главу."""
        return self.chapter_count == 1

    @property
    def has_multiple_authors(self) -> bool:
        """Проверяет, есть ли у книги несколько авторов."""
        return self.author_count > 1
