# app/database/queries.py

import logging
from functools import wraps
from typing import Any, Optional, cast

import psycopg

from app.processing.error_handler import ErrorType, ProcessingError

from .connection import get_db_connection

# РЕФАКТОРИНГ ЗАВЕРШЕН: Все функции в этом файле используют декоратор @handle_db_errors
# для унификации обработки ошибок psycopg. Декоратор устраняет дублирование кода
# и обеспечивает консистентную обработку ошибок соединения, данных и SQL.


def handle_db_errors(operation_name: str = "database operation"):
    """Декоратор для унификации обработки ошибок psycopg в запросах к БД.

    Устраняет дублирование кода обработки ошибок во всех функциях queries.py.

    Args:
        operation_name: Описание операции для сообщений об ошибках

    Returns:
        Декорированная функция с обработкой стандартных ошибок psycopg
    """

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except psycopg.OperationalError as e:
                # Ошибки соединения, блокировки БД - можно повторить
                logging.error(f"❌ Ошибка соединения с БД при {operation_name}: {e}")
                raise ProcessingError(
                    f"Ошибка соединения с БД при {operation_name}: {e}",
                    error_type=ErrorType.RETRY,
                    details={
                        "error_category": "connection",
                        "operation": operation_name,
                    },
                ) from e
            except psycopg.DataError as e:
                # Ошибки данных (неверные параметры) - не повторять
                logging.error(f"❌ Ошибка данных при {operation_name}: {e}")
                raise ProcessingError(
                    f"Неверные параметры запроса при {operation_name}: {e}",
                    error_type=ErrorType.FATAL,
                    details={"error_category": "data", "operation": operation_name},
                ) from e
            except psycopg.ProgrammingError as e:
                # Ошибки SQL-синтаксиса - критическая ошибка кода
                logging.error(f"❌ Ошибка SQL при {operation_name}: {e}")
                raise ProcessingError(
                    f"Ошибка SQL-запроса при {operation_name}: {e}",
                    error_type=ErrorType.FATAL,
                    details={"error_category": "sql", "operation": operation_name},
                ) from e
            except psycopg.Error as e:
                # Прочие ошибки psycopg
                logging.error(f"❌ Ошибка БД при {operation_name}: {e}")
                raise ProcessingError(
                    f"Ошибка БД при {operation_name}: {e}",
                    error_type=ErrorType.RETRY,
                    details={"error_category": "database", "operation": operation_name},
                ) from e

        return wrapper

    return decorator


@handle_db_errors("проверке обработанности файла")
def is_source_processed(source_type: int, source_id: int) -> bool:
    """ЕДИНСТВЕННЫЙ источник правды о том, обработан ли файл.

    Проверяет наличие (source_type, source_id) в book_sources.
    Использует уникальный индекс uq_source_identity для быстрого поиска.

    ВАЖНО: Все модули должны использовать ТОЛЬКО эту функцию для проверки
    обработанных файлов. Никаких дублирующих проверок по file_path или другим критериям!

    Args:
        source_type: Тип источника (1=flibusta, 2=searchfloor, 3=anna)
        source_id: ID файла (только цифры)

    Returns:
        True если файл уже обработан

    Raises:
        ProcessingError: При ошибках соединения с БД (тип RETRY)

    """
    with get_db_connection() as conn:
        with conn.cursor() as cur:
            cur.execute(
                "SELECT 1 FROM book_sources WHERE source_type = %s AND source_id = %s LIMIT 1",
                (source_type, source_id),
            )
            return cur.fetchone() is not None


@handle_db_errors("проверке дубликатов книг")
def check_book_duplicates(metadata_hash: str) -> dict[str, str] | None:
    """Проверяет существование книги в БД по metadata_hash.
    Возвращает только сырые данные из БД без бизнес-логики.

    Args:
        metadata_hash: Хэш метаданных книги

    Returns:
        Dict с existing_book_id если найден дубликат, иначе None
    """
    with get_db_connection() as conn:
        with conn.cursor() as cur:
            # Простой запрос по единому хэшу
            cur.execute(
                "SELECT id FROM books WHERE metadata_hash = %s LIMIT 1",
                (metadata_hash,),
            )

            result = cur.fetchone()
            if result:
                row_dict = cast(dict[str, Any], result)
                return {
                    "existing_book_id": row_dict["id"],
                }

            # Дубликатов не найдено
            return None


@handle_db_errors("получении общего количества книг")
def get_books_total_count() -> int:
    """Возвращает общее количество книг в БД."""
    with get_db_connection() as conn:
        with conn.cursor() as cur:
            cur.execute("SELECT COUNT(*) as total FROM books")
            result = cur.fetchone()
            row_dict = cast(dict[str, Any], result)
            return row_dict["total"]


@handle_db_errors("проверке целостности данных")
def check_data_integrity() -> dict[str, int]:
    """Проверяет целостность данных в БД.

    ИСПРАВЛЕНО: Объединяет несколько отдельных запросов в один атомарный
    для повышения производительности и обеспечения консистентности данных.
    """
    stats: dict[str, int] = {
        "total_books": 0,
        "orphaned_sources": 0,
        "orphaned_authors": 0,
        "errors": 0,
    }

    with get_db_connection() as conn:
        with conn.cursor() as cur:
            # ОПТИМИЗИРОВАНО: Объединяем все проверки целостности в один атомарный запрос
            cur.execute(
                """
                SELECT 
                    -- Общее количество книг
                    (SELECT COUNT(*) FROM books) as total_books,
                    
                    -- Источники без связанных книг (orphaned book_sources)
                    (SELECT COUNT(*) 
                     FROM book_sources bs
                     WHERE NOT EXISTS (SELECT 1 FROM books b WHERE b.id = bs.book_id)
                    ) as orphaned_sources,
                    
                    -- Авторы без связанных книг (orphaned authors)
                    (SELECT COUNT(*) 
                     FROM authors a
                     WHERE NOT EXISTS (SELECT 1 FROM book_authors ba WHERE ba.author_id = a.id)
                    ) as orphaned_authors
                """
            )
            result = cur.fetchone()

            if result:
                row_dict = cast(dict[str, Any], result)
                stats["total_books"] = row_dict["total_books"]
                stats["orphaned_sources"] = row_dict["orphaned_sources"]
                stats["orphaned_authors"] = row_dict["orphaned_authors"]

            return stats


@handle_db_errors("добавлении в карантин")
def add_to_quarantine(source_type: int, source_id: int, quarantine_type: str, reason: str, details: dict) -> None:
    """Добавляет книгу в карантин с записью в PostgreSQL.

    Использует INSERT ... ON CONFLICT DO NOTHING для защиты от повторных записей.
    PostgreSQL становится единственным источником правды о карантинном статусе.

    Args:
        source_type: Тип источника (1=flibusta, 2=searchfloor, 3=anna)
        source_id: ID файла в источнике
        quarantine_type: Основной тип карантина ('trial', 'small_content', 'anthology', etc.)
        reason: Человекочитаемая причина помещения в карантин
        details: JSON с task_data и detected_anomalies

    Raises:
        ProcessingError: При ошибках соединения с БД (тип RETRY)
    """
    with get_db_connection() as conn:
        with conn.cursor() as cur:
            cur.execute(
                """
                INSERT INTO quarantined_books
                (source_type, source_id, primary_quarantine_type, reason, details)
                VALUES (%s, %s, %s, %s, %s)
                ON CONFLICT (source_type, source_id) DO NOTHING
                """,
                (source_type, source_id, quarantine_type, reason, details),
            )

            # Логируем только если была выполнена вставка (не конфликт)
            if cur.rowcount > 0:
                logging.info(f"📋 Книга {source_type}:{source_id} добавлена в карантин (тип: {quarantine_type})")
            else:
                logging.debug(f"📋 Книга {source_type}:{source_id} уже была в карантине (тип: {quarantine_type})")


@handle_db_errors("получении карантинных книг")
def get_all_quarantined_books(
    filter_type: Optional[str] = None, since_days: Optional[int] = None
) -> list[dict[str, Any]]:
    """Возвращает книги из карантина для анализа и исправления с опциональными фильтрами.

    Используется в run_anomaly_analyzer.py для режимов Тюнинг и Исправление.
    Фильтрация выполняется на стороне PostgreSQL для максимальной производительности.

    Args:
        filter_type: Фильтр по типу карантина (например, "trial", "small_content")
        since_days: Фильтр по времени - книги обновленные за последние N дней

    Returns:
        Список словарей с данными карантинных книг:
        [
            {
                "source_type": 1,
                "source_id": 12345,
                "primary_quarantine_type": "trial",
                "reason": "Обнаружен ознакомительный фрагмент",
                "details": {...},
                "updated_at": "2024-01-01T12:00:00+00:00"
            },
            ...
        ]

    Raises:
        ProcessingError: При ошибках соединения с БД (тип RETRY)
    """
    with get_db_connection() as conn:
        with conn.cursor() as cur:
            # Строим параметризованный запрос с фильтрами
            base_query = """
                SELECT source_type, source_id, primary_quarantine_type,
                       reason, details, updated_at
                FROM quarantined_books
            """

            conditions = []
            params = []

            if filter_type:
                conditions.append("primary_quarantine_type = %s")
                params.append(filter_type)

            if since_days:
                # Безопасная параметризация интервала через PostgreSQL INTERVAL
                # Передаем количество дней как отдельный параметр для make_interval()
                if not isinstance(since_days, int) or since_days <= 0:
                    raise ValueError(f"since_days должен быть положительным int, получен: {since_days}")
                conditions.append("updated_at >= NOW() - make_interval(days => %s)")
                params.append(str(since_days))

            # Собираем финальный запрос
            if conditions:
                query = base_query + " WHERE " + " AND ".join(conditions)
            else:
                query = base_query

            query += " ORDER BY updated_at DESC"

            cur.execute(query, params)

            quarantined_books = []
            for row in cur.fetchall():
                row_dict = cast(dict[str, Any], row)
                quarantined_books.append(
                    {
                        "source_type": row_dict["source_type"],
                        "source_id": row_dict["source_id"],
                        "primary_quarantine_type": row_dict["primary_quarantine_type"],
                        "reason": row_dict["reason"],
                        "details": row_dict["details"],
                        "updated_at": row_dict["updated_at"],
                    }
                )

            # Логируем с указанием примененных фильтров
            filter_info = []
            if filter_type:
                filter_info.append(f"тип: {filter_type}")
            if since_days:
                filter_info.append(f"за {since_days} дней")

            filter_str = f" (фильтры: {', '.join(filter_info)})" if filter_info else ""
            logging.debug(f"📋 Получено {len(quarantined_books)} книг из карантина{filter_str}")
            return quarantined_books


@handle_db_errors("удалении из карантина")
def delete_from_quarantine(source_type: int, source_id: int) -> bool:
    """Удаляет книгу из карантина для повторной обработки.

    Используется в run_anomaly_analyzer.py в режиме Исправление
    для книг, которые больше не имеют блокирующих аномалий.

    Args:
        source_type: Тип источника (1=flibusta, 2=searchfloor, 3=anna)
        source_id: ID файла в источнике

    Returns:
        True если книга была удалена, False если её не было в карантине

    Raises:
        ProcessingError: При ошибках соединения с БД (тип RETRY)
    """
    with get_db_connection() as conn:
        with conn.cursor() as cur:
            cur.execute(
                """
                DELETE FROM quarantined_books
                WHERE source_type = %s AND source_id = %s
                """,
                (source_type, source_id),
            )

            was_deleted = cur.rowcount > 0

            if was_deleted:
                logging.info(f"📋 Книга {source_type}:{source_id} удалена из карантина")
            else:
                logging.debug(f"📋 Книга {source_type}:{source_id} не была в карантине")

            return was_deleted


@handle_db_errors("проверке карантина")
def get_quarantined_ids(candidate_pairs: list[tuple[int, int]]) -> set[tuple[int, int]]:
    """Проверяет, какие из кандидатов уже находятся в карантине.

    Выполняет пакетный SELECT для оптимизации производительности при больших объемах.
    Используется сканером для фильтрации уже помещенных в карантин книг.

    Args:
        candidate_pairs: Список пар (source_type, source_id) для проверки

    Returns:
        Set пар (source_type, source_id), которые найдены в карантине

    Raises:
        ProcessingError: При ошибках соединения с БД (тип RETRY)
    """
    if not candidate_pairs:
        return set()

    with get_db_connection() as conn:
        with conn.cursor() as cur:
            # Используем IN для идиоматичного Python-стиля и лучшей переносимости
            # psycopg автоматически преобразует это в оптимальный PostgreSQL SQL
            cur.execute(
                """
                SELECT source_type, source_id
                FROM quarantined_books
                WHERE (source_type, source_id) IN %s
                """,
                (candidate_pairs,),
            )

            # Возвращаем set для быстрой проверки принадлежности
            quarantined_pairs = set()
            for row in cur.fetchall():
                row_dict = cast(dict[str, Any], row)
                quarantined_pairs.add((row_dict["source_type"], row_dict["source_id"]))

            logging.debug(
                f"📋 Проверено {len(candidate_pairs)} кандидатов, найдено {len(quarantined_pairs)} в карантине"
            )

            return quarantined_pairs
