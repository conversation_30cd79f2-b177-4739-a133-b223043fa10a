# app/database/connection.py

import threading
from contextlib import contextmanager
from typing import Any, Iterator

import psycopg
from psycopg.rows import dict_row
from psycopg_pool import ConnectionPool

from app import settings


def configure_connection(conn: psycopg.Connection[Any]) -> None:
    """Настройка соединения для работы с dict_row."""
    conn.row_factory = dict_row


# Глобальная переменная для хранения пула (None до первого обращения)
_pool: ConnectionPool | None = None

# Блокировка для потокобезопасной инициализации пула
_pool_lock = threading.Lock()


def get_pool() -> ConnectionPool:
    """Потокобезопасный лениво-инициализируемый синглтон пула соединений.

    Создает пул только при первом обращении, что предотвращает проблемы
    с наследованием состояния в ProcessPoolExecutor.

    ПОТОКОБЕЗОПАСНОСТЬ: Использует double-checked locking для предотвращения
    состояния гонки при одновременной инициализации из нескольких потоков.
    Критично важно для будущего использования ThreadPoolExecutor.
    """
    global _pool

    # Первая проверка без блокировки (быстрый путь для уже инициализированного пула)
    if _pool is not None:
        return _pool

    # Блокируем доступ для инициализации
    with _pool_lock:
        # Вторая проверка под блокировкой (может быть инициализирован другим потоком)
        if _pool is None:
            _pool = ConnectionPool(
                conninfo=settings.DATABASE_URL,
                min_size=settings.DB_POOL_MIN_SIZE,
                max_size=settings.DB_POOL_MAX_SIZE,
                configure=configure_connection,
            )

    return _pool


@contextmanager
def get_db_connection() -> Iterator[psycopg.Connection]:
    """Контекстный менеджер для получения соединения из пула.
    Гарантирует, что соединение будет возвращено в пул после использования.

    Пример использования:
    with get_db_connection() as conn:
        with conn.cursor() as cur:
            cur.execute("SELECT 1")
    """
    pool = get_pool()  # Получаем пул через функцию
    conn = pool.getconn()
    try:
        yield conn
    finally:
        pool.putconn(conn)
