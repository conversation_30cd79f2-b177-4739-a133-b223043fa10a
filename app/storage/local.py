# app/storage/local.py
"""
Локальный StorageManager с оптимизированным I/O для ZIP архивов.

КРИТИЧЕСКИЕ ОПТИМИЗАЦИИ 2025-07-15:
- read_file_from_archive(): Устранена проблема полного чтения архива в память.
  Теперь zipfile читает только нужный файл без лишнего I/O.
- list_books_in_archive(): Используется namelist() вместо infolist() для лучшей производительности.

Эти изменения исправляют фундаментальную ошибку архитектуры I/O, которая
приводила к переполнению памяти и деградации производительности до 1-3 книг/сек.
"""

import io
import logging
import zipfile
from pathlib import Path
from typing import Iterator

from .base import (
    ArchiveMetadata,
    StorageAccessError,
    StorageCorruptionError,
    StorageManager,
)

logger = logging.getLogger(__name__)


class LocalStorageManager(StorageManager):
    """Реализация StorageManager для локальной файловой системы."""

    def __init__(self):
        self.storage_type = "local"

    def list_archives(self, source_path: str) -> Iterator[str]:
        """Возвращает итератор путей ко всем ZIP архивам в локальной директории.

        Args:
            source_path: Путь к локальной директории

        Yields:
            str: Абсолютный путь к архиву
        """
        try:
            source_dir = Path(source_path)

            if not source_dir.exists():
                raise StorageAccessError(
                    f"Директория не существует: {source_path}",
                    storage_type=self.storage_type,
                    path=source_path,
                )

            if not source_dir.is_dir():
                raise StorageAccessError(
                    f"Путь не является директорией: {source_path}",
                    storage_type=self.storage_type,
                    path=source_path,
                )

            # Сканируем только корень директории (НЕ рекурсивно)
            for archive_path in source_dir.glob("*.zip"):
                if archive_path.is_file():
                    yield str(archive_path.absolute())

        except (OSError, PermissionError) as e:
            raise StorageAccessError(
                f"Ошибка доступа к директории: {e}",
                storage_type=self.storage_type,
                path=source_path,
            ) from e

    def get_archive_metadata(self, archive_path: str) -> ArchiveMetadata:
        """Получает метаданные локального архива.

        Args:
            archive_path: Путь к локальному архиву

        Returns:
            ArchiveMetadata: Метаданные архива
        """
        try:
            path = Path(archive_path)

            if not path.exists():
                raise StorageAccessError(
                    f"Архив не найден: {archive_path}",
                    storage_type=self.storage_type,
                    path=archive_path,
                )

            stat = path.stat()

            return ArchiveMetadata(
                archive_path=archive_path,
                archive_mtime=stat.st_mtime,
                size_bytes=stat.st_size,
            )

        except (OSError, PermissionError) as e:
            raise StorageAccessError(
                f"Ошибка доступа к архиву: {e}",
                storage_type=self.storage_type,
                path=archive_path,
            ) from e

    def list_books_in_archive(self, archive_path: str) -> list[str]:
        """Возвращает список книжных файлов в локальном ZIP архиве.

        Args:
            archive_path: Путь к локальному архиву

        Returns:
            list[str]: Список имен файлов (например, ['12345.fb2'])
        """
        try:
            path = Path(archive_path)

            if not path.exists():
                raise StorageAccessError(
                    f"Архив не найден: {archive_path}",
                    storage_type=self.storage_type,
                    path=archive_path,
                )

            # ОПТИМИЗАЦИЯ: Используем namelist() вместо infolist() для быстрого доступа
            # ОПТИМИЗАЦИЯ: Используем set для O(1) проверки расширений вместо O(n) итерации
            book_extensions = {".fb2", ".epub", ".txt"}
            with zipfile.ZipFile(path, "r") as zip_file:
                book_files = [
                    Path(name).name
                    for name in zip_file.namelist()
                    if not name.endswith("/")  # Игнорируем директории
                    and not name.startswith(".")  # Игнорируем системные файлы
                    and Path(name).suffix.lower() in book_extensions
                ]

            logger.debug(f"Найдено {len(book_files)} книжных файлов в {Path(archive_path).name}")
            return book_files

        except zipfile.BadZipFile as e:
            raise StorageCorruptionError(
                f"Поврежденный ZIP архив: {e}",
                storage_type=self.storage_type,
                path=archive_path,
            ) from e
        except (OSError, PermissionError) as e:
            raise StorageAccessError(
                f"Ошибка чтения архива: {e}",
                storage_type=self.storage_type,
                path=archive_path,
            ) from e

    def read_file_from_archive(self, archive_path: str, file_in_archive: str) -> io.BytesIO:
        """Читает конкретный файл из локального ZIP архива.

        ОПТИМИЗИРОВАНО: Не загружает весь архив в память, использует
        прямое чтение файла через zipfile без лишнего I/O.

        Args:
            archive_path: Путь к локальному архиву
            file_in_archive: Имя файла внутри архива

        Returns:
            io.BytesIO: Поток байт содержимого файла

        Raises:
            StorageAccessError: Если архив не найден или недоступен
            StorageCorruptionError: При поврежденном архиве
        """
        try:
            path = Path(archive_path)

            if not path.exists():
                raise StorageAccessError(
                    f"Архив не найден: {archive_path}",
                    storage_type=self.storage_type,
                    path=archive_path,
                )

            # ОПТИМИЗАЦИЯ: Работаем с архивом напрямую без загрузки в память
            with zipfile.ZipFile(path, "r") as zip_file:
                if file_in_archive not in zip_file.namelist():
                    raise StorageAccessError(
                        f"Файл '{file_in_archive}' не найден в архиве {archive_path}",
                        storage_type=self.storage_type,
                        path=file_in_archive,
                    )

                # Читаем только нужный файл, не весь архив
                file_content = zip_file.read(file_in_archive)
                return io.BytesIO(file_content)

        except zipfile.BadZipFile as e:
            raise StorageCorruptionError(
                f"Поврежденный ZIP архив: {archive_path}",
                storage_type=self.storage_type,
                path=archive_path,
            ) from e
        except (OSError, PermissionError) as e:
            raise StorageAccessError(
                f"Ошибка доступа к архиву: {e}",
                storage_type=self.storage_type,
                path=archive_path,
            ) from e
