# Документация проекта "Литературный Компас"

## 📋 Оглавление документации

### 🎯 Основные документы
- **[passport.md](passport.md)** - Паспорт проекта с видением и бизнес-требованиями
- **[CHANGELOG.md](CHANGELOG.md)** - ⭐ История изменений и текущий статус
- **[roadmap.md](roadmap.md)** - План реализации и архитектурные принципы

### 🛠️ Техническая документация  
- **[architecture/parsing_architecture.md](architecture/parsing_architecture.md)** - Архитектура модульного парсинга
- **[operations/pipeline01.md](operations/pipeline01.md)** - Сканер-продюсер источников книг
- **[operations/pipeline00.md](operations/pipeline00.md)** - Система восстановления и мониторинга
- **[operations/quarantine.md](operations/quarantine.md)** - Система карантина и обработка проблемных файлов

## 🏗️ Архитектура системы

### Компоненты Ingestion Pipeline
1. **Сканер-продюсер** (`run_10_scan_sources.py`) - поиск новых файлов
2. **Воркер-потребитель** (`run_20_process_book_worker.py`) - обработка с модульным парсингом
3. **Система карантина** - изоляция и дифференцированная обработка проблемных файлов
4. **Система восстановления** (`run_00_recovery.py`) - мониторинг и восстановление

### 🆕 Модульная архитектура парсинга
- **ParserDispatcher** - определение формата и диспетчеризация
- **FB2Parser** + **FB2CanonicalTransformer** - обработка FB2 в каноническую модель
- **HashComputer** - оптимизированное хэширование с MD5 UUID
- **CanonicalBook** - универсальная модель для всех форматов

## 🚀 Быстрый старт

```bash
# Сканирование новых книг
python run_10_scan_sources.py

# Обработка воркером (с новой архитектурой)
python run_20_process_book_worker.py

# Аудит целостности состояния системы
python run_00_recovery.py --verbose
```

## 📖 Рекомендуемый порядок чтения

1. **[passport.md](passport.md)** - общее понимание проекта и бизнес-целей
2. **[CHANGELOG.md](CHANGELOG.md)** - текущий статус и последние изменения
3. **[architecture/parsing_architecture.md](architecture/parsing_architecture.md)** - детали архитектуры парсинга
4. **[operations/pipeline01.md](operations/pipeline01.md)** - как работает сканер
5. **[operations/quarantine.md](operations/quarantine.md)** - система карантина (для операторов)
6. **[roadmap.md](roadmap.md)** - планы развития
7. **[operations/pipeline00.md](operations/pipeline00.md)** - восстановление и отладка

---

**Последнее обновление:**
**См. подробности:** [CHANGELOG.md](CHANGELOG.md) 