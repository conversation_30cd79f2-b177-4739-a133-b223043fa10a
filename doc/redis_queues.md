# Redis Queues - единый документ, описывающий всю систему очередей Redis.

Общая логика системы статусов `[базовый_статус_завершения]` + `1` для "в процессе" и `100+` для ошибок — это отличный паттерн.

## Архитектура очередей и статусов обработки

Система использует гибридный подход: **Redis** как быстрый диспетчер задач и **PostgreSQL** как долгосрочный источник правды о состоянии обработки.

**ВАЖНО (2024):** Новая версия Scanner-Inventorizer полностью заменила кэш-зависимую логику. PostgreSQL (`book_sources`) является единственным источником правды об обработанных книгах. SET_PROCESSED больше не используется для дедупликации.

## Стандартизированная схема именования

**Формат:** `books:queue:{pipeline_num}_{stage_name}_{status}`

Где:
- `pipeline_num`: Номер pipeline (20, 30, 40, 50)
- `stage_name`: Название этапа (parsing, chunking, enrich, vectorize)
- `status`: Состояние очереди (new, processing)

### Преимущества стандартизации:
1. **Естественная сортировка** в Redis при просмотре ключей
2. **Самодокументируемость** - имя полностью описывает назначение
3. **Масштабируемость** - легко добавлять новые pipeline
4. **Мониторинг** - упрощенная группировка по этапам

## Система очередей (Полный жизненный цикл)

### Основные очереди Redis

| Имя очереди | Pipeline | Назначение | Структура данных | Источник | Потребитель |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **`books:queue:20_parsing_new`** | 20: Parser | Новые задачи для парсинга | `{"source_type": 1, "source_id": 826222, "archive_path": "/path/to/archive_826222.zip", "book_filename": "826222.fb2", "archive_mtime": 1640995200.123}` | `scanner_inventorizer.py` | `run_20_process_book_worker.py` |
| **`books:queue:20_parsing_processing`** | 20: Parser | Задачи парсинга в работе | То же + `{"_claimed_at": timestamp, "_raw_task": "..."}` | `TaskQueueManager.claim_task()` | `TaskQueueManager.complete_task()` |
| **`books:queue:30_chunking_new`** | 30: Chunker | Готовые к чанкингу книги | `{"book_id": "uuid-v7-...", "task_type": "chunking", "created_at": timestamp}` | `BookProcessor.enqueue_rag_task()` | Будущий Chunker воркер |
| **`books:queue:30_chunking_processing`** | 30: Chunker | Задачи чанкинга в работе | То же + `{"_claimed_at": timestamp}` | Chunker воркер | Chunker воркер |
| **`books:queue:40_enrich_new`** | 40: Enricher | Готовые к обогащению книги | `{"book_id": "uuid-v7-...", "task_type": "enrich", "created_at": timestamp}` | Chunker воркер | Enricher воркер |
| **`books:queue:40_enrich_processing`** | 40: Enricher | Задачи обогащения в работе | То же + `{"_claimed_at": timestamp}` | Enricher воркер | Enricher воркер |
| **`books:queue:50_vectorize_new`** | 50: Vectorizer | Готовые к векторизации книги | `{"book_id": "uuid-v7-...", "task_type": "vectorize", "created_at": timestamp}` | Enricher воркер | Vectorizer воркер |
| **`books:queue:50_vectorize_processing`** | 50: Vectorizer | Задачи векторизации в работе | То же + `{"_claimed_at": timestamp}` | Vectorizer воркер | Vectorizer воркер |
| **`books:queue:completed`** | Все | Архив завершенных задач | Любой тип + `{"_completed_at": timestamp}` | Любой воркер | Только логирование |

### Системные множества (SET)

| Имя множества | Назначение | Структура ключей | Операции |
| :--- | :--- | :--- | :--- |
| **`books:set:processed`** | ⚠️ УСТАРЕЛ: Кэш обработанных файлов | `"processed:{source_type}:{source_id}"` | Заменен на прямые PostgreSQL запросы в scanner_inventorizer.py |
| **`books:set:queued_ids`** | Быстрая проверка активных задач | `"{source_type}:{source_id}"` | O(1) проверка наличия в parsing очередях |

### API TaskQueueManager

#### Основные операции

```python
from app.processing.queue_manager import TaskQueueManager

queue_manager = TaskQueueManager()

# Захват задачи парсинга (атомарный BRPOPLPUSH)
task = queue_manager.claim_task(timeout=10)
# Redis: BRPOPLPUSH books:queue:20_parsing_new books:queue:20_parsing_processing 10

# Завершение задачи (атомарная транзакция)
success = queue_manager.complete_task(task_data)
# Redis Pipeline:
# - LREM books:queue:20_parsing_processing 1 task
# - LPUSH books:queue:completed task  
# - LTRIM books:queue:completed 0 99999 (ограничение размера)
# - SREM books:set:queued_ids "type:id"

# Возврат в очередь для повтора
queue_manager.return_task_to_queue(task_data)

# Финализация с ошибкой
queue_manager.finalize_task(task_data, "error reason")

# Постановка задачи в следующий pipeline (чанкинг)
queue_manager.enqueue_rag_task(book_id)
# Redis: LPUSH books:queue:30_chunking_new '{"book_id": "...", "task_type": "rag_processing"}'

# Статистика
stats = queue_manager.get_queue_stats()
# {"new_tasks": 150, "processing_tasks": 3, "completed_tasks": 1247, "cached_processed": 5420}
```

#### Восстановление системы

```python
# Поиск зависших задач (старше WORKER_TIMEOUT)
stale_tasks = queue_manager.find_stale_tasks()

# Восстановление SET из очередей  
from app.ingestion.scanner import rebuild_queued_set
rebuild_queued_set(redis_client)
```

### Конфигурация очередей

Все имена очередей определены в `app/settings.py`:

```python
# Redis Queue Names - Стандартизированная схема именования
# 📋 Полная документация: doc/redis_queues.md

# Parsing Pipeline (20)
QUEUE_PARSING_NEW = "books:queue:20_parsing_new"
QUEUE_PARSING_PROCESSING = "books:queue:20_parsing_processing"

# RAG Pipelines
QUEUE_CHUNKING_NEW = "books:queue:30_chunking_new"
QUEUE_CHUNKING_PROCESSING = "books:queue:30_chunking_processing"

QUEUE_ENRICH_NEW = "books:queue:40_enrich_new"
QUEUE_ENRICH_PROCESSING = "books:queue:40_enrich_processing"

QUEUE_VECTORIZE_NEW = "books:queue:50_vectorize_new"
QUEUE_VECTORIZE_PROCESSING = "books:queue:50_vectorize_processing"

# Системные вспомогательные структуры
QUEUE_COMPLETED = "books:queue:completed"  # Архив завершенных задач
SET_PROCESSED = "books:set:processed"  # ⚠️ УСТАРЕЛ: Кэш обработанных файлов (заменен на PostgreSQL)
SET_QUEUED_IDS = "books:set:queued_ids"  # Быстрая проверка активных задач parsing (O(1))

# Производительность
WORKER_TIMEOUT = 300  # Секунд до признания задачи зависшей
COMPLETED_QUEUE_MAX_SIZE = 100000  # Максимум записей в completed
```

### Требования к Redis

| Параметр | Минимум | Рекомендуется | Обоснование |
| :--- | :--- | :--- | :--- |
| **Версия Redis** | 2.2+ | 6.0+ | BRPOPLPUSH доступен с 2.2, современные фичи в 6.0+ |
| **Память** | 100MB | 512MB+ | Очереди и SET_QUEUED_IDS (SET_PROCESSED больше не используется) |
| **Persistence** | Опционально | RDB каждые 15 мин | Восстановление после рестарта |

**Совместимость с облачными провайдерами:**
- ✅ AWS ElastiCache (Redis 6.0+)  
- ✅ Azure Cache for Redis (5.0+)
- ✅ Google Cloud Memorystore (6.0+)
- ✅ Redis Cloud (любая версия)
- ✅ Локальный Redis в Docker (7.0+)

```

### Паттерны использования

#### 1. Сканирование источников (Pipeline 20) - НОВАЯ ВЕРСИЯ
```python
# НОВАЯ ЛОГИКА: Пакетная дедупликация через ScannerInventorizer
from app.ingestion.scanner_inventorizer import ScannerInventorizer

scanner = ScannerInventorizer()
stats = scanner.scan_all_sources()

# Внутри Scanner использует:
# 1. Сбор всех кандидатов из архивов через StorageManager
# 2. Пакетные проверки Redis SET_QUEUED_IDS (размер пакета: 5000)
# 3. Пакетные запросы PostgreSQL book_sources (размер пакета: 1000)
# 4. Атомарное добавление задач с полными метаданными архива

# Новый формат задач:
task_data = {
    "source_type": book.source_type,
    "source_id": book.source_id,
    "archive_path": book.archive_path,
    "book_filename": book.book_filename,
    "archive_mtime": book.archive_mtime,
}

# УСТАРЕВШАЯ ЛОГИКА (больше не используется):
# - Кэш SET_PROCESSED заменен на прямые PostgreSQL запросы
# - Восстановление путей заменено на полные метаданные в задачах
```

#### 2. Обработка воркером парсинга (Pipeline 20)
```python
# Захват с таймаутом
task = queue_manager.claim_task(timeout=10)
if not task:
    continue  # Очередь пуста

try:
    # Бизнес-логика парсинга
    result = book_processor.process_book(task)
    
    # Атомарное завершение + постановка в следующий pipeline
    queue_manager.complete_task(task)
    queue_manager.enqueue_rag_task(result.book_id)  # → chunking_new
    
except RetryableError:
    # ИСПРАВЛЕНО: Активный возврат в очередь для немедленного повтора
    success = queue_manager.return_task_to_queue(task)
    if not success:
        # При провале возврата - задача остается в processing для recovery
        logger.error("Не удалось вернуть задачу в очередь, остается в processing")
    
except FatalError as e:
    # Финализация с ошибкой (критические/неустранимые ошибки)
    queue_manager.finalize_task(task, str(e))
```

#### 3. Обработка воркером RAG pipeline (30, 40, 50)
```python
# Пример для воркера чанкинга (Pipeline 30)
# Используем BRPOPLPUSH для атомарного перемещения (см. архитектурное решение выше)
task = redis_client.brpoplpush(QUEUE_CHUNKING_NEW, QUEUE_CHUNKING_PROCESSING, timeout=10)

# Атомарный захват в PostgreSQL
with db_connection:
    rows_affected = db.execute(
        "UPDATE books SET process_status = 31 WHERE id = %s AND process_status IN (20, 31)",
        [book_id]
    )
    if rows_affected == 0:
        # Задача уже захвачена другим воркером
        return

try:
    # Бизнес-логика чанкинга
    chunking_result = chunker.process_book(book_id)
    
    # Успешное завершение
    db.execute("UPDATE books SET process_status = 30 WHERE id = %s", [book_id])
    redis_client.lpush(QUEUE_ENRICH_NEW, json.dumps({"book_id": book_id}))
    
except Exception as e:
    # Неустранимая ошибка
    db.execute("UPDATE books SET process_status = 132 WHERE id = %s", [book_id])
```

## Логика для воркеров RAG pipeline (Pipeline 30, 40, 50)

Это стандартный шаблон, который используется для **каждого** воркера RAG pipeline.

### Общий алгоритм для любого RAG воркера:

1.  **Захват задачи из Redis:** Воркер ждет задачу в соответствующей очереди:
    *   **Воркер чанкинга (Pipeline 30):** `books:queue:30_chunking_new`
    *   **Воркер обогащения (Pipeline 40):** `books:queue:40_enrich_new`
    *   **Воркер векторизации (Pipeline 50):** `books:queue:50_vectorize_new`

2.  **Атомарное перемещение в processing:** Redis BRPOPLPUSH для атомарности
    ```python
    # АРХИТЕКТУРНОЕ РЕШЕНИЕ: BRPOPLPUSH vs BLMOVE
    # 
    # Используем BRPOPLPUSH вместо современного BLMOVE по следующим причинам:
    # - BRPOPLPUSH: универсальная поддержка Redis 2.2+ (включая облачные провайдеры)
    # - BLMOVE: требует Redis 6.2+ (может ограничить развертывание)
    # - Идентичная атомарность и производительность
    # - BRPOPLPUSH - проверенный временем стандарт message queues
    # - Стабильность > модернизация ради модернизации
    
    task = redis_client.brpoplpush(
        source_queue,  # 30_chunking_new, 40_enrich_new, 50_vectorize_new
        processing_queue,  # 30_chunking_processing, 40_enrich_processing, 50_vectorize_processing
        timeout=10
    )
    ```

3.  **Атомарный захват в PostgreSQL (Ключевой шаг!):**
    *   **Воркер чанкинга (Pipeline 30):**
        ```sql
        UPDATE books SET process_status = 31 WHERE id = %s AND process_status IN (20, 31);
        ```
    *   **Воркер обогащения (Pipeline 40):**
        ```sql
        UPDATE books SET process_status = 41 WHERE id = %s AND process_status IN (30, 41);
        ```
    *   **Воркер векторизации (Pipeline 50):**
        ```sql
        UPDATE books SET process_status = 51 WHERE id = %s AND process_status IN (40, 51);
        ```
    *   **Логика:** Воркер проверяет, что `UPDATE` затронул ровно одну строку. Если 0 — значит, задача уже была захвачена другим воркером, и текущий воркер удаляет задачу из processing и отбрасывает ее.

4.  **Выполнение основной работы:** Чанкинг, NER или векторизация **всей книги**.

5.  **Обработка ошибок:**
    *   Если происходит **неустранимая** ошибка, воркер выполняет `UPDATE`, выставляя соответствующий error-статус:
    *   **Воркер чанкинга:** `UPDATE books SET process_status = 132 WHERE id = %s;`
    *   **Воркер обогащения:** `UPDATE books SET process_status = 142 WHERE id = %s;`
    *   **Воркер векторизации:** `UPDATE books SET process_status = 152 WHERE id = %s;`
    *   После этого задача удаляется из processing очереди и жизненный цикл завершается.

6.  **Успешное завершение и передача дальше:**
    *   **Воркер чанкинга:**
        1.  `UPDATE books SET process_status = 30 WHERE id = %s;`
        2.  `LPUSH books:queue:40_enrich_new '{"book_id": "..."}'`
        3.  `LREM books:queue:30_chunking_processing 1 task`
    *   **Воркер обогащения:**
        1.  `UPDATE books SET process_status = 40 WHERE id = %s;`
        2.  `LPUSH books:queue:50_vectorize_new '{"book_id": "..."}'`
        3.  `LREM books:queue:40_enrich_processing 1 task`
    *   **Воркер векторизации:**
        1.  `UPDATE books SET process_status = 50 WHERE id = %s;`
        2.  `LREM books:queue:50_vectorize_processing 1 task`
        3.  (Задача дальше не передается - конец pipeline).

### Важные принципы:
- **Идемпотентность:** Воркер может повторно захватить ту же задачу без вреда
- **Отказоустойчивость:** PostgreSQL статус - источник правды, Redis - диспетчер
- **Изоляция:** Каждый pipeline независим и может масштабироваться отдельно

## Статусы `books.process_status`

> **Примечание:** Статусы `30+` (Chunking, Enriching и т.д.) относятся к планируемому **RAG-пайплайну** и в текущей реализации ingestion-пайплайна (Pipeline 20) не используются. Они зарезервированы для будущих этапов обработки.

Поле `process_status` в таблице `books` является "летописью" и отражает полный жизненный цикл обработки книги.

| Статус | Название | Pipeline | Описание |
| :--- | :--- | :--- | :--- |
| **20** | **Parsed** | 20: Parser | Первичный парсинг завершен, артефакт создан. **Готов к чанкингу.** |
| **31** | **Chunking** | 30: Chunker | *В процессе* чанкинга. |
| **30** | **Chunked** | 30: Chunker | Чанкинг завершен. **Готов к обогащению.** |
| **41** | **Enriching** | 40: Enricher | *В процессе* обогащения (NER). |
| **40** | **Enriched** | 40: Enricher | Обогащение завершено. **Готов к векторизации.** |
| **51** | **Vectorizing** | 50: Vectorizer | *В процессе* векторизации. |
| **50** | **Completed** | 50: Vectorizer | Векторизация завершена, книга полностью обработана. |
| **132**| **Chunking Error** | 30: Chunker | Неустранимая ошибка на этапе чанкинга. |
| **142**| **Enriching Error**| 40: Enricher | Неустранимая ошибка на этапе обогащения. |
| **152**| **Vectorizing Error**| 50: Vectorizer | Неустранимая ошибка на этапе векторизации. |
