#### Частые команды запуска
```bash
# Параллельное скачивание (2 потока)
python run_05_searchfloor_sync.py --range 1-1000 --batch-size 10 --workers 2 --proxy
```

# Интеграция источника SearchFloor

## Обзор

**СТАТУС: ✅ РЕАЛИЗОВАНО И РАБОТАЕТ**

Интеграция источника `zip_searchfloor` (source_type=2) для автоматического скачивания книг с сайта searchfloor.org и создания ZIP-архивов для существующего пайплайна обработки.

### Ключевые возможности
- ✅ **Параллельное скачивание** с настраиваемым количеством потоков
- ✅ **Пакетная обработка** с промежуточными сохранениями
- ✅ **SOCKS прокси поддержка** с автоматической ротацией
- ✅ **Правильная обработка ZIP/FB2** форматов
- ✅ **Таймауты и retry логика** для надежности
- ✅ **Размер в KB и дата+время** в логах

## Архитектурные принципы

### Минималистичный подход
- **НЕТ** долгосрочного хранения метаданных синхронизации
- **НЕТ** сложной интеграции с пайплайном
- **НЕТ** дополнительного мониторинга
- **ПРОСТО** создаем ZIP-архивы, остальное делает существующий пайплайн

### Философия "создай архивы и забудь"
1. Скачиваем книги с сайта
2. Упаковываем в ZIP-архивы по ID-диапазонам
3. Существующий `scanner_inventorizer.py` автоматически подхватывает новые архивы
4. Стандартный пайплайн обработки делает дедупликацию и обработку

## Техническая архитектура

### Структура компонентов
```
app/ingestion/
└── searchfloor_downloader.py   # Единый компонент: парсинг, скачивание, архивирование

run_05_searchfloor_sync.py      # Запускной скрипт с полным набором опций
tmp/proxy.txt                   # Файл со списком SOCKS прокси (опционально)
```

### Именование файлов и архивов

**Архивы**: По диапазонам ID книг
```
zip_searchfloor/
├── 1000.zip     # Книги с ID 1-1000
├── 2000.zip     # Книги с ID 1001-2000  
├── 3000.zip     # Книги с ID 2001-3000
└── ...
```

**Файлы внутри архивов**: Только ID
```
1000.zip:
├── 1.fb2
├── 2.fb2
├── ...
└── 1000.fb2
```

## Особенности реализации

### Парсинг сайта SearchFloor

**Источник данных**: HTML-парсинг (API отсутствует)

**Основная страница**: `https://searchfloor.org/?status=is_finished&page=1`
- Извлечение пар (ID, дата): `<button id="9507" data-url="/book/9507" data-date="2025-07-22 10:55">`
- Построение URL скачивания: `https://searchfloor.org/book/{id}`

**Стратегии получения дат**:

1. **HTTP-метаданные подход** (РЕКОМЕНДУЕТСЯ):
   - Перебираем ID от 1 до max_id
   - Извлекаем `Last-Modified` из HTTP-заголовков при скачивании
   - Используем дату модификации файла для `date_time` в ZIP
   - **Проверено**: ID 1880 возвращает `Last-Modified: Tue, 31 Dec 2019 21:00:00 GMT`
   - Простота реализации, один запрос на книгу

2. **Гибридный подход** (альтернатива):
   - Парсим главную страницу для получения всех пар (ID, дата) за один запрос
   - Скачиваем файлы напрямую по ID с уже известными датами публикации
   - Больше сложности, но меньше HTTP-запросов

### Стратегии синхронизации

#### 1. Полная синхронизация (разовая)
- Парсинг всех страниц `?status=is_finished` для получения полного списка (ID, дата)
- Определение максимального ID из собранных данных
- Скачивание всех книг с известными датами публикации
- Создание архивов по диапазонам 1000 книг с правильными `date_time`

#### 2. Инкрементальная синхронизация (регулярная)
- Сохранение `searchfloor:last_max_id` в Redis
- Парсинг первой страницы `?status=is_finished` для новых книг
- Фильтрация ID > last_max_id
- Скачивание только новых книг с датами
- Обновление существующих книг при изменении дат

### Обработка контента

#### Очистка FB2 от изображений
```xml
<!-- УДАЛЯЕМ -->
<binary id="image1" content-type="image/jpeg">...</binary>
<image l:href="#image1"/>

<!-- ОСТАВЛЯЕМ -->
<title>Название книги</title>
<body>Текстовое содержание</body>
```

#### Обновления книг
- **Проблема**: Книги редактируются после публикации
- **Решение**: Перезаписывание существующих файлов в архивах
- **Детекция**: Сравнение размера/хеша скачанного файла

#### Извлечение и установка дат публикации
- **Источник дат**: Парсинг метаданных с сайта SearchFloor (дата публикации/обновления)
- **Fallback**: Дата скачивания файла
- **Применение**: Установка `date_time` в `ZipInfo` для корректной работы с `archive_mtime`
- **Формат**: Преобразование в tuple `(year, month, day, hour, minute, second)`

### Технические требования

#### Зависимости
```python
# Основные зависимости (уже в requirements.txt)
requests>=2.31.0
requests[socks]>=2.31.0  # Для SOCKS прокси
beautifulsoup4>=4.12.0
lxml>=4.9.0
pysocks>=1.7.1          # SOCKS поддержка

# Типы для разработки
types-requests>=2.32.0
```

#### Защита от блокировок
- **SOCKS прокси ротация**: Автоматическая ротация прокси из `tmp/proxy.txt`
- **Таймауты**: 10 сек для прокси, 30 сек для прямых соединений
- **Retry logic**: До 3 попыток с разными прокси при таймаутах
- **Адаптивный rate limiting**: Динамическая задержка, увеличивающаяся при ошибках 429
- **Мониторинг ошибок**: Автоматическая остановка при превышении лимита последовательных ошибок
- **Восстановление состояния**: Продолжение скачивания с места остановки при следующем запуске

#### Параллельная обработка
- **ThreadPoolExecutor**: Настраиваемое количество потоков (--workers)
- **Пакетная обработка**: Промежуточные сохранения каждые N книг (--batch-size)
- **Потоковая запись**: Прямая запись в ZIP без промежуточных файлов
- **Memory management**: Обработка больших файлов через потоки

#### Управление датами файлов в ZIP
- **Критическая важность**: Система использует `archive_mtime` для генерации UUID v7 и извлечения дат
- **Источники дат**: Дата публикации из метаданных FB2 или дата создания на сайте
- **Техническая реализация**: Установка `date_time` в `ZipInfo` перед записью файла

```python
import zipfile
from datetime import datetime

# Пример установки даты файла в ZIP
with zipfile.ZipFile("archive.zip", "w") as zip_file:
    zip_info = zipfile.ZipInfo(filename="book.fb2")
    # Устанавливаем дату публикации книги (например, из метаданных сайта)
    zip_info.date_time = (2023, 12, 15, 10, 30, 0)  # год, месяц, день, час, минута, секунда
    zip_file.writestr(zip_info, book_content)
```

## Интеграция с существующей системой

### Автоматическое обнаружение
Существующий `scanner_inventorizer.py` автоматически:
1. Сканирует директорию `zip_searchfloor/`
2. Извлекает `source_type=2` из пути
3. Создает задачи в Redis-очереди
4. Выполняет дедупликацию через PostgreSQL

### Cron-интеграция
```bash
# Полная синхронизация (разово)
python run_05_searchfloor_sync.py --full-sync --workers 3 --batch-size 100

# Синхронизация новых диапазонов каждые 6 часов
0 */6 * * * cd /path/to/books && python run_05_searchfloor_sync.py --range 10001-11000 --workers 2 --quiet >> /var/log/searchfloor.log 2>&1

# С использованием прокси для обхода блокировок
0 */6 * * * cd /path/to/books && python run_05_searchfloor_sync.py --range 10001-11000 --proxy --workers 2 --quiet >> /var/log/searchfloor.log 2>&1

# Стандартное сканирование источников каждый час
0 * * * * cd /path/to/books && python run_10_scan_sources.py --quiet >> /var/log/scanner.log 2>&1
```

## Команды управления

### Запускной скрипт `run_05_searchfloor_sync.py`

#### Основные команды
```bash
# Полная синхронизация всех книг
python run_05_searchfloor_sync.py --full-sync

# Синхронизация конкретного диапазона ID
python run_05_searchfloor_sync.py --range 1-1000

# Тихий режим для cron
python run_05_searchfloor_sync.py --range 1-1000 --quiet

# Режим отладки
python run_05_searchfloor_sync.py --range 1-100 --debug
```

#### Параметры производительности
```bash
# Параллельное скачивание (2 потока)
python run_05_searchfloor_sync.py --range 1-1000 --workers 2

# Пакетная обработка (сохранение каждые 50 книг)
python run_05_searchfloor_sync.py --range 1-1000 --batch-size 50

# Комбинированный режим
python run_05_searchfloor_sync.py --range 1-1000 --workers 3 --batch-size 100
```

#### Использование прокси
```bash
# Создать файл прокси
echo "*************:1080" > tmp/proxy.txt
echo "*************:1080" >> tmp/proxy.txt

# Запуск с ротацией прокси
python run_05_searchfloor_sync.py --range 1-1000 --proxy --workers 2

# Прокси + пакетная обработка
python run_05_searchfloor_sync.py --range 1-1000 --proxy --workers 2 --batch-size 50
```

#### Полный список параметров
```bash
python run_05_searchfloor_sync.py --help

Опции:
  --full-sync              Полная синхронизация всех книг
  --range START-END        Синхронизация диапазона ID (например: 1-1000)
  --workers N              Количество параллельных потоков (по умолчанию: 5)
  --batch-size N           Размер пакета для промежуточного сохранения (по умолчанию: 100)
  --proxy                  Использовать ротацию SOCKS прокси из tmp/proxy.txt
  --output-dir DIR         Директория для сохранения архивов
  --delay SECONDS          Начальная задержка между запросами (по умолчанию: 1.0)
  --max-errors N           Лимит последовательных ошибок перед остановкой (по умолчанию: 5)
  --quiet                  Тихий режим (только ошибки)
  --debug                  Режим отладки (подробные логи)

Коды возврата:
  0 - Успешное завершение
  1 - Критическая ошибка
  2 - Остановка из-за превышения лимита ошибок (можно продолжить)
  130 - Прерывание пользователем (Ctrl+C)
```

## Обработка ошибок

### Типы ошибок и их обработка
- **HTTP 404**: Книга удалена/недоступна → пропускаем, логируем как debug
- **HTTP 401**: Требует авторизации → пропускаем, логируем как info
- **HTTP 429**: Rate limiting → увеличение задержки, подсчет ошибок, остановка при лимите
- **Timeout/ProxyError**: Проблемы с прокси → переключение на следующий прокси
- **ConnectionError**: Сетевые проблемы → retry до 3 раз, подсчет критических ошибок
- **ZIP extraction error**: Поврежденный ZIP → пропускаем, логируем warning

### Новые возможности обработки ошибок
- **Счетчик последовательных ошибок**: Отслеживание 429 и критических ошибок
- **Адаптивная задержка**: Экспоненциальное увеличение при ошибках (до 60 сек)
- **Автоматическая остановка**: При достижении лимита ошибок (по умолчанию 5)
- **Сохранение прогресса**: Возможность продолжения с места остановки
- **Graceful degradation**: Постепенное уменьшение задержки при успешных запросах

### Реализованные стратегии восстановления
- **Proxy rotation**: Автоматическое переключение прокси при ошибках
- **Timeout handling**: 10 сек для прокси, 30 сек для прямых соединений
- **Graceful degradation**: Продолжение работы при частичных ошибках
- **Batch recovery**: Промежуточные сохранения предотвращают потерю данных
- **Progress tracking**: Подробная статистика успешности по пакетам

## Мониторинг и логирование

### Актуальное логирование
```
INFO: 🚀 Запуск синхронизации диапазона 1-1000
INFO: 🔄 Загружено 400 прокси для ротации
INFO: 📦 Пакет 1/10: скачиваем ID 1-100 (100 книг, 2 потоков)
INFO: ID 1 - OK (847 KB, 2022-11-19 09:30) via *************:1080
INFO: ID 2 - OK (755 KB, 2022-11-16 05:30) via *************:1080
INFO: ✅ Пакет 1: 98/100 книг (98.0%) за 45.2с + 0.1с сохранение
INFO: 🎉 Архив 1000.zip создан: 962/1000 книг (96.2%) за 8.5 минут
```

### Статистика в Redis (опционально)
```
searchfloor:stats:last_sync_time
searchfloor:stats:books_downloaded
searchfloor:stats:errors_count
```

## Настройка прокси

### Формат файла `tmp/proxy.txt`
```
# Один SOCKS5 прокси на строку в формате IP:PORT
*************:1080
*************:1080
*************:1080

# Комментарии поддерживаются
# proxy.example.com:1080
```

### Тестирование прокси
```bash
# Тест одного прокси
curl --socks5 *************:1080 --connect-timeout 10 -I https://searchfloor.org/book/1880

# Тест всех прокси из файла
while read proxy; do
  [[ $proxy =~ ^#.*$ ]] && continue
  curl --socks5 "$proxy" --connect-timeout 10 -s -o /dev/null -w "Proxy $proxy: %{http_code} %{time_total}s\n" https://searchfloor.org/book/1880
done < tmp/proxy.txt
```

## Производительность и рекомендации

### Оптимальные настройки
- **Без прокси**: `--workers 1` (избегаем rate limiting)
- **С прокси**: `--workers 2-5` (зависит от количества прокси)
- **Batch size**: `--batch-size 50-100` (баланс между памятью и надежностью)

### Типичные сценарии
```bash
# Быстрое скачивание с прокси
python run_05_searchfloor_sync.py --range 1-1000 --proxy --workers 3 --batch-size 50

# Надежное скачивание без прокси
python run_05_searchfloor_sync.py --range 1-1000 --workers 1 --batch-size 100

# Массовая синхронизация
python run_05_searchfloor_sync.py --full-sync --proxy --workers 5 --batch-size 200
```

---

**Статус**: ✅ ПОЛНОСТЬЮ РЕАЛИЗОВАНО И ПРОТЕСТИРОВАНО
**Возможности**: Параллельная загрузка, прокси ротация, пакетная обработка
**Интеграция**: Автоматическая через `scanner_inventorizer.py`
