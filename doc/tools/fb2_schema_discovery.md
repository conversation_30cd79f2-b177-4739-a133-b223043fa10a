# FB2 Schema Discovery Tool

Автоматический анализатор для выявления потенциальных маркеров глав в FALLBACK книгах.

## Назначение

Инструмент решает проблему 21.2% книг, которые попадают в категорию `FALLBACK=no_markers` - книги без распознаваемых маркеров глав. Использует статистический анализ для автоматического поиска повторяющихся текстовых и структурных паттернов, которые могут быть новыми маркерами глав.

## Принцип работы

### Основная идея
**Структурные элементы (заголовки, разделители) повторяются, а повествовательный текст — нет.**

### Алгоритм анализа
1. **Частотный анализ текста** - поиск повторяющихся текстовых строк
2. **Структурный анализ** - поиск повторяющихся комбинаций XML/FB2 тегов
3. **Статистическая фильтрация** - отсеивание случайных совпадений
4. **Агрегация результатов** - сбор статистики по всей базе книг

### Архитектура
- **Producer-Consumer** с `ProcessPoolExecutor` для параллельной обработки
- **Воркеры** анализируют отдельные книги независимо
- **Агрегатор** собирает и ранжирует результаты

## Использование

### Базовое использование
```bash
python tools/run_discover_fb2_schemas.py --input fallback_books.txt
```

### Расширенные опции
```bash
# Увеличить количество воркеров
python tools/run_discover_fb2_schemas.py --input fallback_books.txt --workers 8

# Изменить порог минимальных вхождений
python tools/run_discover_fb2_schemas.py --input fallback_books.txt --min-occurrences 5

# Показать больше результатов и сохранить в CSV
python tools/run_discover_fb2_schemas.py --input fallback_books.txt --top-results 100 --output results.csv

# Подробный вывод для отладки
python tools/run_discover_fb2_schemas.py --input fallback_books.txt --verbose
```

### Формат входного файла
```
# Комментарии начинаются с #
# Поддерживаются два формата:

# 1. Конкретный файл в архиве
/path/to/archive.zip::book.fb2

# 2. Первый FB2 файл в архиве
/path/to/archive.zip
```

## Параметры

| Параметр | Описание | По умолчанию |
|----------|----------|--------------|
| `--input` | Путь к файлу со списком книг | Обязательный |
| `--output` | Путь к выходному CSV файлу | Опционально |
| `--workers` | Количество параллельных воркеров | 4 |
| `--min-occurrences` | Минимальное количество вхождений паттерна в книге | 3 |
| `--top-results` | Количество топ-результатов для вывода | 50 |
| `--verbose` | Подробный вывод | false |

## Алгоритмы нормализации

### Текстовые паттерны
- **Нормализация пробелов** - множественные пробелы заменяются одним
- **Приведение к нижнему регистру** - для унификации
- **Римские цифры** - заменяются на `[ROMAN]` (I, II, III → [ROMAN])
- **Арабские числа** - заменяются на `[NUMBER]` (1, 2, [3] → [NUMBER])

### Структурные сигнатуры
- **Иерархические подписи** - `paragraph>strong>emphasis`
- **Дедупликация** - избегание повторов в одной сигнатуре
- **Фильтрация текстовых узлов** - учитываются только XML элементы

## Пример вывода

```
================================================================================
📝 ТОП-50 ТЕКСТОВЫХ ПАТТЕРНОВ-КАНДИДАТОВ
================================================================================
Паттерн                                  | Книг     | Всего    | Ср/книгу | Пример книги
------------------------------------------------------------------------------------------------
* * *                                    | 8542     | 120432   | 14.1     | archive1.zip::book1.fb2
глава [NUMBER]                           | 5123     | 95123    | 18.6     | archive2.zip::book2.fb2
часть [ROMAN]                            | 3456     | 40321    | 11.7     | archive3.zip::book3.fb2

================================================================================
🏗️ ТОП-50 СТРУКТУРНЫХ ПАТТЕРНОВ-КАНДИДАТОВ
================================================================================
Структурная сигнатура                    | Книг     | Всего    | Ср/книгу | Пример книги
------------------------------------------------------------------------------------------------
paragraph>strong                         | 12345    | 250123   | 20.3     | archive4.zip::book4.fb2
paragraph>emphasis                       | 9876     | 180456   | 18.3     | archive5.zip::book5.fb2
```

## Интерпретация результатов

### Приоритет анализа
1. **Book Count** - количество книг, где встретился паттерн (главный критерий)
2. **Avg Per Book** - среднее количество на книгу (показатель регулярности)
3. **Total Occurrences** - общее количество (менее важно)

### Перспективные кандидаты
- **Высокий Book Count** (>1000) - паттерн встречается во многих книгах
- **Умеренный Avg Per Book** (5-20) - регулярное, но не избыточное использование
- **Осмысленные паттерны** - `* * *`, `глава [NUMBER]`, `paragraph>strong`

### Подозрительные результаты
- **Очень высокий Avg Per Book** (>50) - возможно, обычный текст
- **Очень низкий Book Count** (<10) - редкие случаи, не стоят внимания
- **Бессмысленные паттерны** - случайные символы, фрагменты предложений

## Следующие шаги

После получения результатов анализа:

1. **Ручная проверка** - изучить топ-10 кандидатов в реальных книгах
2. **Создание правил** - добавить новые паттерны в `MarkerAnalyzer`
3. **Тестирование** - проверить на регрессионных тестах
4. **Измерение эффекта** - запустить повторный анализ для оценки улучшений

## Ограничения

- **Не гарантирует 100% точность** - требует ручной проверки результатов
- **Зависит от качества входных данных** - нужен репрезентативный набор FALLBACK книг
- **Вычислительно затратен** - обработка больших объемов может занять время
- **Не учитывает семантику** - анализирует только структуру и частоту
