# План реализации (Roadmap)

### Архитектурные принципы Ingestion Pipeline

- **Двухфазная атомарность данных:** 
  1. **Фаза 1:** Метаданные сохраняются в БД со статусом `process_status=10` (метаданные сохранены)
  2. **Фаза 2:** Создается JSON-артефакт на диске, статус обновляется на `process_status=20` (полностью обработано)
  3. Это гарантирует целостность между БД и файловой системой, предотвращая "мусорные" артефакты
- **Разделение ответственности:** Система разделена на два независимых компонента:
    - **Сканер (Producer):** Легковесный сервис, единственная задача которого — находить новые файлы и ставить задачи в очередь.
    - **Воркер (Consumer):** Легковесный координирующий сервис, делегирующий бизнес-логику `BookProcessor` с модульной архитектурой парсинга.
- **Модульность парсинга:** Новая архитектура с разделением по форматам:
    - `ParserDispatcher` - определяет формат и выбирает парсер
    - Специализированные парсеры (FB2Parser, EpubParser, MobiParser)
    - Трансформеры в универсальную CanonicalBook модель
    - `HashComputer` - оптимизированное хэширование с MD5 UUID
- **Масштабируемость:** Архитектура поддерживает горизонтальное масштабирование воркеров для параллельной обработки задач из очереди, что позволяет легко наращивать производительность.
- **Идемпотентность:** Повторная обработка одной и той же задачи не приводит к созданию дубликатов в БД или повреждению данных.

---

### Этап 1: MVP - Ядро системы (Ingestion Pipeline)

**Задача:** Реализовать отказоустойчивый и масштабируемый пайплайн для приема, обработки и сохранения книг.

**Цель:** Наладить стабильный процесс, при котором новые книги автоматически попадают в систему, проходят дедупликацию, сохраняются в PostgreSQL и архивируются.

#### Компоненты и логика работы

**1. Сканер-продюсер (`run_10_scan_sources.py`)**
*   Сканирует исходные директории, определенные в `SOURCE_DIRS`.
*   Извлекает числовой `source_id` из имени файла регулярным выражением.
*   Определяет `source_type` на основе родительской директории и `SOURCE_TYPE_MAP`.
*   **Дедупликация (3-уровневая проверка):**
    1. Redis очереди: `books:queue:new`, `books:queue:processing`, `books:queue:completed`
    2. PostgreSQL: таблица `book_sources` по индексу `(source_type, source_id)`
    3. Карантин PostgreSQL: таблица `quarantined_books` для исключения проблемных файлов
*   Формирует компактные задачи: `{"source_type": 1, "source_id": 826222}` и добавляет в `books:queue:new`.
*   **Режимы работы:**
    - Пакетная дедупликация: оптимизированная обработка больших объемов данных через пакетные запросы к PostgreSQL и Redis
*   **Важно:** Сканер не перемещает файлы, только создает задачи.

**2. Воркер-потребитель (`run_20_process_book_worker.py`)**
*   **Захват задачи и логическая блокировка:**
    1.  Атомарно извлекает задачу из `books:queue:new` и помещает ее в `books:queue:processing` (через `RPOPLPUSH`).
    2.  Устанавливает логическую блокировку в Redis: `SET lock:book:{source_type}:{source_id}` с TTL.
    3.  Исходные файлы остаются на месте, воркер работает с task_data.
*   **Модульная обработка (Новая архитектура):**
    1.  Читает архив напрямую из исходной директории через StorageManager.
    2.  **ParserDispatcher** определяет формат файла (FB2/EPUB/MOBI) по расширению и магическим байтам.
    3.  Выбирает специализированный парсер:
        - **FB2Parser** → парсит FB2 в нативную модель
        - **FB2CanonicalTransformer** → преобразует в универсальную CanonicalBook
        - (В будущем: EpubParser, MobiParser с соответствующими трансформерами)
    4.  **HashComputer** вычисляет оптимизированный хэш:
        - `metadata_hash` - MD5 UUID из "название+авторы+переводчики+серия" (36 символов)
        - Единый подход к дедупликации вместо сложной логики двух хэшей
    5.  Проводит дедупликацию по хэшам, обращаясь к PostgreSQL.
    6.  Если книга уникальна, в рамках одной транзакции сохраняет всю информацию в БД.
*   **Завершение и очистка:**
    1.  Перемещает задачу из `books:queue:processing` в `books:queue:completed` (постоянное хранение).
    2.  Снимает логическую блокировку Redis.
    3.  Исходные файлы остаются на месте, результаты сохраняются в `CANONICAL_STORAGE_PATH` (многоуровневая структура по UUID).

**3. Обработка исключений и отказоустойчивость**
*   **Сбой воркера:** Если воркер падает, задача остается в `books:queue:processing`, а логическая блокировка Redis истекает по TTL. Система восстановления обнаруживает "зависшие" задачи и возвращает их в `books:queue:new` для повторной обработки.
*   **Поврежденный файл (Quarantine):** При критических ошибках или превышении лимита повторных попыток воркер:
    1.  Отлавливает исключение или фиксирует исчерпание попыток.
    2.  Удаляет задачу из `books:queue:processing`.
    3.  Записывает информацию о проблеме в PostgreSQL таблицу `quarantined_books` (логический карантин).
    4.  Исходный файл остается на месте для возможной повторной обработки.
    5.  Снимает логическую блокировку Redis.
    6.  Логирует инцидент.
*   **Повторные попытки:** Воркер автоматически повторяет обработку до 3 раз при временных ошибках. После исчерпания попыток файл отправляется в карантин.
*   **Система восстановления (`run_00_recovery.py`):** Аудитор целостности состояния с двумя модулями:
    1. **StaleTaskAuditor:** Обнаружение зависших задач в Redis по истекшему таймауту
    2. **IncompleteBookAuditor:** Поиск книг со статусом 10 в PostgreSQL без артефактов
    3. **Философия:** Безопасный откат вместо доделывания, идемпотентность, режим --dry-run по умолчанию
*   **Мониторинг:** Скрипт поддерживает подробный анализ состояния системы с флагами --verbose и --debug

---

### Технические спецификации

#### Структура определяется переменными SOURCE_DIRS и CANONICAL_STORAGE_PATH в .env
**Новая архитектура без перемещения файлов:**
```
# Исходные файлы (определяется SOURCE_DIRS)
/path/to/zip_flibusta/   # Исходные архивы остаются нетронутыми
├── flibusta.826222.zip  # Файлы никуда не перемещаются
├── flibusta.826789.zip  # Обрабатываются потоково через StorageManager
└── flibusta.828931.zip

# Результаты обработки (определяется CANONICAL_STORAGE_PATH)
/storage/canonical_json/ # Многоуровневая структура по UUID
├── 01/8f/7b/018f7b8c-a0f8-7177-8c6a-3a1b5b9d4a1e.json.zst
├── 01/8f/7c/018f7c1d-b2e9-7288-9d7b-4c2c6c0e5b2f.json.zst
└── ...

# Карантин: логический (PostgreSQL таблица quarantined_books)
# Физических копий файлов больше нет
```

#### Схема Redis (3-очередная архитектура)
- **Очереди (Lists):**
    - `books:queue:new` — новые задачи от сканера.
    - `books:queue:processing` — задачи в обработке воркером.
    - `books:queue:completed` — успешно завершенные задачи (постоянное хранение).
- **Множество (Set):**
    - `books:set:queued_ids` — кэш ID задач в очередях для быстрой проверки дубликатов при пакетной дедупликации.

#### Статусы обработки книг (process_status)
- **0:** Новая запись (по умолчанию)
- **10:** Метаданные сохранены в БД, артефакт не создан
- **20:** Полностью обработано (метаданные + артефакт)

#### Конфигурация (`settings.py` / `.env`)
```bash
# Redis
REDIS_URL=redis://localhost:6379
QUEUE_PARSING_NEW=books:queue:20_parsing_new
QUEUE_PARSING_PROCESSING=books:queue:20_parsing_processing
QUEUE_COMPLETED=books:queue:completed
SET_QUEUED_IDS=books:set:queued_ids
WORKER_TIMEOUT=300 # Таймаут для определения "зависших" задач в секундах

# Примечание: SET_PROCESSED кэш упразднен в новой архитектуре
# PostgreSQL является единственным источником правды для дедупликации
```

---

### Будущие этапы

#### Этап 2: RAG Pipeline и семантический поиск
- **Задача:** Реализация пайплайна для чанкинга, векторизации и сохранения данных в Qdrant.
- **Цель:** Создать API для выполнения семантического поиска по всему корпусу текстов.

#### Этап 3: MVP - Веб-сайт
- **Задача:** Разработка минимально жизнеспособного сайта с базовым и семантическим поиском.
- **Цель:** Предоставить пользователям интерфейс для взаимодействия с результатами работы RAG.

---

### Мониторинг и Логирование

- **Логирование:** Необходимо определить единый структурированный формат логов (например, JSON) для сканера и воркера, чтобы упростить отладку. Логи должны четко отражать жизненный цикл каждой задачи.
- **Мониторинг:** Система предоставляет команды для мониторинга 3-очередной архитектуры:
    ```bash
    # Размеры всех очередей
    redis-cli LLEN books:queue:new
    redis-cli LLEN books:queue:processing  
    redis-cli LLEN books:queue:completed
    redis-cli SCARD books:set:queued_ids
    
    # Файловая статистика
    find /sources/*/quarantine -name "*.zip" | wc -l
    find /sources/*/processed -name "*.json" | wc -l
    
    # Статистика БД
    psql -c "SELECT source_type, COUNT(*) FROM book_sources GROUP BY source_type;"
    psql -c "SELECT process_status, COUNT(*) FROM books GROUP BY process_status;" # Контроль статусов
    ```
    *   **Ключевые метрики:** размер очередей, время обработки книги, количество ошибок, распределение по источникам
    *   **Автоматизация:** Возможность интеграции с Prometheus/Grafana для визуализации