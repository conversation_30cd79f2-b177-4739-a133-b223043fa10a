# История изменений

## 🚀 Оптимизация детекторов аномалий с BookValidationContext (текущая версия)

### ✅ Введение BookValidationContext для устранения избыточных вычислений
- **Принцип "Вычислить один раз, использовать многократно"**: Создан `BookValidationContext` с предвычисленными метриками
- **Централизация вычислений**: Все ресурсоемкие операции выполняются в `BookValidator._create_validation_context()`
- **Оптимизация детекторов**: Все детекторы (`SmallBookDetector`, `FragmentDetector`, `AnthologyDetector`) переведены на работу с контекстом
- **Повышение производительности**: Устранены дублирующие итерации по главам книги в каждом детекторе
- **Неизменяемость данных**: `BookValidationContext` объявлен как `frozen=True` для гарантии целостности

### ✅ Обновленная архитектура валидации
- **BookValidator**: Добавлен метод `_create_validation_context()` для создания контекста с метриками
- **SmallBookDetector**: Методы `check_book_structure()` и `get_rejection_reason()` работают с `BookValidationContext`
- **FragmentDetector**: Методы `is_fragment()` и `get_fragment_reason()` используют предвычисленное содержимое последней главы
- **AnthologyDetector**: Методы `is_anthology()` и `get_anthology_reason()` используют предвычисленные метрики авторов и глав
- **Обратная совместимость**: Публичный API `BookValidator.validate()` остался неизменным

### ✅ Тестирование и валидация
- **Инструменты tools/**: `run_statistical_reporter.py` и `run_anomaly_analyzer.py` работают с новой архитектурой
- **Производительность**: Обработка 1836 книг за 7.3 секунды (251.80 книг/сек) в statistical reporter
- **Корректность**: Детекция аномалий работает правильно (59 малый контент, 437 мало глав, 186 антологий)
- **Документация**: Обновлена `doc/operations/quarantine.md` с новыми сигнатурами методов

## 🔄 Рефакторинг диагностического инструмента

### ✅ Введение диагностического режима в BookProcessor  
- **Добавлен diagnostic_mode в BookProcessor**: Флаг `diagnostic_mode: bool = False` в конструкторе
- **Сбор аномалий без прерывания**: В диагностическом режиме `_check_fragment()` собирает аномалии в `detected_anomalies` вместо выбрасывания `QuarantineError`
- **Полная имитация производственного пайплайна**: `run_diagnostic_tool.py` теперь использует `BookProcessor` в `diagnostic_mode=True`
- **Устранение дублирования логики**: Диагностический инструмент больше не дублирует логику парсинга, валидации и обработки
- **Автоматическая синхронизация**: Изменения в `BookProcessor` автоматически отражаются в диагностике
- **Поддержка локальных файлов**: Создается временный ZIP архив для совместимости с `BookProcessor.process()`

### 🎯 Архитектурные улучшения
- **Принцип DRY**: Единая кодовая база для продакшена и диагностики  
- **Точность имитации**: Диагностика использует тот же код, что и реальная обработка
- **Безопасность**: Диагностический режим НЕ записывает в БД и НЕ сохраняет артефакты

## 📊 Завершение очистки кода

### ✅ Очистка технического долга
- **Удаление legacy-методов после завершения рефакторинга**
  - Удален неиспользуемый `BookDataBuilder.build_book_dto()` - все компоненты перешли на `build_book_dto_from_canonical()`
  - Удален неиспользуемый `HashComputer._compute_hashes_from_dict()` - полный переход на работу с `CanonicalBook`
  - Упрощен интерфейс `HashComputer.compute_hashes()` - убрана поддержка dict, только `CanonicalBook`

## 📊 Завершение Этапа 1

### ✅ Завершенные обновления
- **Модульная архитектура парсинга**: Рефакторинг `BookParser` → `ParserDispatcher`
  - Добавлена поддержка специализированных парсеров по форматам
  - `FB2Parser` + `FB2CanonicalTransformer` для FB2 → `CanonicalBook`
  - Подготовлена архитектура для EPUB/MOBI

- **КРИТИЧЕСКИЙ РЕФАКТОРИНГ: Устранение "божественного метода" в воркере**
  - Создан `BookProcessor` - инкапсуляция всей бизнес-логики обработки
  - Воркер теперь отвечает только за координацию и файловые операции
  - Соблюдение принципа единственной ответственности (SRP)
  - Упрощение тестирования и поддержки архитектуры
  
- **Оптимизация хэширования**: Переход на MD5 UUID
  - Экономия места в БД: 36 символов вместо 64
  - `metadata_hash` и `content_hash` оба используют MD5 UUID
  
- **КРИТИЧЕСКИЙ РЕФАКТОРИНГ: Устранение неэффективного преобразования CanonicalBook → dict**
  - `HashComputer` теперь работает напрямую с `CanonicalBook`
  - `BookDataBuilder` получил метод `build_book_dto_from_canonical()`
  - Убрано промежуточное копирование 15+ полей в воркере
  - Улучшена типобезопасность и производительность

### 📈 Текущий статус
- **FB2 парсинг:** 100% готов с новой архитектурой
- **EPUB парсинг:** Запланирован на Этап 2
- **MOBI парсинг:** Запланирован на Этап 3
- **Ingestion Pipeline:** Полностью функционален
- **RAG Pipeline:** В планах (Этап 2)

### 🛠️ Инфраструктурные улучшения
- 3-очередная архитектура Redis (new/processing/completed)
- Система восстановления - аудитор целостности состояния (StaleTaskAuditor + IncompleteBookAuditor)
- Многоуровневое хранение артефактов по UUID с Zstandard сжатием
- Логический карантин через PostgreSQL без физического копирования файлов
- Оптимизированная дедупликация с Redis кэшированием

---

## 📋 Будущие версии

### Этап 2: RAG Pipeline (планируется)
- Чанкинг и векторизация текстов
- Qdrant для векторного поиска
- LLM анализ содержания и персонажей
- Генерация нестандартных тегов

## Версия 1.3.0 Унификация дедупликации

### 🔄 КРИТИЧЕСКИЙ РЕФАКТОРИНГ: Переход к единому `metadata_hash`

**Причина изменений:**
- `content_hash` оказался архитектурно неправильным и технически хрупким
- Алгоритм `_find_unique_sentence` был ненадежен (fallback на первые 100 символов)
- `FragmentDetector` уже решает проблему ознакомительных фрагментов
- Необходимость учета переводчиков для корректного различения переводов

**Ключевые изменения:**

- **Единый metadata_hash**: Убран `content_hash`, весь алгоритм дедупликации переведен на улучшенный `metadata_hash`
- **Учет переводчиков**: `metadata_hash` теперь включает переводчиков (role_type=2) для различения переводов одного произведения
- **Агрессивная нормализация**: Добавлена функция `normalize_for_hash()` с заменой ё→е, удалением пунктуации
- **Упрощение схемы БД**: Убран столбец `content_hash` и соответствующий индекс `uq_books_content_hash`
- **Простая логика дедупликации**: Один запрос к БД вместо сложного анализа типов дубликатов

**Технические детали:**
- **HashComputer**: Переписан для работы только с `metadata_hash`
- **BookProcessor**: Упрощена логика `_check_duplicates()` 
- **DatabaseSaver**: Убраны все обращения к `content_hash`
- **Queries**: `check_book_duplicates()` принимает только `metadata_hash`

**Результаты:**
- ✅ Увеличена надежность дедупликации (нет зависимости от качества содержимого)
- ✅ Упрощена архитектура (один хэш вместо двух)
- ✅ Учитываются переводчики (правильное различение переводов)
- ✅ Фрагменты по-прежнему отсеиваются через `FragmentDetector`

---

## Версия 1.2.0 Модульная архитектура парсинга 