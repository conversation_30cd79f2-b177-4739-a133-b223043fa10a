## 🔍 Анализ аномалий

### `run_anomaly_analyzer.py` - Профессиональный инструмент для тюнинга детекторов

Основной инструмент для итеративного улучшения детекторов аномалий с четырьмя режимами работы:

**Режимы работы:**
- **Разведка** (`--path` или `--from-file`): Сканирует новые файлы по указанным путям или из файла
- **Тюнинг** (по умолчанию): Перепроверяет файлы из `result_anomalies_registry.json`
- **Фокусный тюнинг** (`--type`): Перепроверяет только указанный тип аномалий
- **Исправление** (`--fix`): Применяет исправления для книг из карантина

**Примеры использования:**
```bash
# Тюнинг всех аномалий
python tools/run_anomaly_analyzer.py

# Фокусный тюнинг только trial аномалий
python tools/run_anomaly_analyzer.py --type trial

# Разведка новых файлов по путям
python tools/run_anomaly_analyzer.py --path /data/books

# Разведка новых файлов из списка в файле
python tools/run_anomaly_analyzer.py --from-file paths.txt

# Исправление карантинных книг
python tools/run_anomaly_analyzer.py --fix

# Исправление без подтверждения
python tools/run_anomaly_analyzer.py --fix --yes

# Включение отладочного режима
python tools/run_anomaly_analyzer.py --debug
```

**Основной артефакт:** `result_anomalies_registry.json` - персистентный файл состояния аномалий.

**Дополнительные опции:**
- `--debug`: Включает детальное логирование для отладки
- `--from-file`: Позволяет указать файл со списком путей для сканирования
- `--type`: Фильтрует аномалии по типу для фокусного тюнинга
- `--yes`: Пропускает интерактивное подтверждение в режиме исправления

**Особенности:**
- **Прогресс-бар:** Визуальное отслеживание прогресса обработки (при наличии tqdm)
- **Sliding Window:** Оптимизированная обработка больших объемов данных
- **Многопроцессность:** Параллельная обработка файлов


## 🔗 Связанная документация

- [Архитектура pipeline](../doc/operations/pipeline01.md)
- [Redis очереди](../doc/redis_queues.md)
- [Система карантина](../doc/operations/quarantine.md)
- [База данных](../doc/database/schema.md)
