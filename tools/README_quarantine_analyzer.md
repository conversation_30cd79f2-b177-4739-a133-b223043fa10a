# run_analyze_quarantine.py - Анализ карантинных книг

Инструмент для анализа и управления книгами в карантине PostgreSQL.

## 🎯 Назначение

Этот инструмент предназначен для работы с карантинными книгами в PostgreSQL базе данных. Он позволяет анализировать, фильтровать и экспортировать данные о книгах, которые были помещены в карантин различными детекторами аномалий.

## ✨ Возможности

- **Поиск записей** по типу карантина, архиву, причине
- **Статистика карантина** с группировкой по типам аномалий
- **Экспорт в CSV** для дальнейшего анализа
- **Чтение файлов** из карантина для проверки
- **Фильтрация** по различным критериям
- **Детальная информация** о каждой карантинной записи

## 🚀 Использование

### Базовые команды

```bash
# Статистика карантина
python tools/run_analyze_quarantine.py --stats

# Поиск trial аномалий
python tools/run_analyze_quarantine.py --type trial --limit 10

# Поиск по архиву
python tools/run_analyze_quarantine.py --archive-path "problematic.zip"

# Экспорт в CSV
python tools/run_analyze_quarantine.py --stats --export-csv quarantine_report.csv

# Поиск по причине
python tools/run_analyze_quarantine.py --reason "Обнаружен ознакомительный фрагмент"

# Комбинированный поиск
python tools/run_analyze_quarantine.py --type trial --archive-path "books.zip" --limit 5
```

### Параметры командной строки

| Параметр | Описание |
|----------|----------|
| `--stats` | Показать статистику карантина |
| `--type` | Фильтр по типу карантина (trial, small_content, etc.) |
| `--archive-path` | Фильтр по пути к архиву |
| `--reason` | Фильтр по причине карантина |
| `--limit` | Ограничить количество результатов |
| `--export-csv` | Экспортировать результаты в CSV файл |
| `--read-file` | Прочитать конкретный файл из карантина |
| `--debug` | Включить отладочный режим |

## 📊 Типы карантина

Система поддерживает следующие типы карантина:

| Тип | Описание |
|-----|----------|
| `trial` | Ознакомительные фрагменты |
| `small_content` | Слишком маленькие книги |
| `anthology` | Антологии и сборники |
| `broken_footnotes` | Проблемы со сносками |
| `encoding_issues` | Проблемы с кодировкой |
| `parsing_errors` | Ошибки парсинга |

## 🔧 Примеры использования

### Общая статистика

```bash
# Получить общую статистику карантина
python tools/run_analyze_quarantine.py --stats

# Экспортировать статистику в CSV
python tools/run_analyze_quarantine.py --stats --export-csv quarantine_stats.csv
```

### Анализ конкретных проблем

```bash
# Найти все trial аномалии
python tools/run_analyze_quarantine.py --type trial

# Найти проблемы в конкретном архиве
python tools/run_analyze_quarantine.py --archive-path "/data/problematic.zip"

# Найти записи с конкретной причиной
python tools/run_analyze_quarantine.py --reason "Слишком мало контента"
```

### Детальное исследование

```bash
# Ограничить результаты для детального анализа
python tools/run_analyze_quarantine.py --type small_content --limit 5

# Прочитать конкретный файл из карантина
python tools/run_analyze_quarantine.py --read-file \
    --type trial \
    --archive-path "books.zip" \
    --book-filename "book.fb2"
```

## 📈 Статистический анализ

Инструмент предоставляет детальную статистику:

- **Общее количество** карантинных записей
- **Группировка по типам** аномалий
- **Топ архивов** с наибольшим количеством проблем
- **Временная динамика** поступления в карантин
- **Распределение причин** карантина

### Пример статистического вывода

```
📊 Статистика карантина:
  📋 Всего записей: 1,234
  
📈 По типам аномалий:
  trial: 456 (37.0%)
  small_content: 321 (26.0%)
  anthology: 234 (19.0%)
  broken_footnotes: 123 (10.0%)
  encoding_issues: 67 (5.4%)
  parsing_errors: 33 (2.7%)

📦 Топ проблемных архивов:
  problematic.zip: 89 записей
  old_books.zip: 67 записей
  mixed_content.zip: 45 записей
```

## 🏗️ Архитектура

### Основные компоненты

- **Подключение к PostgreSQL** через `get_db_connection()`
- **SQL запросы** с параметризацией для безопасности
- **CSV экспорт** с правильной кодировкой UTF-8
- **Фильтрация данных** по множественным критериям

### Структура данных

Инструмент работает с таблицей `quarantined_books`:

```sql
CREATE TABLE quarantined_books (
    source_type INTEGER,
    source_id INTEGER,
    primary_quarantine_type VARCHAR,
    reason TEXT,
    details JSONB,
    updated_at TIMESTAMP,
    PRIMARY KEY (source_type, source_id)
);
```

## 📋 Требования

- Python 3.12+
- Доступ к PostgreSQL базе данных
- Установленные зависимости:
  - `psycopg` (для работы с PostgreSQL)
  - Конфигурация подключения к БД

## ⚠️ Ограничения

- **Только чтение:** Инструмент не изменяет данные в карантине
- **PostgreSQL:** Работает только с PostgreSQL базой данных
- **Права доступа:** Требует прав на чтение таблицы `quarantined_books`

## 🔗 Связанные инструменты

- **`run_anomaly_analyzer.py`** - Основной инструмент для анализа аномалий
- **`run_statistical_reporter.py`** - Генератор статистических отчетов
- **BookValidator** - Компонент для валидации книг

## 📊 Пример CSV экспорта

Экспортируемый CSV содержит следующие колонки:

```csv
source_type;source_id;quarantine_type;reason;archive_path;book_filename;updated_at
1;12345;trial;Обнаружен ознакомительный фрагмент;/data/books.zip;book1.fb2;2024-01-01 12:00:00
2;67890;small_content;Слишком мало контента;/data/small.zip;tiny.fb2;2024-01-01 13:00:00
```
