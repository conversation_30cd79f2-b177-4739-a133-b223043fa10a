#!/usr/bin/env python3
"""
Общие утилиты для скриптов tools/.

Устраняет дублирование логики работы с:
- ZIP архивами и извлечением FB2 файлов
- Созданием временных файлов и их очисткой
- Инициализацией проектных компонентов парсинга
- Стандартными паттернами обработки ошибок

Все функции используют только проектные компоненты, никакой самописной логики.

Синглтон ToolsComponents устраняет дублирование "тяжелых" объектов между инструментами.
Все инструменты используют переиспользуемые экземпляры ParserDispatcher, BookValidator и др.
"""

import io
import sys
import time
from concurrent.futures import ProcessPoolExecutor, as_completed
from pathlib import Path
from typing import Any, Callable, Generator, Iterator

sys.path.insert(0, str(Path(__file__).parent.parent))

# Импорты проектных компонентов
from app.processing.canonical_model import CanonicalBook
from app.processing.error_handler import QuarantineError
from app.processing.fragment_detector import FragmentDetector
from app.processing.parser_dispatcher import ParserDispatcher
from app.processing.parsing_report import ParsingReport

# Импорты дополнительных компонентов (могут быть недоступны в тестовой среде)
try:
    from app.processing.book_validator import BookValidator
    from app.processing.queue_manager import TaskQueueManager
    from app.storage import LocalStorageManager

    FULL_COMPONENTS_AVAILABLE = True
except ImportError:
    FULL_COMPONENTS_AVAILABLE = False


class ToolsComponents:
    """Синглтон для переиспользования проектных компонентов.

    Устраняет дублирование "тяжелых" объектов в инструментах tools/.
    ParserDispatcher, BookValidator и другие компоненты создаются единожды
    и переиспользуются во всех инструментах.
    """

    _instance: "ToolsComponents | None" = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if not getattr(self, "_initialized", False):
            # Базовые компоненты (всегда доступны)
            self.parser_dispatcher = ParserDispatcher()
            self.fragment_detector = FragmentDetector()

            # Дополнительные компоненты (только если доступны)
            if FULL_COMPONENTS_AVAILABLE:
                # BookValidator создается с детекторами, настроенными из settings
                self.book_validator = BookValidator()
                self.storage_manager = LocalStorageManager()
                self.queue_manager = TaskQueueManager()
            else:
                self.book_validator = None
                self.storage_manager = None
                self.queue_manager = None

            self._initialized = True

    def has_full_functionality(self) -> bool:
        """Проверяет доступность всех компонентов."""
        return FULL_COMPONENTS_AVAILABLE


def get_components() -> ToolsComponents:
    """Получить переиспользуемые компоненты проекта."""
    return ToolsComponents()


# Legacy функции удалены - используйте потоковую обработку через get_canonical_book_from_stream() с обязательным parser_dispatcher


def get_canonical_book_from_stream(
    fb2_stream: io.BytesIO,
    fb2_filename: str,
    file_mtime: float | None,
    parser_dispatcher: ParserDispatcher,
) -> tuple[CanonicalBook, ParsingReport]:
    """Получает CanonicalBook и ParsingReport из потока байтов FB2.

    Args:
        fb2_stream: Поток с содержимым FB2
        fb2_filename: Имя файла (для логов и определения формата)
        file_mtime: Время модификации файла (timestamp)
        parser_dispatcher: ParserDispatcher для парсинга (ОБЯЗАТЕЛЬНЫЙ для производительности)

    Returns:
        Кортеж (CanonicalBook, ParsingReport)
    """
    return parser_dispatcher.parse_to_canonical(fb2_stream, fb2_filename, file_mtime)


def get_canonical_book_for_anomaly_detection(
    fb2_stream: io.BytesIO,
    fb2_filename: str,
    file_mtime: float | None,
    parser_dispatcher: ParserDispatcher,
) -> tuple[CanonicalBook, ParsingReport]:
    """Получает CanonicalBook для детекции аномалий.

    Использует основной pipeline с ленивым рендерингом:
    - FB2Parser для извлечения FB2Book
    - date_extractor для публикации
    - Стандартная логика агрегации глав
    - Полная обработка сносок (КРИТИЧНО для FragmentDetector!)
    - Ленивый рендеринг: производительность без потери точности

    ПРОИЗВОДИТЕЛЬНОСТЬ: Принимает предварительно инициализированный parser_dispatcher
    для устранения накладных расходов на создание объектов в ProcessPoolExecutor.

    Args:
        fb2_stream: Поток с содержимым FB2
        fb2_filename: Имя файла (для логов и определения формата)
        file_mtime: Время модификации файла (timestamp)
        parser_dispatcher: Предварительно инициализированный ParserDispatcher (ОБЯЗАТЕЛЬНЫЙ для производительности)

    Returns:
        Кортеж (CanonicalBook, ParsingReport)
    """
    # Используем основной пайплайн с переданным parser_dispatcher для производительности
    return get_canonical_book_from_stream(fb2_stream, fb2_filename, file_mtime, parser_dispatcher)


def process_zip_archive(
    zip_path: str | Path,
    fb2_processor: Callable[[str, str, io.BytesIO, float | None], Any],
    fb2_filter: Callable[[str], bool] | None = None,
    error_handler: Callable[[str, str, Exception], None] | None = None,
) -> list[Any]:
    """
    Универсальная функция для обработки ZIP архивов с FB2 файлами.

    Использует StorageManager для чтения архивов, обеспечивая единый источник правды
    для работы с архивами во всем проекте. Совместима с различными типами хранилищ
    (Local/S3) через абстракцию StorageManager.

    Args:
        zip_path: Путь к ZIP архиву
        fb2_processor: Функция обработки FB2 файла с сигнатурой:
            (archive_path, fb2_filename, fb2_stream, mtime) -> result
        fb2_filter: Опциональный фильтр FB2 файлов (fb2_filename) -> bool
        error_handler: Опциональный обработчик ошибок (archive_path, fb2_filename, exception) -> None

    Returns:
        Список результатов обработки FB2 файлов

    Example:
        def process_fb2(archive_path, fb2_filename, fb2_stream, mtime):
            parser_dispatcher = get_components().parser_dispatcher
            canonical_book, _ = get_canonical_book_from_stream(fb2_stream, fb2_filename, mtime, parser_dispatcher)
            return {"title": canonical_book.title, "chapters": len(canonical_book.chapters)}

        results = process_zip_archive("/path/to/archive.zip", process_fb2)
    """
    results: list[Any] = []
    zip_path = str(zip_path)

    # Получаем StorageManager через синглтон компонентов
    components = get_components()
    if not components.has_full_functionality():
        # В тестовой среде StorageManager может быть недоступен - это нормально
        if error_handler:
            error_handler(zip_path, "", RuntimeError("Компоненты недоступны в тестовой среде"))
        return results

    storage_manager = components.storage_manager

    try:
        # Получаем список книжных файлов в архиве через StorageManager
        book_files = storage_manager.list_books_in_archive(zip_path)

        # Получаем время модификации архива для всех файлов
        # (поскольку StorageManager не предоставляет индивидуальные времена файлов)
        try:
            archive_mtime = Path(zip_path).stat().st_mtime
        except Exception:
            archive_mtime = None

        for book_filename in book_files:
            # Применяем фильтр если указан
            if fb2_filter and not fb2_filter(book_filename):
                continue

            try:
                # Читаем файл из архива через StorageManager
                fb2_stream = storage_manager.read_file_from_archive(zip_path, book_filename)

                # Обрабатываем файл через потоковый API
                result = fb2_processor(zip_path, book_filename, fb2_stream, archive_mtime)
                if result is not None:
                    results.append(result)

            except QuarantineError:
                # Тихо пропускаем файлы в карантине
                pass
            except Exception as e:
                if error_handler:
                    error_handler(zip_path, book_filename, e)

    except Exception as e:
        if error_handler:
            error_handler(zip_path, "", e)

    return results


# safe_cleanup_temp_file удалена - больше не используется в потоковой архитектуре


def collect_zip_archives(paths: list[str], limit: int = None) -> list[str]:
    """
    Собирает пути ко всем ZIP архивам в указанных директориях.

    Args:
        paths: Список путей для сканирования
        limit: Максимальное количество архивов (None = без лимита)

    Returns:
        Список путей к ZIP архивам
    """
    archive_paths = []

    for scan_path in paths:
        scan_path_obj = Path(scan_path)
        if not scan_path_obj.exists():
            continue

        if scan_path_obj.is_file() and scan_path_obj.suffix.lower() == ".zip":
            archive_paths.append(str(scan_path_obj))
        else:
            # Рекурсивно ищем ZIP файлы
            for zip_file in scan_path_obj.rglob("*.zip"):
                archive_paths.append(str(zip_file))

        # Применяем лимит если указан
        if limit and len(archive_paths) >= limit:
            archive_paths = archive_paths[:limit]
            break

    return archive_paths


def format_file_path(archive_path: str, fb2_filename: str) -> str:
    """
    Форматирует путь к файлу для вывода в стандартном формате.

    Args:
        archive_path: Путь к архиву
        fb2_filename: Имя FB2 файла

    Returns:
        Отформатированный путь
    """
    return f"{archive_path}::{fb2_filename}"


def get_first_non_empty_author(canonical_book: CanonicalBook) -> str:
    """Получает первого НЕ пустого автора из книги.

    Args:
        canonical_book: Каноническая модель книги

    Returns:
        Строка с именем первого непустого автора или "Неизвестный автор"
    """
    if not canonical_book.authors:
        return "Неизвестный автор"

    for author in canonical_book.authors:
        name_parts = [author.first_name, author.middle_name, author.last_name]
        # Убираем пустые части и проверяем, что остались непустые
        non_empty_parts = [part.strip() for part in name_parts if part and part.strip()]
        if non_empty_parts:
            return " ".join(non_empty_parts)

    return "Неизвестный автор"


def process_tasks_parallel(
    task_generator: Iterator[Any] | Generator[Any, None, None],
    worker_function: Callable[[Any], Any],
    result_handler: Callable[[Any, Any], None] | None = None,
    max_workers: int | None = None,
    window_size: int | None = None,
    worker_initializer: Callable[[], None] | None = None,
    progress_callback: Callable[[int, int], None] | None = None,
) -> dict[str, Any]:
    """Универсальная утилита для параллельной обработки задач с ProcessPoolExecutor.

    Объединяет общие паттерны из run_anomaly_analyzer.py и run_statistical_reporter.py:
    - Sliding window для управления памятью
    - Инициализация воркеров
    - Централизованная обработка результатов и ошибок
    - Сбор статистики выполнения

    Args:
        task_generator: Итератор или генератор задач для обработки
        worker_function: Функция-воркер для обработки одной задачи
        result_handler: Опциональная функция для обработки результатов
        max_workers: Количество процессов (по умолчанию: CPU count)
        window_size: Размер окна для sliding window (по умолчанию: max_workers * 2)
        worker_initializer: Функция инициализации для каждого процесса-воркера
        progress_callback: Функция для отслеживания прогресса (processed, total)

    Returns:
        Словарь со статистикой выполнения:
        - processed_tasks: количество обработанных задач
        - successful_tasks: количество успешных задач
        - failed_tasks: количество задач с ошибками
        - execution_time: время выполнения в секундах
        - errors: список ошибок

    Example:
        def process_book(task_args):
            archive_path, book_filename = task_args
            # ... обработка ...
            return result

        def handle_result(result, task_args):
            if result:
                print(f"Processed: {task_args}")

        stats = process_tasks_parallel(
            task_generator=[(archive, book) for archive, book in book_tasks],
            worker_function=process_book,
            result_handler=handle_result,
            max_workers=20
        )
    """
    import os

    # Параметры по умолчанию
    if max_workers is None:
        max_workers = min(os.cpu_count() or 4, 20)  # Разумное ограничение

    if window_size is None:
        window_size = min(max_workers * 2, 200)  # Предотвращаем переполнение памяти

    # Статистика выполнения
    stats: dict[str, Any] = {
        "processed_tasks": 0,
        "successful_tasks": 0,
        "failed_tasks": 0,
        "execution_time": 0.0,
        "errors": [],
    }

    start_time = time.time()

    try:
        with ProcessPoolExecutor(max_workers=max_workers, initializer=worker_initializer) as executor:
            # Реализация sliding window паттерна
            active_futures = {}  # {future: task_args}
            task_iter = iter(task_generator)

            # Заполняем начальное окно
            try:
                for _ in range(window_size):
                    task_args = next(task_iter)
                    future = executor.submit(worker_function, task_args)
                    active_futures[future] = task_args
            except StopIteration:
                pass  # Меньше задач чем размер окна

            # Главный цикл обработки с sliding window
            while active_futures:
                # Ждем завершения любой из активных задач
                for completed_future in as_completed(active_futures.keys()):
                    task_args = active_futures.pop(completed_future)
                    stats["processed_tasks"] += 1

                    try:
                        # Получаем результат
                        result = completed_future.result()
                        stats["successful_tasks"] += 1

                        # Обрабатываем результат через пользовательский handler
                        if result_handler:
                            result_handler(result, task_args)

                    except Exception as e:
                        # Обрабатываем ошибку выполнения задачи
                        stats["failed_tasks"] += 1
                        error_info = {
                            "task_args": task_args,
                            "error": str(e),
                            "error_type": type(e).__name__,
                        }
                        stats["errors"].append(error_info)

                    # Уведомляем о прогрессе
                    if progress_callback:
                        progress_callback(stats["processed_tasks"], None)  # total неизвестен для генераторов

                    # Добавляем новую задачу в окно если есть
                    try:
                        new_task_args = next(task_iter)
                        new_future = executor.submit(worker_function, new_task_args)
                        active_futures[new_future] = new_task_args
                    except StopIteration:
                        pass  # Больше задач нет

                    # Обрабатываем только одну завершенную задачу за итерацию
                    break

    except Exception as e:
        # Критическая ошибка в основном цикле
        stats["errors"].append(
            {
                "task_args": "executor_error",
                "error": str(e),
                "error_type": type(e).__name__,
            }
        )

    finally:
        stats["execution_time"] = time.time() - start_time

    return stats
