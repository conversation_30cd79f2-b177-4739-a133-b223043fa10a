"""
Парсер входных данных для диагностического анализа.

Обрабатывает различные форматы входных путей, включая локальные файлы
и файлы внутри архивов.
"""

import logging
from pathlib import Path

logger = logging.getLogger(__name__)


def parse_input_path(input_str: str) -> tuple[str | None, str]:
    """Парсит входную строку для определения цели анализа.

    Поддерживает два формата:
    - Локальный файл: /path/to/book.fb2
    - Файл в архиве: /path/to/archive.zip::book.fb2

    Args:
        input_str: Строка из аргумента --input

    Returns:
        <PERSON>ор<PERSON>еж (archive_path, file_path):
        - Если есть разделитель "::": (archive_path, file_in_archive)
        - Если нет разделителя: (None, file_path)
    """
    if "::" in input_str:
        # Формат archive_path::file_in_archive
        parts = input_str.split("::", 1)
        archive_path = parts[0].strip()
        file_in_archive = parts[1].strip()

        logger.debug(f"Распознан формат архива: '{archive_path}' :: '{file_in_archive}'")
        return archive_path, file_in_archive
    else:
        # Обычный локальный файл
        file_path = input_str.strip()
        logger.debug(f"Распознан локальный файл: '{file_path}'")
        return None, file_path


def validate_input_path(input_str: str) -> tuple[bool, str]:
    """Валидирует входной путь перед обработкой.

    Args:
        input_str: Входная строка для проверки

    Returns:
        Кортеж (is_valid, error_message):
        - is_valid: True если путь корректен
        - error_message: Описание ошибки если путь некорректен
    """
    if not input_str or not input_str.strip():
        return False, "Путь не может быть пустым"

    archive_path, file_path = parse_input_path(input_str)

    if archive_path:
        # Проверяем существование архива
        if not Path(archive_path).exists():
            return False, f"Архив не найден: {archive_path}"

        # Проверяем, что это действительно архив
        if not archive_path.lower().endswith((".zip", ".rar", ".7z", ".tar", ".tar.gz")):
            return False, f"Неподдерживаемый формат архива: {archive_path}"

        if not file_path:
            return False, "Не указан файл внутри архива"
    else:
        # Проверяем существование локального файла
        if not Path(file_path).exists():
            return False, f"Файл не найден: {file_path}"

    return True, ""


def get_file_extension(input_str: str) -> str:
    """Извлекает расширение файла из входной строки.

    Args:
        input_str: Входная строка с путем

    Returns:
        Расширение файла в нижнем регистре (без точки)
    """
    _, file_path = parse_input_path(input_str)

    if file_path:
        return Path(file_path).suffix.lower().lstrip(".")

    return ""


def extract_filename(input_str: str) -> str:
    """Извлекает имя файла из входной строки.

    Args:
        input_str: Входная строка с путем

    Returns:
        Имя файла без пути
    """
    _, file_path = parse_input_path(input_str)

    if file_path:
        return Path(file_path).name

    return ""
