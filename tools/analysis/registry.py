"""Управление JSON-реестром аномальных файлов."""

import json
import threading
from datetime import datetime
from pathlib import Path

# Внутренний импорт
from tools.analysis.types import AnomalyRegistryData


class AnomalyPathRegistry:
    """Управляет JSON реестром путей аномальных файлов."""

    def __init__(self, registry_path: Path):
        self.registry_path = registry_path
        self.registry_data: AnomalyRegistryData = {
            "metadata": {"created_at": None, "total_files": 0, "last_full_scan": None},
            "anomalies": {
                "trial_fragments": [],  # Ознакомительные фрагменты
                "small_content": [],  # Недостаточный объем контента
                "few_chapters": [],  # Мало глав
                "anthology_books": [],  # Сборники/антологии
                "broken_footnotes": [],  # Проблемы со сносками
            },
            "excluded_files": [],  # Файлы исключенные из детекции
        }
        self._lock = threading.Lock()

    def load_registry(self) -> bool:
        """Загружает существующий реестр. Возвращает True если успешно."""
        if not self.registry_path.exists():
            return False

        try:
            with open(self.registry_path, "r", encoding="utf-8") as f:
                loaded_data = json.load(f)

            # Обратная совместимость: добавляем excluded_files если его нет
            if "excluded_files" not in loaded_data:
                loaded_data["excluded_files"] = []

            # Обратная совместимость: добавляем новые типы аномалий если их нет
            if "anomalies" not in loaded_data:
                loaded_data["anomalies"] = {}

            anomalies = loaded_data["anomalies"]

            # Дополняем новыми типами аномалий (не перезаписывая существующие)
            new_anomaly_types: dict[str, list[str]] = {
                "small_books": [],  # Маленькие книги
                "anthology_books": [],  # Сборники/антологии
                "trial_fragments": [],  # Ознакомительные фрагменты
            }

            for anomaly_type, default_value in new_anomaly_types.items():
                if anomaly_type not in anomalies:
                    anomalies[anomaly_type] = default_value

            self.registry_data = loaded_data
            return True
        except (json.JSONDecodeError, FileNotFoundError):
            return False

    def is_empty(self) -> bool:
        """Проверяет, пуст ли реестр аномалий."""
        anomalies = self.registry_data.get("anomalies", {})
        return sum(len(paths) for paths in anomalies.values()) == 0

    def get_all_anomaly_paths(self) -> set[str]:
        """Возвращает множество всех аномальных путей для ресканирования, исключая помеченные excluded_files."""
        paths: set[str] = set()
        anomalies = self.registry_data.get("anomalies", {})
        excluded = set(self.registry_data.get("excluded_files", []))  # Исключённые пути

        # Добавляем только не исключённые пути
        for _anomaly_type, file_paths in anomalies.items():
            for fp in file_paths:
                if fp not in excluded:
                    paths.add(fp)

        return paths

    def get_unique_archive_paths(self) -> set[str]:
        """Извлекает уникальные пути архивов из аномальных файлов."""
        archive_paths = set()

        for anomaly_path in self.get_all_anomaly_paths():
            if "::" in anomaly_path:
                archive_path = anomaly_path.split("::")[0]
                archive_paths.add(archive_path)

        return archive_paths

    def get_anomaly_types_for_file(self, file_path: str) -> set[str]:
        """Возвращает множество типов аномалий для указанного файла."""
        found_types: set[str] = set()
        anomalies = self.registry_data.get("anomalies", {})
        for anomaly_type, paths in anomalies.items():
            if file_path in paths:
                found_types.add(anomaly_type)
        return found_types

    def add_anomaly_path(self, anomaly_type: str, archive_path: str, fb2_filename: str):
        """Добавляет аномальный файл в реестр."""
        full_path = f"{archive_path}::{fb2_filename}"

        with self._lock:
            if anomaly_type in self.registry_data["anomalies"]:
                if full_path not in self.registry_data["anomalies"][anomaly_type]:
                    self.registry_data["anomalies"][anomaly_type].append(full_path)

    def remove_anomaly_path(self, anomaly_type: str, archive_path: str, fb2_filename: str):
        """Удаляет исправленный файл из реестра."""
        full_path = f"{archive_path}::{fb2_filename}"

        with self._lock:
            if anomaly_type in self.registry_data["anomalies"]:
                if full_path in self.registry_data["anomalies"][anomaly_type]:
                    self.registry_data["anomalies"][anomaly_type].remove(full_path)

    def clear_and_rebuild(self):
        """Очищает реестр для нового полного сканирования, сохраняя excluded_files."""
        with self._lock:
            now = datetime.now().isoformat()
            # Сохраняем excluded_files при полном пересканировании
            excluded_files = self.registry_data.get("excluded_files", [])

            self.registry_data = {
                "metadata": {
                    "created_at": now,
                    "total_files": 0,
                    "last_full_scan": now,
                },
                "anomalies": {
                    # Типы аномалий на основе системы карантина
                    "trial_fragments": [],  # Ознакомительные фрагменты
                    "small_content": [],  # Недостаточный объем контента
                    "few_chapters": [],  # Мало глав
                    "anthology_books": [],  # Сборники/антологии
                    "broken_footnotes": [],  # Проблемы со сносками
                },
                "excluded_files": excluded_files,  # Сохраняем исключения
            }

    def is_excluded(self, archive_path: str, fb2_filename: str) -> bool:
        """Проверяет, исключен ли файл из детекции аномалий."""
        full_path = f"{archive_path}::{fb2_filename}"
        excluded_files = self.registry_data.get("excluded_files", [])
        return full_path in excluded_files

    def add_excluded_file(self, archive_path: str, fb2_filename: str):
        """Добавляет файл в список исключений."""
        full_path = f"{archive_path}::{fb2_filename}"

        with self._lock:
            excluded_files = self.registry_data.get("excluded_files", [])
            if full_path not in excluded_files:
                excluded_files.append(full_path)
                self.registry_data["excluded_files"] = excluded_files

    def remove_excluded_file(self, archive_path: str, fb2_filename: str):
        """Удаляет файл из списка исключений."""
        full_path = f"{archive_path}::{fb2_filename}"

        with self._lock:
            excluded_files = self.registry_data.get("excluded_files", [])
            if full_path in excluded_files:
                excluded_files.remove(full_path)
                self.registry_data["excluded_files"] = excluded_files

    def get_excluded_count(self) -> int:
        """Возвращает количество исключенных файлов."""
        return len(self.registry_data.get("excluded_files", []))

    def update_metadata(self, total_files: int):
        """Обновляет метаданные реестра."""
        with self._lock:
            now = datetime.now().isoformat()
            self.registry_data["metadata"]["total_files"] = total_files
            self.registry_data["metadata"]["last_full_scan"] = now

            if not self.registry_data["metadata"]["created_at"]:
                self.registry_data["metadata"]["created_at"] = now

    def save_registry(self):
        """Сохраняет реестр в JSON файл."""
        with self._lock:
            with open(self.registry_path, "w", encoding="utf-8") as f:
                json.dump(self.registry_data, f, ensure_ascii=False, indent=2)

    def get_anomaly_counts(self) -> dict[str, int]:
        """Возвращает количество аномалий по типам."""
        counts = {}
        anomalies = self.registry_data.get("anomalies", {})

        for anomaly_type, paths in anomalies.items():
            counts[anomaly_type] = len(paths)

        return counts
