"""
Утилиты сериализации для диагностического анализа.

Содержит функции для преобразования объектов канонической модели
в сериализуемые структуры данных.
"""

import sys
from dataclasses import asdict
from pathlib import Path
from typing import Any

# Добавление корневой директории проекта в sys.path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from app.processing.canonical_model import CanonicalBook
from app.processing.dto import is_author_empty


def canonical_book_to_dict(book: CanonicalBook) -> dict[str, Any]:
    """Преобразует CanonicalBook в словарь для сериализации.

    Выполняет специальную обработку сложных типов данных, таких как даты,
    и убирает несериализуемые поля. Использует ту же логику фильтрации
    пустых авторов, что и основной проект.

    Args:
        book: Объект CanonicalBook для сериализации

    Returns:
        Словарь, готовый для JSON сериализации
    """
    # Используем asdict для базовой сериализации
    book_dict = asdict(book)

    # Фильтруем пустых авторов с использованием основной логики проекта
    def _create_temp_author(author_dict):
        """Создает временный объект автора для проверки."""

        class TempAuthor:
            def __init__(self, data):
                self.first_name = data.get("first_name")
                self.last_name = data.get("last_name")
                self.middle_name = data.get("middle_name")
                self.nickname = data.get("nickname")

        return TempAuthor(author_dict)

    book_dict["authors"] = [
        author for author in book_dict["authors"] if not is_author_empty(_create_temp_author(author))
    ]

    book_dict["translators"] = [
        translator for translator in book_dict["translators"] if not is_author_empty(_create_temp_author(translator))
    ]

    # Явно преобразуем главы в нужный формат
    # asdict() не вызывает @property, поэтому делаем это явно
    book_dict["chapters"] = [
        {
            "title": chapter.title,
            "content_md": chapter.content_md,  # Это вызовет рендеринг если нужно
        }
        for chapter in book.chapters
    ]

    # Обрабатываем даты и другие сложные типы
    if book_dict.get("publication_date"):
        book_dict["publication_date"] = book_dict["publication_date"].isoformat()

    # Убираем сырую модель и служебные поля, которые не сериализуются
    book_dict.pop("raw_source_model", None)

    return book_dict


def clean_duplicated_fields(artifact_content: dict[str, Any]) -> dict[str, Any]:
    """Убирает дублированные поля из содержимого артефакта.

    Удаляет поля, которые уже присутствуют в других частях структуры
    для избежания дублирования данных.

    Args:
        artifact_content: Словарь с содержимым артефакта

    Returns:
        Очищенный от дублирования словарь
    """
    # Убираем поля, которые дублируются в book_id_info
    fields_to_remove = [
        "book_id_generation_date",
        "book_id_date_source",
    ]

    for field in fields_to_remove:
        artifact_content.pop(field, None)

    return artifact_content


def serialize_book_for_database(book: CanonicalBook, book_id: str, metadata_hash: str) -> dict[str, Any]:
    """Создает упрощенную структуру для database payload.

    Использует ту же логику фильтрации пустых авторов, что и основной проект.

    Args:
        book: Объект CanonicalBook
        book_id: Идентификатор книги
        metadata_hash: Хэш метаданных

    Returns:
        Словарь для database payload
    """
    # Фильтруем пустых авторов с использованием основной логики проекта
    valid_authors = [author for author in book.authors if not is_author_empty(author)]

    return {
        "book_id": book_id,
        "title": book.title,
        "authors": [
            {
                "first_name": author.first_name,
                "last_name": author.last_name,
                "middle_name": author.middle_name,
                "nickname": author.nickname,
            }
            for author in valid_authors
        ],
        "lang": book.lang,
        "series": book.sequences[0].name if book.sequences else None,
        "series_number": book.sequences[0].number if book.sequences else None,
        "genres": [genre.name if hasattr(genre, "name") else str(genre) for genre in book.genres],
        "annotation": book.annotation_md,
        "metadata_hash": metadata_hash,
        "file_format": book.source_format,
        "keywords": book.keywords,
        "raw_metadata": {},  # Упрощенная версия для отчета
    }


def create_summary_stats(
    book: CanonicalBook, anomalies_count: int, processing_time: float, parsing_report=None
) -> dict[str, Any]:
    """Создает статистику для отчета.

    Args:
        book: Объект CanonicalBook
        anomalies_count: Количество обнаруженных аномалий
        processing_time: Время обработки в секундах
        parsing_report: Отчет о парсинге с информацией о сносках (опционально)

    Returns:
        Словарь со статистикой
    """
    # Подсчитываем общее количество символов в главах (используем быструю оценку)
    total_chapter_chars = sum(chapter.estimated_content_length for chapter in book.chapters)

    # Базовая статистика
    stats = {
        "chapters_count": len(book.chapters),
        "total_chapter_characters": total_chapter_chars,
        "annotation_length": len(book.annotation_md),
        "authors_count": len(book.authors),
        "genres_count": len(book.genres),
        "sequences_count": len(book.sequences),
        "processing_time_seconds": round(processing_time, 2),
        "anomalies_count": anomalies_count,
    }

    # Добавляем статистику по сноскам, если доступен parsing_report
    if parsing_report:
        stats["footnotes_total"] = parsing_report.total_footnotes
        stats["footnotes_broken"] = len(parsing_report.broken_footnotes)
        stats["footnotes_success_rate"] = (
            round(
                (parsing_report.total_footnotes - len(parsing_report.broken_footnotes))
                / parsing_report.total_footnotes
                * 100,
                1,
            )
            if parsing_report.total_footnotes > 0
            else 100.0
        )
    else:
        # Если parsing_report недоступен, устанавливаем значения по умолчанию
        stats["footnotes_total"] = 0
        stats["footnotes_broken"] = 0
        stats["footnotes_success_rate"] = 100.0

    return stats
