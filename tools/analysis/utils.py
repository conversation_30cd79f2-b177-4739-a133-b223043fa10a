#!/usr/bin/env python3
"""
Утилиты для анализатора аномалий.

Содержит специализированные функции для:
- Переводов типов аномалий на русский язык
- Форматирования вывода анализатора аномалий
- Другие функции, связанные только с анализом аномалий

Эти функции изолированы от общих утилит tools/utils.py
для соблюдения принципа высокой связности.
"""

import sys
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from app.processing.canonical_model import CanonicalBook
from app.utils.helpers import extract_source_id


def get_anomaly_type_translations() -> dict[str, str]:
    """Возвращает словарь переводов типов аномалий на русский язык.

    Returns:
        Словарь {английский_тип: русский_перевод}
    """
    return {
        "trial_fragments": "фрагмент",
        "small_content": "малый контент",
        "few_chapters": "мало глав",
        "anthology_books": "антология",
        "broken_footnotes": "сломанные сноски",
    }


def get_anomaly_type_translations_plural() -> dict[str, str]:
    """Возвращает словарь переводов типов аномалий на русский язык (множественное число).

    Returns:
        Словарь {английский_тип: русский_перевод_множественное}
    """
    return {
        "trial_fragments": "фрагменты",
        "small_content": "малый контент",
        "few_chapters": "мало глав",
        "anthology_books": "антологии",
        "broken_footnotes": "сломанные сноски",
    }


def format_book_log_entry(
    archive_path: str,
    fb2_filename: str,
    canonical_book: CanonicalBook,
    anomalies: list[str] = None,
) -> str:
    """Форматирует строку лога для книги в стандартном формате анализатора аномалий.

    Args:
        archive_path: Путь к архиву
        fb2_filename: Имя FB2 файла
        canonical_book: Каноническая модель книги
        anomalies: Список типов аномалий (опционально)

    Returns:
        Отформатированная строка лога
    """
    # Используем каноническую функцию извлечения source_id
    source_id = extract_source_id(Path(fb2_filename))

    # Получаем первого непустого автора
    from tools.utils import get_first_non_empty_author

    author = get_first_non_empty_author(canonical_book)

    title = canonical_book.title or "Без названия"

    # Извлекаем только название архива из полного пути
    archive_name = Path(archive_path).name

    if anomalies:
        # Переводим типы аномалий на русский для лога
        anomaly_translations = get_anomaly_type_translations()
        anomaly_list = [anomaly_translations.get(a, a) for a in anomalies]
        anomaly_str = ", ".join(anomaly_list)
        return f"{archive_name}/{source_id} - {author}. {title} - ⚠️  {anomaly_str}"
    else:
        return f"{archive_name}/{source_id} - {author}. {title} - OK"
