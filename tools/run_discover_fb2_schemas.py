#!/usr/bin/env python3
"""
Анализатор частотности и структурных аномалий для FALLBACK книг.

Автоматически выявляет потенциальные маркеры глав в книгах, которые не обрабатываются
существующими стратегиями. Использует статистический анализ для поиска повторяющихся
текстовых и структурных паттернов.

Архитектура:
- Producer-Consumer с ProcessPoolExecutor
- Частотный анализ текстовых паттернов
- <PERSON><PERSON><PERSON><PERSON><PERSON> структурных сигнатур XML/FB2
- Агрегация результатов по всей базе книг

Использование:
    python run_discover_fb2_schemas.py --input fallback_books.txt --workers 4
    python run_discover_fb2_schemas.py --input fallback_books.txt --min-occurrences 5 --top-results 100
"""

import argparse
import csv
import logging
import re
import sys
from collections import defaultdict
from concurrent.futures import ProcessPoolExecutor, as_completed
from dataclasses import dataclass
from pathlib import Path
from typing import Optional

# Добавляем корневую директорию проекта в путь
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.processing.parsers.fb2.fb2_parser import FB2Parser  # noqa: E402
from app.storage import LocalStorageManager  # noqa: E402

logger = logging.getLogger(__name__)


@dataclass
class PatternCandidate:
    """Кандидат в паттерн с метриками."""

    pattern: str
    book_count: int  # В скольких книгах встретился
    total_occurrences: int  # Общее количество вхождений
    avg_per_book: float  # Среднее количество на книгу
    example_book: str  # Пример книги где встретился


@dataclass
class BookAnalysisResult:
    """Результат анализа одной книги."""

    book_path: str
    text_patterns: dict[str, int]
    structure_patterns: dict[str, int]
    error: Optional[str] = None


class FB2SchemaDiscoverer:
    """Анализатор для автоматического выявления FB2 паттернов в FALLBACK книгах."""

    # Минимальное количество вхождений паттерна в одной книге
    MIN_OCCURRENCES_PER_BOOK = 3

    # Максимальная длина текстового паттерна (отсеиваем длинные предложения)
    MAX_PATTERN_LENGTH = 100

    # Минимальная длина текстового паттерна (отсеиваем одиночные символы)
    MIN_PATTERN_LENGTH = 2

    def __init__(self, min_occurrences: int = 3, max_workers: int = 4, checkpoint_interval: int = 1000):
        """Инициализирует анализатор.

        Args:
            min_occurrences: Минимальное количество вхождений паттерна в книге
            max_workers: Количество воркеров для параллельной обработки
            checkpoint_interval: Интервал промежуточного сохранения (количество книг)
        """
        self.min_occurrences = min_occurrences
        self.max_workers = max_workers
        self.checkpoint_interval = checkpoint_interval
        self.storage_manager = LocalStorageManager()
        self.parser = FB2Parser()

        # Глобальные счетчики
        self.global_text_patterns: dict[str, dict[str, int]] = defaultdict(
            lambda: {"book_count": 0, "total_occurrences": 0}
        )
        self.global_structure_patterns: dict[str, dict[str, int]] = defaultdict(
            lambda: {"book_count": 0, "total_occurrences": 0}
        )
        self.pattern_examples: dict[str, str] = {}  # Примеры книг для каждого паттерна

    def analyze_books_from_file(
        self, input_file: Path, output_file: Optional[Path] = None
    ) -> tuple[list[PatternCandidate], list[PatternCandidate]]:
        """Анализирует книги из файла со списком FALLBACK случаев.

        Args:
            input_file: Путь к файлу со списком книг для анализа
            output_file: Путь к выходному файлу для промежуточного сохранения

        Returns:
            Кортеж из списков текстовых и структурных кандидатов
        """
        logger.info(f"🔍 Начинаем анализ FALLBACK книг из {input_file}")

        # Читаем список книг
        book_tasks = self._parse_input_file(input_file)
        logger.info(f"📚 Найдено {len(book_tasks)} книг для анализа")

        if not book_tasks:
            logger.warning("Нет книг для анализа")
            return [], []

        # Обрабатываем книги параллельно с промежуточным сохранением
        results = self._process_books_parallel(book_tasks, output_file)

        # Агрегируем результаты
        self._aggregate_results(results)

        # Формируем финальные отчеты
        text_candidates = self._build_text_candidates()
        structure_candidates = self._build_structure_candidates()

        logger.info(
            f"✅ Анализ завершен. Найдено {len(text_candidates)} текстовых и {len(structure_candidates)} структурных кандидатов"
        )

        return text_candidates, structure_candidates

    def _parse_input_file(self, input_file: Path) -> list[tuple[str, str]]:
        """Парсит входной файл со списком книг.

        Ожидает формат: archive_path::book_filename или просто пути к архивам

        Args:
            input_file: Путь к входному файлу

        Returns:
            Список кортежей (archive_path, book_filename)
        """
        book_tasks = []

        try:
            with open(input_file, "r", encoding="utf-8") as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line or line.startswith("#"):
                        continue

                    try:
                        if "::" in line:
                            # Формат: archive_path::book_filename
                            archive_path, book_filename = line.split("::", 1)
                            book_tasks.append((archive_path.strip(), book_filename.strip()))
                        else:
                            # Формат: просто путь к архиву - берем первый FB2 файл
                            archive_path = line.strip()
                            book_tasks.append((archive_path, None))
                    except Exception as e:
                        logger.warning(f"Не удалось распарсить строку {line_num}: {line} - {e}")

        except Exception as e:
            logger.error(f"Ошибка чтения файла {input_file}: {e}")
            return []

        return book_tasks

    def _process_books_parallel(
        self, book_tasks: list[tuple[str, str]], output_file: Optional[Path] = None
    ) -> list[BookAnalysisResult]:
        """Обрабатывает книги параллельно с помощью ProcessPoolExecutor.

        Args:
            book_tasks: Список задач для обработки

        Returns:
            Список результатов анализа
        """
        results = []
        processed_count = 0
        error_count = 0

        with ProcessPoolExecutor(max_workers=self.max_workers) as executor:
            # Запускаем задачи
            future_to_task = {
                executor.submit(analyze_book_worker, archive_path, book_filename, self.min_occurrences): (
                    archive_path,
                    book_filename,
                )
                for archive_path, book_filename in book_tasks
            }

            # Собираем результаты
            for future in as_completed(future_to_task):
                archive_path, book_filename = future_to_task[future]

                try:
                    result = future.result()
                    results.append(result)

                    if result.error:
                        error_count += 1
                        logger.warning(f"Ошибка обработки {result.book_path}: {result.error}")
                    else:
                        processed_count += 1

                    # Прогресс каждые 100 книг
                    if (processed_count + error_count) % 100 == 0:
                        logger.info(
                            f"Обработано: {processed_count + error_count}/{len(book_tasks)} книг (ошибок: {error_count})"
                        )

                    # Потоковая запись каждые 1000 успешных записей
                    if output_file and not result.error and processed_count % 1000 == 0:
                        self._flush_results_to_csv(results[-1000:], output_file, processed_count)

                except Exception as e:
                    error_count += 1
                    logger.error(f"Критическая ошибка обработки {archive_path}::{book_filename}: {e}")

        logger.info(f"Обработка завершена: {processed_count} успешно, {error_count} ошибок")

        # Финальная запись всех оставшихся результатов
        if output_file and results:
            remaining_results = [r for r in results if not r.error]
            if remaining_results:
                # Записываем все результаты, которые еще не были записаны
                last_flush_count = (processed_count // 1000) * 1000
                remaining_count = processed_count - last_flush_count
                if remaining_count > 0:
                    self._flush_results_to_csv(remaining_results[-remaining_count:], output_file, processed_count)

        return results

    def _aggregate_results(self, results: list[BookAnalysisResult]) -> None:
        """Агрегирует результаты анализа всех книг.

        Args:
            results: Список результатов анализа книг
        """
        logger.info("📊 Агрегируем результаты...")

        for result in results:
            if result.error:
                continue

            # Агрегируем текстовые паттерны
            for pattern, count in result.text_patterns.items():
                self.global_text_patterns[pattern]["book_count"] += 1
                self.global_text_patterns[pattern]["total_occurrences"] += count

                # Сохраняем пример книги
                if pattern not in self.pattern_examples:
                    self.pattern_examples[pattern] = result.book_path

            # Агрегируем структурные паттерны
            for pattern, count in result.structure_patterns.items():
                self.global_structure_patterns[pattern]["book_count"] += 1
                self.global_structure_patterns[pattern]["total_occurrences"] += count

                # Сохраняем пример книги
                if pattern not in self.pattern_examples:
                    self.pattern_examples[pattern] = result.book_path

    def _build_text_candidates(self) -> list[PatternCandidate]:
        """Строит список кандидатов текстовых паттернов."""
        candidates = []

        for pattern, stats in self.global_text_patterns.items():
            book_count = stats["book_count"]
            total_occurrences = stats["total_occurrences"]
            avg_per_book = total_occurrences / book_count if book_count > 0 else 0
            example_book = self.pattern_examples.get(pattern, "")

            candidates.append(
                PatternCandidate(
                    pattern=pattern,
                    book_count=book_count,
                    total_occurrences=total_occurrences,
                    avg_per_book=avg_per_book,
                    example_book=example_book,
                )
            )

        # Сортируем по количеству книг (это важнее общего количества вхождений)
        candidates.sort(key=lambda x: x.book_count, reverse=True)
        return candidates

    def _build_structure_candidates(self) -> list[PatternCandidate]:
        """Строит список кандидатов структурных паттернов."""
        candidates = []

        for pattern, stats in self.global_structure_patterns.items():
            book_count = stats["book_count"]
            total_occurrences = stats["total_occurrences"]
            avg_per_book = total_occurrences / book_count if book_count > 0 else 0
            example_book = self.pattern_examples.get(pattern, "")

            candidates.append(
                PatternCandidate(
                    pattern=pattern,
                    book_count=book_count,
                    total_occurrences=total_occurrences,
                    avg_per_book=avg_per_book,
                    example_book=example_book,
                )
            )

        # Сортируем по количеству книг
        candidates.sort(key=lambda x: x.book_count, reverse=True)
        return candidates

    def _flush_results_to_csv(
        self, recent_results: list[BookAnalysisResult], output_file: Path, processed_count: int
    ) -> None:
        """Записывает последние результаты в CSV файл (потоковая запись).

        Args:
            recent_results: Последние результаты анализа книг
            output_file: Путь к выходному CSV файлу
            processed_count: Количество обработанных книг
        """
        # Проверяем, нужно ли создать файл с заголовками
        file_exists = output_file.exists()

        # Открываем файл в режиме добавления
        with open(output_file, "a", newline="", encoding="utf-8") as csvfile:
            writer = csv.writer(csvfile)

            # Записываем заголовки только если файл новый
            if not file_exists:
                writer.writerow(["Book_Path", "Pattern_Type", "Pattern", "Occurrences"])

            # Записываем результаты последних книг
            for result in recent_results:
                if result.error:
                    continue

                # Записываем текстовые паттерны
                for pattern, count in result.text_patterns.items():
                    writer.writerow([result.book_path, "TEXT", pattern, count])

                # Записываем структурные паттерны
                for pattern, count in result.structure_patterns.items():
                    writer.writerow([result.book_path, "STRUCTURE", pattern, count])

            # Принудительно сохраняем на диск
            csvfile.flush()

        logger.info(
            f"💾 Записано в CSV: {len([r for r in recent_results if not r.error])} книг (всего обработано: {processed_count})"
        )


def analyze_book_worker(archive_path: str, book_filename: Optional[str], min_occurrences: int) -> BookAnalysisResult:
    """Воркер для анализа одной книги.

    Выполняется в отдельном процессе для параллельной обработки.

    Args:
        archive_path: Путь к архиву
        book_filename: Имя файла книги в архиве (или None для автоопределения)
        min_occurrences: Минимальное количество вхождений паттерна

    Returns:
        Результат анализа книги
    """
    book_path = f"{archive_path}::{book_filename}" if book_filename else archive_path

    try:
        # Инициализируем компоненты в воркере
        storage_manager = LocalStorageManager()
        parser = FB2Parser()

        # Читаем книгу
        if book_filename:
            # Конкретный файл в архиве
            book_stream = storage_manager.read_file_from_archive(archive_path, book_filename)
        else:
            # Первый FB2 файл в архиве
            book_files = storage_manager.list_books_in_archive(archive_path)
            if not book_files:
                return BookAnalysisResult(book_path, {}, {}, "Нет книжных файлов в архиве")

            # Берем первый FB2 файл
            fb2_files = [f for f in book_files if f.lower().endswith(".fb2")]
            if not fb2_files:
                return BookAnalysisResult(book_path, {}, {}, "Нет FB2 файлов в архиве")

            first_fb2 = fb2_files[0]
            book_stream = storage_manager.read_file_from_archive(archive_path, first_fb2)
            book_path = f"{archive_path}::{first_fb2}"  # Обновляем путь для отчета

        if not book_stream:
            return BookAnalysisResult(book_path, {}, {}, "Не удалось прочитать книгу")

        # Парсим FB2 из потока
        fb2_book = parser.parse(book_stream)
        if not fb2_book or not fb2_book.bodies:
            return BookAnalysisResult(book_path, {}, {}, "Не удалось распарсить FB2 или нет тела книги")

        # Анализируем паттерны
        text_patterns, structure_patterns = _analyze_fb2_patterns(fb2_book, min_occurrences)

        return BookAnalysisResult(
            book_path=book_path, text_patterns=text_patterns, structure_patterns=structure_patterns
        )

    except Exception as e:
        return BookAnalysisResult(book_path, {}, {}, f"Ошибка анализа: {str(e)}")


def _analyze_fb2_patterns(fb2_book, min_occurrences: int) -> tuple[dict[str, int], dict[str, int]]:
    """Анализирует FB2 книгу на предмет повторяющихся паттернов.

    Args:
        fb2_book: Распарсенная FB2 книга
        min_occurrences: Минимальное количество вхождений для фильтрации

    Returns:
        Кортеж из словарей текстовых и структурных паттернов
    """
    text_frequencies: dict[str, int] = defaultdict(int)
    structure_frequencies: dict[str, int] = defaultdict(int)

    # Обрабатываем все тела книги
    for body in fb2_book.bodies:
        if not body.sections:
            continue

        # Рекурсивно обрабатываем все секции
        for section in body.sections:
            _process_element_recursive(section, text_frequencies, structure_frequencies)

    # Фильтруем по минимальному количеству вхождений
    text_patterns = {pattern: count for pattern, count in text_frequencies.items() if count >= min_occurrences}
    structure_patterns = {
        pattern: count for pattern, count in structure_frequencies.items() if count >= min_occurrences
    }

    return text_patterns, structure_patterns


def _process_element_recursive(
    element, text_frequencies: dict[str, int], structure_frequencies: dict[str, int]
) -> None:
    """Рекурсивно обрабатывает элемент FB2 для поиска паттернов.

    Args:
        element: FB2 элемент для обработки
        text_frequencies: Словарь частот текстовых паттернов
        structure_frequencies: Словарь частот структурных паттернов
    """
    # Импортируем типы FB2 элементов

    # Анализируем текстовое содержимое
    text_content = _extract_element_text(element)
    if text_content:
        normalized_text = _normalize_text_for_pattern(text_content)
        if _is_valid_text_pattern(normalized_text):
            text_frequencies[normalized_text] += 1

    # Анализируем структурную сигнатуру
    structure_signature = _get_structural_signature(element)
    if structure_signature:
        structure_frequencies[structure_signature] += 1

    # Рекурсивно обрабатываем дочерние элементы
    if hasattr(element, "content") and element.content:
        for child in element.content:
            _process_element_recursive(child, text_frequencies, structure_frequencies)


def _extract_element_text(element) -> str:
    """Извлекает текстовое содержимое из FB2 элемента.

    Args:
        element: FB2 элемент

    Returns:
        Текстовое содержимое элемента
    """
    from app.processing.parsers.fb2.fb2_model import Emphasis, Paragraph, Section, Strong

    if isinstance(element, str):
        return element.strip()

    # Для разных типов элементов используем разные стратегии извлечения текста
    if isinstance(element, (Strong, Emphasis)):
        # Для форматированных элементов берем атрибут text
        return getattr(element, "text", "").strip()

    if isinstance(element, Paragraph):
        # Для параграфов собираем текст из всех дочерних элементов
        text_parts = []
        if hasattr(element, "content") and element.content:
            for child in element.content:
                if isinstance(child, str):
                    text_parts.append(child.strip())
                elif isinstance(child, (Strong, Emphasis)):
                    child_text = getattr(child, "text", "").strip()
                    if child_text:
                        text_parts.append(child_text)
        return " ".join(filter(None, text_parts))

    if isinstance(element, Section):
        # Для секций не извлекаем весь текст, только заголовок
        if hasattr(element, "title") and element.title:
            # Заголовок секции может содержать параграфы
            title_parts = []
            if hasattr(element.title, "elements") and element.title.elements:
                for title_elem in element.title.elements:
                    title_text = _extract_element_text(title_elem)
                    if title_text:
                        title_parts.append(title_text)
            return " ".join(filter(None, title_parts))
        return ""

    # Для других элементов пытаемся извлечь атрибут text
    if hasattr(element, "text") and element.text:
        return element.text.strip()

    return ""


def _normalize_text_for_pattern(text: str) -> str:
    """Нормализует текст для поиска паттернов.

    Применяет умную нормализацию, сохраняя важные символы и структуру.

    Args:
        text: Исходный текст

    Returns:
        Нормализованный текст
    """
    if not text:
        return ""

    # Базовая очистка
    text = text.strip()

    # Нормализуем пробелы
    text = re.sub(r"\s+", " ", text)

    # Приводим к нижнему регистру для унификации
    text = text.lower()

    # Специальная обработка римских цифр
    text = _normalize_roman_numerals(text)

    # Специальная обработка чисел
    text = _normalize_numbers(text)

    return text


def _normalize_roman_numerals(text: str) -> str:
    """Нормализует римские цифры в тексте.

    Заменяет конкретные римские цифры на плейсхолдер [ROMAN].

    Args:
        text: Текст с возможными римскими цифрами

    Returns:
        Текст с нормализованными римскими цифрами
    """
    # Паттерн для римских цифр
    roman_pattern = r"\b(?:i{1,3}|iv|v|vi{0,3}|ix|x{1,3}|xl|l|lx{0,3}|xc|c{1,3}|cd|d|dc{0,3}|cm|m{1,3})\b"

    # Заменяем римские цифры на плейсхолдер
    normalized = re.sub(roman_pattern, "[ROMAN]", text, flags=re.IGNORECASE)

    return normalized


def _normalize_numbers(text: str) -> str:
    """Нормализует арабские числа в тексте.

    Заменяет конкретные числа на плейсхолдер [NUMBER].

    Args:
        text: Текст с возможными числами

    Returns:
        Текст с нормализованными числами
    """
    # Паттерн для чисел (включая числа в скобках, с точками и т.д.)
    number_patterns = [
        r"\b\d+\b",  # Обычные числа
        r"\[\d+\]",  # Числа в квадратных скобках
        r"\(\d+\)",  # Числа в круглых скобках
        r"\d+\.",  # Числа с точкой
    ]

    normalized = text
    for pattern in number_patterns:
        normalized = re.sub(pattern, "[NUMBER]", normalized)

    return normalized


def _is_valid_text_pattern(text: str) -> bool:
    """Проверяет, является ли текст валидным кандидатом в паттерн.

    Фильтрует слишком длинные, короткие или бессмысленные тексты.

    Args:
        text: Нормализованный текст

    Returns:
        True если текст может быть паттерном
    """
    if not text:
        return False

    # Проверяем длину
    if len(text) < 2 or len(text) > 100:
        return False

    # Отсеиваем тексты, состоящие только из пунктуации
    if re.match(r"^[^\w\s]+$", text):
        return False

    # Отсеиваем тексты с слишком большим количеством слов (вероятно, обычный текст)
    word_count = len(text.split())
    if word_count > 10:
        return False

    return True


def _get_structural_signature(element) -> str:
    """Создает структурную сигнатуру FB2 элемента.

    Строит иерархическую подпись типов элементов с учетом контекста.

    Args:
        element: FB2 элемент

    Returns:
        Структурная сигнатура элемента
    """
    from app.processing.parsers.fb2.fb2_model import Emphasis, EmptyLine, Paragraph, Section, Strong

    # Пропускаем строковые элементы и EmptyLine
    if isinstance(element, (str, EmptyLine)):
        return ""

    # Определяем тип элемента
    element_type = type(element).__name__.lower()

    # Для параграфов анализируем содержимое более детально
    if isinstance(element, Paragraph):
        if hasattr(element, "content") and element.content:
            # Ищем специфичные паттерны в параграфах
            has_strong = any(isinstance(child, Strong) for child in element.content)
            has_emphasis = any(isinstance(child, Emphasis) for child in element.content)

            if has_strong and has_emphasis:
                return "paragraph>strong+emphasis"
            elif has_strong:
                return "paragraph>strong"
            elif has_emphasis:
                return "paragraph>emphasis"
            else:
                return "paragraph>text"
        return "paragraph>empty"

    # Для секций указываем, есть ли заголовок
    if isinstance(element, Section):
        has_title = hasattr(element, "title") and element.title
        if has_title:
            return "section>titled"
        else:
            return "section>untitled"

    # Для форматированных элементов возвращаем простой тип
    if isinstance(element, (Strong, Emphasis)):
        return element_type

    return element_type


def print_text_candidates_report(candidates: list[PatternCandidate], top_n: int = 50) -> None:
    """Выводит отчет по текстовым кандидатам в паттерны.

    Args:
        candidates: Список кандидатов
        top_n: Количество топ-результатов для вывода
    """
    print("\n" + "=" * 100)
    print(f"📝 ТОП-{top_n} ТЕКСТОВЫХ ПАТТЕРНОВ-КАНДИДАТОВ")
    print("=" * 100)

    if not candidates:
        print("Текстовые паттерны не найдены.")
        return

    # Заголовок таблицы
    print(f"{'Паттерн':<40} | {'Книг':<8} | {'Всего':<8} | {'Ср/книгу':<8} | {'Пример книги':<30}")
    print("-" * 100)

    # Выводим топ-N результатов
    for candidate in candidates[:top_n]:
        pattern_display = candidate.pattern[:37] + "..." if len(candidate.pattern) > 40 else candidate.pattern
        example_display = (
            candidate.example_book[-27] + "..." if len(candidate.example_book) > 30 else candidate.example_book
        )

        print(
            f"{pattern_display:<40} | {candidate.book_count:<8} | {candidate.total_occurrences:<8} | "
            f"{candidate.avg_per_book:<8.1f} | {example_display:<30}"
        )


def print_structure_candidates_report(candidates: list[PatternCandidate], top_n: int = 50) -> None:
    """Выводит отчет по структурным кандидатам в паттерны.

    Args:
        candidates: Список кандидатов
        top_n: Количество топ-результатов для вывода
    """
    print("\n" + "=" * 100)
    print(f"🏗️ ТОП-{top_n} СТРУКТУРНЫХ ПАТТЕРНОВ-КАНДИДАТОВ")
    print("=" * 100)

    if not candidates:
        print("Структурные паттерны не найдены.")
        return

    # Заголовок таблицы
    print(f"{'Структурная сигнатура':<40} | {'Книг':<8} | {'Всего':<8} | {'Ср/книгу':<8} | {'Пример книги':<30}")
    print("-" * 100)

    # Выводим топ-N результатов
    for candidate in candidates[:top_n]:
        pattern_display = candidate.pattern[:37] + "..." if len(candidate.pattern) > 40 else candidate.pattern
        example_display = (
            candidate.example_book[-27] + "..." if len(candidate.example_book) > 30 else candidate.example_book
        )

        print(
            f"{pattern_display:<40} | {candidate.book_count:<8} | {candidate.total_occurrences:<8} | "
            f"{candidate.avg_per_book:<8.1f} | {example_display:<30}"
        )


def save_candidates_to_csv(
    text_candidates: list[PatternCandidate], structure_candidates: list[PatternCandidate], output_file: Path
) -> None:
    """Сохраняет кандидатов в CSV файл для дальнейшего анализа.

    Args:
        text_candidates: Список текстовых кандидатов
        structure_candidates: Список структурных кандидатов
        output_file: Путь к выходному CSV файлу
    """
    logger.info(f"💾 Сохраняем результаты в {output_file}")

    with open(output_file, "w", newline="", encoding="utf-8") as csvfile:
        writer = csv.writer(csvfile)

        # Заголовок
        writer.writerow(["Type", "Pattern", "Book_Count", "Total_Occurrences", "Avg_Per_Book", "Example_Book"])

        # Текстовые паттерны
        for candidate in text_candidates:
            writer.writerow(
                [
                    "TEXT",
                    candidate.pattern,
                    candidate.book_count,
                    candidate.total_occurrences,
                    f"{candidate.avg_per_book:.1f}",
                    candidate.example_book,
                ]
            )

        # Структурные паттерны
        for candidate in structure_candidates:
            writer.writerow(
                [
                    "STRUCTURE",
                    candidate.pattern,
                    candidate.book_count,
                    candidate.total_occurrences,
                    f"{candidate.avg_per_book:.1f}",
                    candidate.example_book,
                ]
            )


def main():
    """Главная функция скрипта."""
    parser = argparse.ArgumentParser(
        description="Анализатор FB2 паттернов для FALLBACK книг",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Примеры использования:
  python run_discover_fb2_schemas.py --input fallback_books.txt
  python run_discover_fb2_schemas.py --input fallback_books.txt --workers 8 --min-occurrences 5
  python run_discover_fb2_schemas.py --input fallback_books.txt --top-results 100 --output results.csv
        """,
    )

    parser.add_argument(
        "--input", "-i", type=Path, required=True, help="Путь к файлу со списком FALLBACK книг для анализа"
    )

    parser.add_argument("--output", "-o", type=Path, help="Путь к выходному CSV файлу (опционально)")

    parser.add_argument(
        "--workers", "-w", type=int, default=4, help="Количество параллельных воркеров (по умолчанию: 4)"
    )

    parser.add_argument(
        "--min-occurrences",
        "-m",
        type=int,
        default=3,
        help="Минимальное количество вхождений паттерна в книге (по умолчанию: 3)",
    )

    parser.add_argument(
        "--top-results", "-t", type=int, default=50, help="Количество топ-результатов для вывода (по умолчанию: 50)"
    )

    parser.add_argument("--verbose", "-v", action="store_true", help="Подробный вывод")

    args = parser.parse_args()

    # Настройка логирования
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(level=log_level, format="%(asctime)s - %(levelname)s - %(message)s", datefmt="%H:%M:%S")

    # Отключаем избыточные логи FB2Parser
    logging.getLogger("app.processing.parsers.fb2.fb2_parser").setLevel(logging.WARNING)

    # Проверяем входной файл
    if not args.input.exists():
        logger.error(f"Входной файл не найден: {args.input}")
        sys.exit(1)

    # Создаем анализатор
    discoverer = FB2SchemaDiscoverer(
        min_occurrences=args.min_occurrences,
        max_workers=args.workers,
        checkpoint_interval=1000,  # Промежуточное сохранение каждые 1000 книг
    )

    try:
        # Запускаем анализ
        text_candidates, structure_candidates = discoverer.analyze_books_from_file(args.input, args.output)

        # Выводим отчеты
        print_text_candidates_report(text_candidates, args.top_results)
        print_structure_candidates_report(structure_candidates, args.top_results)

        # Сохраняем агрегированные результаты в CSV только если не использовалась потоковая запись
        if args.output:
            # Проверяем, был ли файл уже создан потоковой записью
            if args.output.exists():
                logger.info(f"📄 Потоковые данные уже сохранены в {args.output}")
                logger.info("📊 Для агрегированного отчета используйте отдельный файл")
            else:
                save_candidates_to_csv(text_candidates, structure_candidates, args.output)

        # Итоговая статистика
        print("\n🎯 ИТОГОВАЯ СТАТИСТИКА:")
        print(f"   Найдено текстовых паттернов: {len(text_candidates)}")
        print(f"   Найдено структурных паттернов: {len(structure_candidates)}")

        if text_candidates:
            top_text = text_candidates[0]
            print(f"   Самый популярный текстовый паттерн: '{top_text.pattern}' ({top_text.book_count} книг)")

        if structure_candidates:
            top_structure = structure_candidates[0]
            print(f"   Самая популярная структура: '{top_structure.pattern}' ({top_structure.book_count} книг)")

    except KeyboardInterrupt:
        logger.info("Анализ прерван пользователем")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Критическая ошибка: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
