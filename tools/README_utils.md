# Tools Utils - Общие утилиты для скриптов

## Назначение

Модуль `tools/utils.py` устраняет дублирование логики в скриптах tools/. 

**Проблема:** Ранее каждый скрипт содержал практически идентичный код для:
- Потокового чтения ZIP архивов
- Инициализации проектных компонентов (ParserDispatcher, FragmentDetector)
- Обработки ошибок парсинга

**Решение:** Общие переиспользуемые функции с единой логикой, использующие архитектуру StorageManager.

## Основные функции

### `process_zip_archive(zip_path, fb2_processor, fb2_filter=None, error_handler=None)`

Универсальная обработка ZIP архивов с потоковым чтением. Заменяет дублированный код в скриптах.
Использует только потоковую архитектуру через io.BytesIO.

```python
# Потоковое чтение (единственный поддерживаемый стиль)
def my_book_analyzer(archive_path: str, fb2_filename: str, fb2_stream: io.BytesIO, mtime: float) -> dict:
    canonical_book = get_canonical_book_from_stream(fb2_stream, fb2_filename, mtime)
    return {"title": canonical_book.title, "chapters": len(canonical_book.chapters)}

results = process_zip_archive("/path/to/archive.zip", my_book_analyzer)
```

### `get_canonical_book_from_stream(fb2_stream, fb2_filename, file_mtime, parser_dispatcher=None)`

Получение CanonicalBook из потока байтов FB2 (рекомендуется для новой архитектуры).

```python
canonical_book = get_canonical_book_from_stream(fb2_stream, fb2_filename, mtime)
# Соответствует архитектуре StorageManager с потоковым чтением
```

### `collect_zip_archives(paths, limit=None)`

Сбор путей к ZIP архивам в указанных директориях.

```python
archives = collect_zip_archives(["/path1", "/path2"], limit=100)
```

### Вспомогательные функции

- `format_file_path(archive_path, fb2_filename)` - стандартное форматирование путей
- `get_first_non_empty_author(canonical_book)` - получение первого непустого автора
- `get_components()` - синглтон для переиспользования проектных компонентов

## Специализированные модули

### `tools/analysis/utils.py` - Утилиты анализатора аномалий

Специализированные функции для анализатора аномалий, вынесенные из общих утилит для соблюдения принципа высокой связности:

- `get_anomaly_type_translations()` - переводы типов аномалий на русский
- `get_anomaly_type_translations_plural()` - переводы во множественном числе
- `format_book_log_entry()` - форматирование логов анализатора

```python
from tools.analysis.utils import format_book_log_entry, get_anomaly_type_translations_plural

# Специфичное использование только в анализаторе аномалий
log_entry = format_book_log_entry(archive_path, fb2_filename, canonical_book, anomalies)
```

## Рефакторинг существующих скриптов

### До рефакторинга:
```python
# Дублированный код в каждом скрипте:
with zipfile.ZipFile(archive_path, "r") as zip_ref:
    for file_info in zip_ref.infolist():
        if file_info.filename.lower().endswith(".fb2"):
            with tempfile.NamedTemporaryFile(suffix=".fb2", delete=False) as temp_file:
                temp_path = Path(temp_file.name)
            
            with zip_ref.open(file_info.filename) as fb2_file:
                content = fb2_file.read()
            
            with open(temp_path, "wb") as temp_file:
                temp_file.write(content)
            
            parser_dispatcher = ParserDispatcher()
            canonical_book = parser_dispatcher.parse_to_canonical(temp_path)
            
            # ... обработка ...
            
            temp_path.unlink()  # И обработка ошибок
```

### После рефакторинга (новая архитектура):
```python
from tools.utils import process_zip_archive, get_canonical_book_from_stream, get_components

def process_book(archive_path: str, fb2_filename: str, fb2_stream: io.BytesIO, mtime: float):
    # Используем синглтон компонентов для производительности
    parser_dispatcher = get_components().parser_dispatcher
    canonical_book, _ = get_canonical_book_from_stream(fb2_stream, fb2_filename, mtime, parser_dispatcher)
    # ... обработка ...

results = process_zip_archive(archive_path, process_book)
```

**Экономия:** ~50-80 строк дублированного кода на каждый скрипт.

## Архитектурные принципы

1. **Только проектные компоненты:** Все функции используют существующую логику проекта (ParserDispatcher, CanonicalBook, etc.)
2. **Потоковая архитектура:** Исключительно потоковое чтение через io.BytesIO, соответствие StorageManager
3. **Высокая связность:** Специализированные функции вынесены в отдельные модули (analysis/)
4. **Каноническое API:** Использование стандартных функций из app/utils.py (extract_source_id)
5. **Обработка ошибок:** Стандартизированная обработка QuarantineError и других исключений
6. **Переиспользование:** Синглтон для дорогих компонентов (ParserDispatcher)

## Примеры использования

Смотрите рефакторированные скрипты `run_anomaly_analyzer.py` и `run_statistical_reporter.py` как образцы применения утилит. 