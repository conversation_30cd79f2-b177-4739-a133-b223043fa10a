#!/usr/bin/env python3
"""
Инструмент для создания статистических CSV-отчетов по книгам.

Этот инструмент выделен из run_anomaly_analyzer.py для фокусировки на
статистическом анализе без запуска детекторов аномалий. Он парсит книги
и извлекает только статистические поля для анализа.

Особенности:
- Облегченный парсинг: только статистические поля
- Не запускает BookValidator (детекторы аномалий)
- Создает CSV отчет с метаданными книг
- Анализ структуры глав для выявления "монолитных" и "дробленых" книг
- Архитектура "Продюсер-Потребитель" для параллелизма на уровне файлов
"""

import argparse
import csv
import io
import logging
import signal
import sys
import threading
import time
from concurrent.futures import ProcessPoolExecutor, as_completed
from datetime import datetime
from pathlib import Path
from typing import Any, Optional

# Добавляем корневую директорию проекта в sys.path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Настройка логирования только для инструментов (без глобального basicConfig)
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# Создаем обработчик только для этого инструмента
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)

# Конфигурация отчета
REPORT_OUTPUT_PATH = Path(__file__).parent / "statistical_report.csv"

# Конфигурация буферизации
DEFAULT_BUFFER_SIZE = 750  # Количество записей в буфере (оптимизировано на основе тестов)
DEFAULT_FLUSH_INTERVAL = 30.0  # Интервал принудительного flush в секундах


class SimpleFormatter(logging.Formatter):
    """Простой форматтер без временных меток для обычного режима."""

    def format(self, record):
        return record.getMessage()


def setup_logging():
    """Настраивает логирование для инструмента."""
    # Устанавливаем простой форматтер для инструмента
    simple_formatter = SimpleFormatter()
    console_handler.setFormatter(simple_formatter)
    logger.addHandler(console_handler)

    # Подавляем логи из компонентов приложения
    app_loggers = [
        "app.processing.parsers.fb2.chapter_aggregator",
        "app.processing.pruner",
        "app.processing",
        "app",
    ]

    for logger_name in app_loggers:
        app_logger = logging.getLogger(logger_name)
        app_logger.setLevel(logging.WARNING)  # Показываем только WARNING и выше


# Импорты для статистического анализа
try:
    from app import settings
    from app.processing.anthology_detector import AnthologyDetector
    from app.processing.canonical_model import CanonicalBook
    from app.processing.date_extractor import extract_best_date
    from app.processing.dto import BookValidationContext
    from app.processing.parsing_report import ParsingReport
    from app.utils.helpers import extract_source_id

    from tools.utils import (
        get_canonical_book_from_stream,
        get_components,
    )

    FUNCTIONALITY_AVAILABLE = True
except ImportError as e:
    logger.error(f"❌ Необходимые компоненты недоступны: {e}")
    logger.error("🔧 Установите недостающие зависимости для работы инструмента")
    FUNCTIONALITY_AVAILABLE = False


# Глобальный экземпляр AnthologyDetector для переиспользования в процессах
_global_anthology_detector = None


def _get_anthology_triggers_string(context: "BookValidationContext") -> str:
    """Получает строку триггеров антологии используя глобальный экземпляр детектора.

    Эта функция создана для оптимизации производительности в многопроцессорной среде.
    Вместо создания нового AnthologyDetector для каждой книги, используется
    глобальный экземпляр, который создается один раз на процесс-воркер.

    Args:
        context: Контекст валидации книги

    Returns:
        Строка с триггерами антологии
    """
    global _global_anthology_detector

    # Ленивая инициализация: создаем детектор только при первом использовании
    if _global_anthology_detector is None:
        _global_anthology_detector = AnthologyDetector(min_authors_threshold=settings.QUARANTINE_ANTHOLOGY_MIN_AUTHORS)

    return _global_anthology_detector.get_anthology_triggers_string(context)


# Колонки для статистического CSV отчета
REPORT_COLUMNS = [
    "archive_path",  # Путь к архиву
    "fb2_filename",  # Имя FB2 файла
    "title",  # Название книги
    "chapters_count",  # Количество глав
    "total_chapter_characters",  # Общее количество символов текста всех глав
    "avg_chapter_size",  # Средний размер главы в символах
    "first_chapter_size",  # Размер первой главы
    "last_chapter_size",  # Размер последней главы
    "sequences_count",  # Количество серий
    "authors_count",  # Количество авторов
    "authors",  # Список авторов
    "sequences",  # Список серий
    "lang",  # Язык книги
    "date_source",  # Источник даты для book_id
    "generation_date",  # Дата генерации book_id
    "genres",  # Жанры
    "has_annotation",  # Есть ли аннотация
    "chapter_heuristic",  # Использованная эвристика разбиения на главы
    "anthology_triggers",  # Детальная информация о триггерах антологии
]


class BufferedCSVWriter:
    """Буферизованный CSV writer с автоматическим flush и безопасностью данных.

    Особенности:
    - Накопление записей в памяти до достижения buffer_size
    - Автоматический flush по таймеру (flush_interval секунд)
    - Безопасное завершение через контекстный менеджер
    - Обработка сигналов для сохранения данных при аварийном завершении
    - Метрики производительности (количество flush операций, время записи)
    """

    def __init__(
        self,
        file_path: Path,
        columns: list[str],
        buffer_size: int = DEFAULT_BUFFER_SIZE,
        flush_interval: float = DEFAULT_FLUSH_INTERVAL,
    ):
        """Инициализирует буферизованный CSV writer.

        Args:
            file_path: Путь к CSV файлу
            columns: Список колонок для заголовка
            buffer_size: Размер буфера (количество записей)
            flush_interval: Интервал принудительного flush в секундах
        """
        self.file_path = file_path
        self.columns = columns
        self.buffer_size = buffer_size
        self.flush_interval = flush_interval

        # Внутреннее состояние
        self.csv_file: Optional[io.TextIOWrapper] = None
        self.csv_writer: Optional[Any] = None
        self.buffer: list[list[str]] = []
        self.last_flush_time = time.time()
        self.lock = threading.Lock()  # Защита буфера (на случай будущих изменений)

        # Метрики производительности
        self.total_records = 0
        self.flush_count = 0
        self.total_write_time = 0.0

        # Флаг для корректного завершения
        self._closed = False

    def __enter__(self):
        """Открывает файл и записывает заголовок."""
        self.csv_file = open(self.file_path, "w", encoding="utf-8-sig", newline="")
        self.csv_writer = csv.writer(self.csv_file, delimiter=";")
        self.csv_writer.writerow(self.columns)
        self.csv_file.flush()  # Заголовок записываем сразу

        # Устанавливаем обработчики сигналов для безопасного завершения
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Финальный flush и закрытие файла."""
        _ = exc_type, exc_val, exc_tb  # Параметры не используются
        self._flush_buffer(force=True)
        if self.csv_file:
            self.csv_file.close()
        self._closed = True

    def _signal_handler(self, signum, frame):
        """Обработчик сигналов для безопасного сохранения данных."""
        _ = frame  # Параметр не используется
        logger.info(f"🛑 Получен сигнал {signum}, сохраняем буфер...")
        self._flush_buffer(force=True)

    def write_row(self, row_data: list[str]) -> None:
        """Добавляет строку в буфер с автоматическим flush при необходимости.

        Args:
            row_data: Данные строки для записи
        """
        if self._closed:
            return

        with self.lock:
            self.buffer.append(row_data)
            self.total_records += 1

            # Проверяем условия для flush
            current_time = time.time()
            should_flush = (
                len(self.buffer) >= self.buffer_size or (current_time - self.last_flush_time) >= self.flush_interval
            )

            if should_flush:
                self._flush_buffer()

    def _flush_buffer(self, force: bool = False) -> None:
        """Записывает буфер на диск.

        Args:
            force: Принудительная запись даже пустого буфера
        """
        if not self.buffer and not force:
            return

        if not self.csv_writer:
            return

        start_time = time.time()

        try:
            # Записываем все строки из буфера
            for row in self.buffer:
                self.csv_writer.writerow(row)

            # Принудительно сбрасываем на диск
            self.csv_file.flush()

            # Обновляем метрики
            write_time = time.time() - start_time
            self.total_write_time += write_time
            self.flush_count += 1
            self.last_flush_time = time.time()

            # Очищаем буфер
            buffer_size = len(self.buffer)
            self.buffer.clear()

            # Логируем только значительные flush операции
            if buffer_size > 100 or write_time > 0.1:
                logger.debug(f"📝 Flush: {buffer_size} записей за {write_time:.3f}с")

        except Exception as e:
            logger.error(f"❌ Ошибка записи буфера: {e}")
            raise

    def get_performance_stats(self) -> dict[str, Any]:
        """Возвращает метрики производительности."""
        avg_write_time = self.total_write_time / max(self.flush_count, 1)
        avg_buffer_size = self.total_records / max(self.flush_count, 1)

        return {
            "total_records": self.total_records,
            "flush_count": self.flush_count,
            "total_write_time": self.total_write_time,
            "avg_write_time_per_flush": avg_write_time,
            "avg_buffer_size": avg_buffer_size,
        }


def extract_statistical_data(
    canonical_book: CanonicalBook,
    parsing_report: ParsingReport,
    archive_path: str,
    fb2_filename: str,
) -> dict[str, Any]:
    """Извлекает статистические данные из книги для отчета.

    Включает базовые метаданные книги, а также метрики анализа структуры глав
    для выявления аномалий типа "монолитных" и "дробленых" книг.
    Также собирает детальную информацию о триггерах антологии.

    Args:
        canonical_book: Каноническая модель книги
        parsing_report: Отчет о парсинге с диагностической информацией
        archive_path: Путь к архиву
        fb2_filename: Имя FB2 файла

    Returns:
        Словарь со статистическими данными, включая метрики структуры глав:
        - chapters_count: количество глав
        - total_chapter_characters: общее количество символов текста всех глав
        - avg_chapter_size: средний размер главы в символах
        - first_chapter_size: размер первой главы
        - last_chapter_size: размер последней главы
        - anthology_triggers: детальная информация о триггерах антологии
    """
    # Формируем строку авторов
    authors_list = []
    for author in canonical_book.authors:
        name_parts = [author.first_name, author.middle_name, author.last_name]
        full_name = " ".join(filter(None, name_parts))
        if full_name:
            authors_list.append(full_name)
    authors_str = "; ".join(authors_list) if authors_list else "Unknown"

    # Формируем строку серий
    sequences_list = []
    for seq in canonical_book.sequences:
        seq_str = seq.name
        if seq.number is not None:
            seq_str += f" #{seq.number}"
        sequences_list.append(seq_str)
    sequences_str = "; ".join(sequences_list) if sequences_list else ""

    # Извлекаем дату генерации и источник даты
    date_source = "unknown"
    generation_date = "unknown"

    if canonical_book.raw_source_model is not None:
        try:
            best_date, date_source = extract_best_date(canonical_book.raw_source_model, None)
            generation_date = best_date.strftime("%Y-%m-%d")
        except Exception:
            pass

    # Извлекаем информацию об использованной эвристике из диагностики
    # Используем детализированную информацию если доступна, иначе fallback на базовую
    chapter_heuristic = "unknown"
    if parsing_report:
        detailed_heuristic = parsing_report.diagnostics.get("detailed_chapter_heuristic")
        if detailed_heuristic and detailed_heuristic != "unknown":
            chapter_heuristic = detailed_heuristic
        else:
            # Fallback на базовую информацию
            chapter_heuristic = parsing_report.diagnostics.get("chapter_heuristic", "unknown")

    # Вычисляем метрики анализа структуры глав (используем быструю оценку размера)
    chapters_count = len(canonical_book.chapters)
    if chapters_count > 0:
        chapter_sizes = [ch.estimated_content_length for ch in canonical_book.chapters]
        total_chapter_characters = sum(chapter_sizes)
        avg_chapter_size = total_chapter_characters // chapters_count if chapters_count > 0 else 0
        first_chapter_size = chapter_sizes[0]
        last_chapter_size = chapter_sizes[-1]
    else:
        total_chapter_characters = 0
        avg_chapter_size = 0
        first_chapter_size = 0
        last_chapter_size = 0

    # Получаем детальную информацию о триггерах антологии
    anthology_triggers_str = ""
    try:
        # Создаем контекст валидации для анализа
        context = BookValidationContext(
            book=canonical_book,
            chapter_count=chapters_count,
            author_count=len(canonical_book.authors),
            avg_chapter_length=avg_chapter_size,
            total_content_length=total_chapter_characters,
            has_chapters=chapters_count > 0,
            last_chapter_content="",  # Не используется для антологий
        )

        # Получаем строку комбинированных правил антологии (используем глобальный экземпляр)
        anthology_triggers_str = _get_anthology_triggers_string(context)

        # DEBUG: Выводим отладочную информацию для диагностики
        if anthology_triggers_str:
            logger.debug(f"Антология {fb2_filename}: '{anthology_triggers_str}'")
    except Exception as e:
        # В случае ошибки оставляем поле пустым и логируем ошибку
        logger.error(f"Ошибка при обработке триггеров антологии для {fb2_filename}: {e}")
        anthology_triggers_str = ""

    # Извлекаем статистику XML исправлений из диагностических данных
    xml_fixes_stats = parsing_report.diagnostics.get("xml_fixes_stats", {})

    return {
        "archive_path": archive_path,
        "fb2_filename": fb2_filename,
        "title": canonical_book.title or "No Title",
        "chapters_count": chapters_count,
        "total_chapter_characters": total_chapter_characters,
        "avg_chapter_size": avg_chapter_size,
        "first_chapter_size": first_chapter_size,
        "last_chapter_size": last_chapter_size,
        "sequences_count": len(canonical_book.sequences),
        "authors_count": len(canonical_book.authors),
        "authors": authors_str,
        "sequences": sequences_str,
        "lang": canonical_book.lang or "unknown",
        "date_source": date_source,
        "generation_date": generation_date,
        "genres": "; ".join(canonical_book.genres) if canonical_book.genres else "",
        "has_annotation": "Yes" if canonical_book.annotation_md.strip() else "No",
        "chapter_heuristic": chapter_heuristic,
        "anthology_triggers": anthology_triggers_str,
        "xml_fixes_stats": xml_fixes_stats,  # Добавляем статистику исправлений
    }


def process_single_file_worker(archive_path: str, fb2_filename: str) -> dict[str, Any] | None:
    """
    Воркер для обработки ОДНОГО файла. Выполняется в отдельном процессе.
    Не имеет побочных эффектов, только возвращает результат.

    Args:
        archive_path: Путь к архиву
        fb2_filename: Имя FB2 файла в архиве

    Returns:
        Словарь с данными книги или None при ошибке
    """
    try:
        # 1. Инициализируем компоненты ВНУТРИ процесса
        components = get_components()
        parser_dispatcher = components.parser_dispatcher
        storage_manager = components.storage_manager

        # 2. Читаем один файл из архива (оптимизированное чтение без загрузки всего архива)
        fb2_stream = storage_manager.read_file_from_archive(archive_path, fb2_filename)

        # 3. Получаем каноническую модель (file_mtime здесь не критичен для статистики)
        canonical_book, parsing_report = get_canonical_book_from_stream(
            fb2_stream, fb2_filename, None, parser_dispatcher
        )

        # 4. Извлекаем статистику
        book_data = extract_statistical_data(canonical_book, parsing_report, archive_path, fb2_filename)

        # 5. Возвращаем результат. Никакой записи в CSV здесь!
        return book_data

    except Exception:
        # В случае ошибки просто возвращаем None
        # Основной процесс обработает это централизованно
        return None


class StatisticalReporter:
    """Генератор статистических отчетов по книгам."""

    def __init__(
        self,
        max_workers: int = 12,
        buffer_size: int = DEFAULT_BUFFER_SIZE,
        flush_interval: float = DEFAULT_FLUSH_INTERVAL,
        debug_mode: bool = False,
    ):
        """Инициализирует репортер.

        Args:
            max_workers: Количество процессов для обработки
            buffer_size: Размер буфера для CSV записи
            flush_interval: Интервал принудительного flush в секундах
            debug_mode: Показывать детальные логи и метрики буферизации CSV
        """
        if not FUNCTIONALITY_AVAILABLE:
            raise RuntimeError("Необходимые компоненты недоступны")

        self.output_path = REPORT_OUTPUT_PATH
        self.max_workers = max_workers
        self.buffer_size = buffer_size
        self.flush_interval = flush_interval
        self.debug_mode = debug_mode

        # Настройка логирования ПЕРЕД созданием компонентов
        self._configure_logging()

        # Компоненты через синглтон (устраняет дублирование "тяжелых" объектов)
        components = get_components()
        if not components.has_full_functionality():
            raise RuntimeError("Синглтон компонентов не имеет полной функциональности")

        self.parser_dispatcher = components.parser_dispatcher
        self.storage_manager = components.storage_manager

        # Статистика
        self.stats: dict[str, Any] = {
            "processed_files": 0,
            "processed_archives": 0,
            "errors": 0,
            "start_time": datetime.now(),
        }

        # Список ошибок для вывода в конце
        self.error_list: list[str] = []

        # Статистика XML исправлений
        self.xml_fixes_stats: dict[str, Any] = {
            "files_with_paragraph_fixes": [],
            "files_with_structural_fixes": [],
            "total_paragraph_fixes": 0,
            "total_structural_fixes": 0,
            "total_attribute_fixes": 0,
        }

        # Буферизованный CSV writer
        self.csv_writer: Optional[BufferedCSVWriter] = None

    def _configure_logging(self) -> None:
        """Настраивает логирование для минимального вывода."""
        # Устанавливаем простой форматтер для основного логгера
        handler = logging.StreamHandler()
        handler.setFormatter(SimpleFormatter())
        logger.handlers.clear()
        logger.addHandler(handler)
        logger.propagate = False

        # Подавляем детальные логи парсинга и ошибки
        logging.getLogger("app.processing.parser_dispatcher").setLevel(logging.CRITICAL)
        logging.getLogger("app.processing.parsers.fb2.fb2_parser").setLevel(logging.CRITICAL)
        logging.getLogger("app.processing.parsers.fb2.fb2_transformer").setLevel(logging.CRITICAL)
        logging.getLogger("app.processing.date_extractor").setLevel(logging.CRITICAL)

    def __enter__(self):
        """Контекстный менеджер - инициализирует буферизованный CSV writer."""
        self.csv_writer = BufferedCSVWriter(self.output_path, REPORT_COLUMNS, self.buffer_size, self.flush_interval)
        self.csv_writer.__enter__()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Контекстный менеджер - закрывает буферизованный CSV writer."""
        if self.csv_writer:
            self.csv_writer.__exit__(exc_type, exc_val, exc_tb)

    def _collect_archives(self, paths: list[str]) -> list[Path]:
        """Собирает архивы для обработки из указанных путей.

        Args:
            paths: Список путей к архивам или директориям

        Returns:
            Список путей к ZIP архивам
        """
        archives_to_process = []
        for path_str in paths:
            path = Path(path_str)
            if path.is_file() and path.suffix.lower() == ".zip":
                archives_to_process.append(path)
            elif path.is_dir():
                zip_files = list(path.glob("*.zip"))
                archives_to_process.extend(zip_files)
        return archives_to_process

    def process_paths(self, paths: list[str]) -> None:
        """
        Обрабатывает пути: генерирует задачи (файлы) и агрегирует результаты.
        Архитектура "Продюсер-Потребитель" с параллелизмом на уровне файлов.
        """
        archives_to_process = self._collect_archives(paths)
        if not archives_to_process:
            logger.error("❌ Не найдено ZIP архивов для обработки")
            return

        # --- ЭТАП 1: ПРОДЮСЕР (Генерация задач) ---
        tasks = []
        # ОПТИМИЗАЦИЯ: Используем set для O(1) проверки расширений
        fb2_extensions = {".fb2", ".fb2.zip"}
        print("🔍 Сканирование архивов и генерация задач...")
        for archive_path in archives_to_process:
            try:
                # Получаем список файлов без полного чтения архива
                book_files = self.storage_manager.list_books_in_archive(str(archive_path))
                for book_filename in book_files:
                    from pathlib import Path

                    if Path(book_filename).suffix.lower() in fb2_extensions:  # Фильтр на всякий случай
                        tasks.append((str(archive_path), book_filename))
            except Exception as e:
                self.stats["errors"] += 1
                self.error_list.append(f"Ошибка сканирования архива {archive_path.name}: {e}")

        if not tasks:
            logger.error("❌ Не найдено подходящих файлов в архивах.")
            return

        total_files = len(tasks)
        logger.info(f"📦 Найдено {len(archives_to_process)} архивов, всего {total_files} задач для обработки.")
        print()

        # --- ЭТАП 2: ПОТРЕБИТЕЛИ И АГРЕГАТОР (Обработка и запись) ---
        # Импортируем tqdm для прогресс-бара
        has_real_tqdm = False
        try:
            from tqdm import tqdm

            has_real_tqdm = True
        except ImportError:
            logger.warning("⚠️ tqdm не установлен, прогресс-бар будет недоступен")

            # Простая заглушка для tqdm
            class SimpleTqdm:
                def __init__(self, iterable, total, desc):
                    self.iterable = iterable
                    self.total = total
                    self.desc = desc
                    self.count = 0

                def __iter__(self):
                    return self

                def __next__(self):
                    if self.count < len(list(self.iterable)):
                        result = next(iter(list(self.iterable)[self.count :]))
                        self.count += 1
                        if self.count % 100 == 0:  # Логируем каждые 100 файлов
                            print(f"{self.desc}: {self.count}/{self.total}")
                        return result
                    else:
                        raise StopIteration

            tqdm = SimpleTqdm

        with ProcessPoolExecutor(max_workers=self.max_workers) as executor:
            # Создаем итератор future-объектов
            futures = {executor.submit(process_single_file_worker, *task): task for task in tasks}

            # Используем tqdm для отслеживания прогресса
            for future in tqdm(as_completed(futures), total=total_files, desc="📚 Обработка книг"):
                task = futures[future]
                archive_path_str, fb2_filename = task

                try:
                    # Получаем результат от дочернего процесса
                    book_data = future.result()

                    if book_data is not None:
                        # Записываем данные книги в буферизованный CSV writer
                        if self.csv_writer:
                            row = [book_data.get(col, "") for col in REPORT_COLUMNS]
                            self.csv_writer.write_row(row)

                        # Обновляем статистику успешной обработки
                        self.stats["processed_files"] += 1

                        # Собираем статистику XML исправлений
                        xml_fixes_stats = book_data.get("xml_fixes_stats", {})
                        if xml_fixes_stats and xml_fixes_stats.get("total_fixes_applied", 0) > 0:
                            # Формируем ID файла для статистики
                            source_id = extract_source_id(Path(fb2_filename))
                            archive_name = Path(archive_path_str).name
                            if source_id:
                                file_id = f"{archive_name}/{source_id}.fb2"
                            else:
                                book_name = Path(fb2_filename).stem
                                file_id = f"{archive_name}::{book_name}.fb2"

                            # Собираем статистику по типам исправлений
                            if xml_fixes_stats.get("paragraph_fixes", 0) > 0:
                                self.xml_fixes_stats["files_with_paragraph_fixes"].append(file_id)
                                self.xml_fixes_stats["total_paragraph_fixes"] += xml_fixes_stats["paragraph_fixes"]

                            if xml_fixes_stats.get("structural_fixes", 0) > 0:
                                self.xml_fixes_stats["files_with_structural_fixes"].append(file_id)
                                self.xml_fixes_stats["total_structural_fixes"] += xml_fixes_stats["structural_fixes"]

                            if xml_fixes_stats.get("attribute_fixes", 0) > 0:
                                self.xml_fixes_stats["total_attribute_fixes"] += xml_fixes_stats["attribute_fixes"]

                        # Логируем успешную обработку (простой формат для пользователя)
                        source_id = extract_source_id(Path(fb2_filename))
                        archive_name = Path(archive_path_str).name
                        if source_id:
                            book_id = f"{archive_name}/{source_id}"
                        else:
                            book_name = Path(fb2_filename).stem
                            book_id = f"{archive_name}/{book_name}"

                        book_title = book_data.get("title", "Без названия")
                        authors = book_data.get("authors", "Unknown")
                        first_author = authors.split(";")[0] if authors != "Unknown" else "Unknown"

                        # Получаем информацию об антологиях для отображения
                        anthology_info = ""
                        anthology_triggers = book_data.get("anthology_triggers", "")
                        if anthology_triggers:
                            # Новый формат: комбинированные правила
                            # Примеры: "strong_title_keywords=сборник", "multiple_authors=4+keyword_in_title=рассказ"
                            anthology_info = f" - ⚠️  Антология: {anthology_triggers}"

                        # Используем tqdm.write() для корректного вывода при работающем прогресс-баре
                        if has_real_tqdm:
                            tqdm.write(f"{book_id} - {first_author}. {book_title}{anthology_info}")
                        else:
                            print(f"{book_id} - {first_author}. {book_title}{anthology_info}")
                    else:
                        # Обрабатываем ошибку
                        self.stats["errors"] += 1

                        # Формируем ID для ошибки
                        source_id = extract_source_id(Path(fb2_filename))
                        archive_name = Path(archive_path_str).name
                        if source_id:
                            book_id = f"{archive_name}/{source_id}"
                        else:
                            book_name = Path(fb2_filename).stem
                            book_id = f"{archive_name}::{book_name}"

                        self.error_list.append(f"{book_id}.fb2: Ошибка обработки файла")

                except Exception as e:
                    # Критическая ошибка процесса
                    self.stats["errors"] += 1

                    # Формируем ID для ошибки
                    source_id = extract_source_id(Path(fb2_filename))
                    archive_name = Path(archive_path_str).name
                    if source_id:
                        book_id = f"{archive_name}/{source_id}"
                    else:
                        book_name = Path(fb2_filename).stem
                        book_id = f"{archive_name}/{book_name}"

                    self.error_list.append(f"{book_id}: Критическая ошибка процесса - {str(e)}")

        # Обновляем количество обработанных архивов
        self.stats["processed_archives"] = len(archives_to_process)

    def print_stats(self) -> None:
        """Выводит статистику обработки."""
        end_time = datetime.now()
        start_time = self.stats["start_time"]
        if isinstance(start_time, datetime):
            total_time = (end_time - start_time).total_seconds()
        else:
            total_time = 0.0

        # Выводим статистику XML исправлений ПЕРЕД ошибками
        self._print_xml_fixes_stats()

        # Выводим список ошибок если они есть
        if self.error_list:
            logger.info(f"❌ Ошибки обработки: {len(self.error_list)}")
            for error in self.error_list:
                logger.info(f"  • {error}")
            print()

        # Статистика в самом конце
        logger.info("📊 Статистика обработки:")
        logger.info(f"  📦 Архивов обработано: {self.stats['processed_archives']}")
        logger.info(f"  📚 Книг обработано: {self.stats['processed_files']}")

        # Добавляем статистику исправленных файлов
        total_paragraph_files = len(self.xml_fixes_stats["files_with_paragraph_fixes"])
        total_structural_files = len(self.xml_fixes_stats["files_with_structural_fixes"])
        if total_paragraph_files > 0 or total_structural_files > 0:
            logger.info(f"  🔧 Исправлено файлов: {total_paragraph_files} / {total_structural_files}")

        logger.info(f"  ⏱️  Время: {total_time:.1f} сек")

        if total_time > 0:
            processed_files = self.stats["processed_files"]
            if isinstance(processed_files, int):
                speed = processed_files / total_time
                logger.info(f"  🚀 Скорость: {speed:.2f} книг/сек")

        # Метрики производительности буферизации (только в debug режиме)
        if self.csv_writer and self.debug_mode:
            perf_stats = self.csv_writer.get_performance_stats()
            logger.info("📝 Метрики записи CSV:")
            logger.info(f"  💾 Операций flush: {perf_stats['flush_count']}")
            logger.info(f"  ⏱️  Время записи: {perf_stats['total_write_time']:.3f} сек")
            logger.info(f"  📊 Средний размер буфера: {perf_stats['avg_buffer_size']:.1f} записей")

            if perf_stats["flush_count"] > 0:
                efficiency = (perf_stats["total_records"] / perf_stats["flush_count"]) / self.buffer_size * 100
                logger.info(f"  ⚡ Эффективность буферизации: {efficiency:.1f}%")

    def _print_xml_fixes_stats(self) -> None:
        """Выводит статистику XML исправлений."""
        total_paragraph_files = len(self.xml_fixes_stats["files_with_paragraph_fixes"])
        total_structural_files = len(self.xml_fixes_stats["files_with_structural_fixes"])

        if total_paragraph_files > 0 or total_structural_files > 0:
            logger.info("🔧 Статистика XML исправлений:")

            if total_paragraph_files > 0:
                logger.info(f"  📝 Файлов с исправлениями параграфов: {total_paragraph_files}")
                logger.info(f"      Всего исправлений параграфов: {self.xml_fixes_stats['total_paragraph_fixes']}")

                # Показываем ВСЕ файлы
                for file_id in self.xml_fixes_stats["files_with_paragraph_fixes"]:
                    logger.info(f"        • {file_id}")

            if total_structural_files > 0:
                logger.info(f"  🏗️  Файлов с структурными исправлениями: {total_structural_files}")
                logger.info(f"      Всего структурных исправлений: {self.xml_fixes_stats['total_structural_fixes']}")

                # Показываем ВСЕ файлы
                for file_id in self.xml_fixes_stats["files_with_structural_fixes"]:
                    logger.info(f"        • {file_id}")

            if self.xml_fixes_stats["total_attribute_fixes"] > 0:
                logger.info(f"  🔗 Всего исправлений атрибутов: {self.xml_fixes_stats['total_attribute_fixes']}")

            print()  # Пустая строка после статистики исправлений


def main() -> int:
    """Главная функция инструмента."""
    # Настраиваем логирование в самом начале
    setup_logging()

    parser = argparse.ArgumentParser(
        description="Инструмент для создания статистических CSV-отчетов по книгам",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Примеры использования:
  %(prog)s --path /data/books
  %(prog)s --path /archive1.zip /archive2.zip
  %(prog)s --path /books --workers 20
  %(prog)s --path /books --buffer-size 1000 --flush-interval 60
  %(prog)s --path /books --debug  # Показать метрики буферизации
        """,
    )

    parser.add_argument(
        "--path",
        nargs="+",
        required=True,
        help="Пути к архивам или директориям для анализа",
    )
    parser.add_argument(
        "--workers",
        type=int,
        default=20,
        help="Количество процессов для параллельной обработки (по умолчанию: 20)",
    )
    parser.add_argument(
        "--buffer-size",
        type=int,
        default=DEFAULT_BUFFER_SIZE,
        help=f"Размер буфера для CSV записи (по умолчанию: {DEFAULT_BUFFER_SIZE})",
    )
    parser.add_argument(
        "--flush-interval",
        type=float,
        default=DEFAULT_FLUSH_INTERVAL,
        help=f"Интервал принудительного flush в секундах (по умолчанию: {DEFAULT_FLUSH_INTERVAL})",
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Показывать детальные логи и метрики буферизации CSV",
    )

    args = parser.parse_args()

    # Проверка доступности функциональности
    if not FUNCTIONALITY_AVAILABLE:
        logger.error("❌ Необходимые компоненты недоступны")
        return 1

    # СНАЧАЛА проверяем наличие архивов ДО создания отчета
    temp_reporter = StatisticalReporter(args.workers, args.buffer_size, args.flush_interval, args.debug)
    archives_to_process = temp_reporter._collect_archives(args.path)

    if not archives_to_process:
        logger.error("❌ Не найдено ZIP архивов для обработки")
        return 1

    # Создаем директорию для отчета если не существует
    REPORT_OUTPUT_PATH.parent.mkdir(parents=True, exist_ok=True)

    # Начальные логи
    logger.info(f"📁 Обработка {len(args.path)} путей с {args.workers} процессами")
    if args.buffer_size != DEFAULT_BUFFER_SIZE or args.flush_interval != DEFAULT_FLUSH_INTERVAL:
        logger.info(f"⚙️  Буферизация: {args.buffer_size} записей, flush каждые {args.flush_interval}с")

    try:
        with StatisticalReporter(args.workers, args.buffer_size, args.flush_interval, args.debug) as reporter:
            reporter.process_paths(args.path)
            print()  # Пустая строка перед статистикой
            reporter.print_stats()

        print()  # Пустая строка в конце
        logger.info(f"✅ Отчет создан: {REPORT_OUTPUT_PATH}")

        return 0

    except KeyboardInterrupt:
        logger.info("🛑 Прервано пользователем")
        return 1
    except Exception as e:
        logger.error(f"❌ Критическая ошибка: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
