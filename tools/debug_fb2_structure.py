#!/usr/bin/env python3
"""
Инструмент для анализа структуры FB2 файла.
Показывает иерархию секций и элементов для понимания, почему не работает разбиение на главы.
"""

import sys
from pathlib import Path

# Добавляем корневую директорию проекта в PYTHONPATH
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.processing.parsers.fb2.fb2_parser import FB2Parser  # noqa: E402
from app.storage import LocalStorageManager  # noqa: E402


def analyze_fb2_structure(file_path: str, max_depth: int = 3):
    """Анализирует структуру FB2 файла."""
    print(f"🔍 Анализ структуры: {file_path}")

    # Парсим файл
    storage_manager = LocalStorageManager()
    parser = FB2Parser()

    if "::" in file_path:
        archive_path, book_filename = file_path.split("::", 1)
        file_stream = storage_manager.read_file_from_archive(archive_path, book_filename)
        file_content = file_stream.read()
    else:
        with open(file_path, "rb") as f:
            file_content = f.read()

    # Создаем временный файл для парсера
    import tempfile

    with tempfile.NamedTemporaryFile(mode="wb", suffix=".fb2", delete=False) as f:
        f.write(file_content)
        temp_file = f.name

    try:
        fb2_book = parser.parse(Path(temp_file))
    finally:
        Path(temp_file).unlink()

    print(f"\n📚 Книга: {fb2_book.description.title_info.book_title}")
    print(f"📖 Количество bodies: {len(fb2_book.bodies)}")

    for i, body in enumerate(fb2_book.bodies):
        print(f"\n📄 Body {i + 1}:")
        print(f"   Название: {body.name}")
        print(f"   Секций: {len(body.sections)}")

        for j, section in enumerate(body.sections):
            analyze_section(section, depth=0, max_depth=max_depth, prefix=f"   Секция {j + 1}")


def analyze_section(section, depth=0, max_depth=3, prefix=""):
    """Рекурсивно анализирует секцию."""
    if depth > max_depth:
        return

    indent = "  " * depth
    title = "БЕЗ ЗАГОЛОВКА"

    if section.title and section.title.elements:
        # Извлекаем заголовок
        title_parts = []
        for element in section.title.elements:
            if hasattr(element, "content"):
                for content in element.content:
                    if isinstance(content, str):
                        title_parts.append(content.strip())
            elif isinstance(element, str):
                title_parts.append(element.strip())
        title = " ".join(title_parts) or "ПУСТОЙ ЗАГОЛОВОК"

    print(f"{indent}{prefix}: '{title}'")
    print(f"{indent}  📊 Элементов контента: {len(section.content)}")

    # Анализируем содержимое
    nested_sections = 0
    paragraphs = 0
    subtitles = 0
    empty_lines = 0
    other = 0

    for item in section.content:
        if hasattr(item, "__class__"):
            class_name = item.__class__.__name__
            if class_name == "Section":
                nested_sections += 1
            elif class_name == "Paragraph":
                paragraphs += 1
            elif class_name == "Subtitle":
                subtitles += 1
            elif class_name == "EmptyLine":
                empty_lines += 1
            else:
                other += 1

    print(f"{indent}  📋 Вложенных секций: {nested_sections}")
    print(f"{indent}  📝 Параграфов: {paragraphs}")
    print(f"{indent}  🏷️  Подзаголовков: {subtitles}")
    print(f"{indent}  ⬜ Пустых строк: {empty_lines}")
    print(f"{indent}  ❓ Других элементов: {other}")

    # Показываем первые несколько параграфов для понимания структуры
    if paragraphs > 0 and depth < 2:
        print(f"{indent}  📖 Первые 10 параграфов:")
        count = 0
        for item in section.content:
            if hasattr(item, "__class__") and item.__class__.__name__ == "Paragraph":
                if count >= 10:  # Показываем первые 10
                    break
                text_parts = []
                for content in item.content:
                    if isinstance(content, str):
                        text_parts.append(content.strip())
                text = " ".join(text_parts)[:150]  # Первые 150 символов
                print(f"{indent}    {count + 1}. {text}...")
                count += 1

    # Рекурсивно обрабатываем вложенные секции
    if nested_sections > 0 and depth < max_depth:
        section_count = 0
        for item in section.content:
            if hasattr(item, "__class__") and item.__class__.__name__ == "Section":
                section_count += 1
                analyze_section(item, depth + 1, max_depth, f"Подсекция {section_count}")


if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Использование: python debug_fb2_structure.py <путь_к_файлу>")
        print("Пример: python debug_fb2_structure.py /path/to/archive.zip::book.fb2")
        sys.exit(1)

    analyze_fb2_structure(sys.argv[1])
