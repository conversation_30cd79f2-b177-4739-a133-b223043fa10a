# run_statistical_reporter.py - Генератор статистических отчетов

Специализированный инструмент для создания CSV-отчетов с метаданными книг без запуска детекторов аномалий.

## 🎯 Назначение

Этот инструмент выделен из `run_anomaly_analyzer.py` для фокусировки на статистическом анализе. Он парсит книги и извлекает только статистические поля для анализа, не запуская детекторы аномалий.

## ✨ Особенности

- **Облегченный парсинг:** Извлекает только статистические поля без анализа аномалий
- **Не запускает BookValidator:** Детекторы аномалий отключены для максимальной производительности
- **Архитектура "Продюсер-Потребитель":** Параллельная обработка на уровне отдельных файлов
- **Многопроцессная обработка:** Параллельная обработка файлов с настраиваемым количеством процессов
- **Фиксированный путь отчета:** Отчет по-умолчанию создается в `tools/statistical_report.csv`
- **Оптимизированное логирование:** Минимальные логи с одной строкой на книгу
- **Контекстный менеджер:** Безопасное управление файловыми ресурсами
- **Прогресс-бар:** Визуальное отслеживание прогресса обработки (при наличии tqdm)

## 📊 Структура CSV отчета

Генерируемый CSV содержит следующие колонки:

| Колонка | Описание |
|---------|----------|
| `archive_path` | Путь к архиву |
| `fb2_filename` | Имя FB2 файла |
| `title` | Название книги |
| `chapters_count` | Количество глав |
| `sequences_count` | Количество серий |
| `authors_count` | Количество авторов |
| `authors` | Список авторов (разделенный `;`) |
| `sequences` | Список серий (разделенный `;`) |
| `lang` | Язык книги |
| `date_source` | Источник даты для book_id |
| `generation_date` | Дата генерации book_id |
| `genres` | Жанры (разделенные `;`) |
| `has_annotation` | Есть ли аннотация (Yes/No) |
| `chapter_heuristic` | Детализированная информация о стратегии и маркерах (формат: "СТРАТЕГИЯ=МАРКЕР1,МАРКЕР2") |

## 🚀 Использование

### Базовые команды

```bash
# Создание отчета по директории
python tools/run_statistical_reporter.py --path /data/books

# Анализ конкретных архивов
python tools/run_statistical_reporter.py --path /archive1.zip /archive2.zip

# Настройка количества процессов
python tools/run_statistical_reporter.py --path /books --workers 20
```

### Параметры командной строки

| Параметр | Обязательный | Описание |
|----------|--------------|----------|
| `--path` | ✅ | Пути к архивам или директориям для анализа |
| `--workers` | ❌ | Количество процессов (по умолчанию: 20) |

### Выходной файл

Отчет всегда создается в файле `tools/statistical_report.csv` в директории инструментов.

### Формат логирования

- Минимальные логи без временных меток
- Одна строка на книгу: `архив/source_id - Автор. Название`
- Подавлены детальные логи парсинга
- Source ID извлекается из имени файла книги

## 🏗️ Архитектура "Продюсер-Потребитель"

### Этапы обработки

1. **ПРОДЮСЕР (Генерация задач):**
   - Сканирование архивов через `StorageManager`
   - Создание списка задач (архив, файл) для каждого FB2 файла
   - Подсчет общего количества файлов для обработки

2. **ПОТРЕБИТЕЛИ (Обработка файлов):**
   - Параллельная обработка отдельных файлов в разных процессах
   - Каждый процесс обрабатывает один файл за раз
   - Оптимизированное чтение одного файла из архива

3. **АГРЕГАТОР (Сбор результатов):**
   - Запись результатов в CSV в основном процессе
   - Централизованная обработка ошибок
   - Сбор статистики и прогресса

### Основные компоненты

- **`StatisticalReporter`** - Основной класс координатор
- **`process_single_file_worker()`** - Функция-воркер для обработки одного файла
- **`extract_statistical_data()`** - Функция извлечения метаданных из книги
- **Контекстный менеджер** - Безопасное управление CSV файлом
- **ProcessPoolExecutor** - Многопроцессная обработка файлов

### Зависимости

Инструмент использует следующие компоненты проекта:
- `ParserDispatcher` - Парсинг книг
- `StorageManager` - Чтение архивов и отдельных файлов
- `extract_best_date` - Извлечение дат
- `get_canonical_book_from_stream` - Преобразование в каноническую модель

## 📈 Производительность

- **Многопроцессность:** Настраиваемое количество процессов (по умолчанию 20)
- **Гранулярный параллелизм:** Обработка на уровне отдельных файлов
- **Оптимизация I/O:** Каждый файл читается отдельно без загрузки всего архива
- **Равномерная нагрузка:** Исключение блокировки процессов на больших архивах
- **Статистика:** Отслеживание прогресса и ошибок с прогресс-баром

## 🔧 Примеры использования

### Анализ большой коллекции

```bash
# Обработка всех архивов в директории с максимальной производительностью
python tools/run_statistical_reporter.py \
    --path /data/library \
    --workers 20
```

### Анализ конкретных файлов

```bash
# Анализ только определенных архивов
python tools/run_statistical_reporter.py \
    --path /data/archive1.zip /data/archive2.zip
```

## ⚠️ Ограничения

- **Требует полных зависимостей:** В отличие от `run_anomaly_analyzer.py`, не работает в ограниченном режиме
- **Только статистика:** Не выполняет анализ аномалий
- **ZIP архивы:** Поддерживает только ZIP архивы с FB2 файлами
- **Опциональность tqdm:** Прогресс-бар доступен только при установленном пакете tqdm

## 🔗 Связанные инструменты

- **`run_anomaly_analyzer.py`** - Основной инструмент для анализа аномалий
- **`analyze_quarantine.py`** - Анализ карантинных книг

## 📊 Пример вывода

```
📁 Обработка 2 путей с 20 процессами
🔍 Сканирование архивов и генерация задач...
📦 Найдено 3 архивов, всего 1543 задач для обработки.

📚 Обработка книг: 100%|██████████| 1543/1543 [00:45<00:00, 34.2книг/s]

archive1.zip/12345 - Иванов И.И. Название книги
archive1.zip/67890 - Петров П.П. Другая книга  
archive2.zip/11111 - Сидоров С.С. Третья книга

❌ Ошибки обработки: 23
  • pack.zip/1173972: Ошибка обработки файла
  • bad.zip/99999: Критическая ошибка процесса

📊 Статистика обработки:
  📦 Архивов обработано: 3
  📚 Книг обработано: 1520
  ⏱️ Время: 45.2 сек
  🚀 Скорость: 33.63 книг/сек

✅ Отчет создан: tools/statistical_report.csv
```

## � Дополнительные параметры

### Буферизация CSV записи

Инструмент использует буферизованную запись CSV для оптимизации производительности:

| Параметр | По умолчанию | Описание |
|----------|--------------|----------|
| `--buffer-size` | 750 | Количество записей в буфере перед записью на диск |
| `--flush-interval` | 30.0 | Интервал принудительного сохранения (сек) |
| `--debug` | false | Показывать детальные метрики буферизации |

```bash
# Показать метрики буферизации
python tools/run_statistical_reporter.py --path /data/books --debug

# Настройка буферизации для критичных данных
python tools/run_statistical_reporter.py --path /data/books --buffer-size 100 --flush-interval 10
```

**Безопасность данных:** автоматическое сохранение при заполнении буфера, по таймеру и при завершении программы (включая Ctrl+C).
