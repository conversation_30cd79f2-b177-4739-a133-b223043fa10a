#!/usr/bin/env python3
"""
Профессиональный инструмент для тюнинга детекторов аномалий.

Этот инструмент предназначен для разработчика/QA и обеспечивает итеративное
улучшение детекторов аномалий через три режима работы:

1. **Разведка** (--path): Сканирует новые файлы по указанным путям
2. **Тюнинг** (по умолчанию): Перепроверяет файлы из result_anomalies_registry.json
3. **Исправление** (--fix): Применяет исправления для книг из карантина

Основной артефакт: result_anomalies_registry.json - персистентный файл состояния.

Принципы:
- Разделение задач: четкое разделение режимов работы
- Единый источник логики: все проверки через BookValidator
- Безопасность по умолчанию: изменяющие операции требуют флагов
- Максимальное переиспользование: использует существующие компоненты
"""

import argparse
import logging
import multiprocessing
import os
import sys
import threading
import time
from concurrent.futures import ProcessPoolExecutor, as_completed
from pathlib import Path
from typing import Any, Generator, Optional

# Добавляем корневую директорию проекта в sys.path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.database.queries import delete_from_quarantine, get_all_quarantined_books

from tools.analysis.registry import AnomalyPathRegistry

# Настройка базового логирования
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    datefmt="%H:%M:%S",
)
logger = logging.getLogger(__name__)

# Конфигурация производительности
DEFAULT_WORKER_THREADS = os.cpu_count() or 4  # Количество потоков для параллельной обработки

# Импорты для полной функциональности (могут отсутствовать в тестовой среде)
try:
    from app.utils.helpers import extract_source_id

    from tools.analysis.utils import (
        format_book_log_entry,
        get_anomaly_type_translations_plural,
    )
    from tools.utils import get_canonical_book_for_anomaly_detection

    FULL_FUNCTIONALITY = True
except ImportError as e:
    logger.warning(f"⚠️ Некоторые компоненты недоступны: {e}")
    logger.warning("🔧 Работает только режим первичной синхронизации")
    FULL_FUNCTIONALITY = False


# Глобальные компоненты для worker процессов (инициализируются один раз на процесс)
worker_storage_manager = None
worker_book_validator = None
worker_parser_dispatcher = None


def init_worker():
    """Инициализирует тяжелые компоненты один раз на каждый процесс-воркер.

    Устраняет накладные расходы на создание объектов для каждой книги.
    После оптимизации I/O и CPU эти накладные расходы стали доминирующими.

    УНИФИКАЦИЯ: Используем синглтон ToolsComponents для консистентности с другими инструментами.
    Каждый процесс в ProcessPoolExecutor получает свой экземпляр синглтона.
    """
    global worker_storage_manager, worker_book_validator, worker_parser_dispatcher

    # Подавляем избыточные логи в worker процессах
    setup_logging(debug=False)

    if not FULL_FUNCTIONALITY:
        # В режиме ограниченной функциональности компоненты недоступны
        worker_storage_manager = None
        worker_book_validator = None
        worker_parser_dispatcher = None
        return

    # УНИФИКАЦИЯ: Используем синглтон компонентов вместо прямого создания
    try:
        from tools.utils import get_components

        components = get_components()
        if not components.has_full_functionality():
            # Fallback на случай проблем с компонентами
            worker_storage_manager = None
            worker_book_validator = None
            worker_parser_dispatcher = None
            return

        worker_storage_manager = components.storage_manager
        worker_book_validator = components.book_validator
        worker_parser_dispatcher = components.parser_dispatcher  # КРИТИЧНО для производительности!
    except ImportError:
        # Fallback на случай проблем с импортами
        worker_storage_manager = None
        worker_book_validator = None
        worker_parser_dispatcher = None


def _process_single_book_task(task_args: tuple) -> dict[str, Any]:
    """Рабочая функция для обработки одной книги в отдельном процессе.

    Args:
        task_args: Кортеж (archive_path, book_filename)

    Returns:
        Словарь с результатом обработки
    """
    archive_path, book_filename = task_args

    try:
        # Используем предварительно инициализированные компоненты воркера
        # Это устраняет накладные расходы на создание объектов для каждой книги
        storage_manager = worker_storage_manager
        book_validator = worker_book_validator
        parser_dispatcher = worker_parser_dispatcher

        # Fallback на случай проблем с инициализацией воркера
        if storage_manager is None or book_validator is None or parser_dispatcher is None:
            # УНИФИКАЦИЯ: Используем синглтон даже в fallback
            from tools.utils import get_components

            components = get_components()
            if components.has_full_functionality():
                storage_manager = components.storage_manager
                book_validator = components.book_validator
                parser_dispatcher = components.parser_dispatcher
            else:
                # Последний fallback - прямое создание (только если синглтон недоступен)
                from app.processing.book_validator import BookValidator
                from app.processing.parser_dispatcher import ParserDispatcher
                from app.storage import LocalStorageManager

                storage_manager = LocalStorageManager()
                book_validator = BookValidator()
                parser_dispatcher = ParserDispatcher()

        # Читаем файл из архива
        try:
            book_stream = storage_manager.read_file_from_archive(archive_path, book_filename)
        except Exception as e:
            return {
                "status": "error",
                "task_args": task_args,
                "log_entry": "",
                "anomalies": [],
                "error_message": f"Ошибка чтения файла: {e}",
            }

        # Используем ОПТИМИЗИРОВАННЫЙ парсер для детекции аномалий с предварительно инициализированным dispatcher
        canonical_book, parsing_report = get_canonical_book_for_anomaly_detection(
            book_stream, book_filename, None, parser_dispatcher
        )

        # Проверяем на аномалии используя parsing_report
        anomaly_objects = book_validator.validate(canonical_book, parsing_report)
        anomaly_types = [anomaly["type"] for anomaly in anomaly_objects]

        # Формируем строку лога
        log_entry = format_book_log_entry(
            archive_path,
            book_filename,
            canonical_book,
            anomaly_types if anomaly_types else None,
        )

        return {
            "status": "ok",
            "task_args": task_args,
            "log_entry": log_entry,
            "anomalies": anomaly_types,
            "error_message": "",
        }

    except Exception as e:
        source_id = extract_source_id(Path(book_filename))
        archive_name = Path(archive_path).name
        error_msg = f"{archive_name}/{source_id} - Ошибка: {e}"

        return {
            "status": "error",
            "task_args": task_args,
            "log_entry": "",
            "anomalies": [],
            "error_message": error_msg,
        }


def _recheck_quarantined_book_task(book_dict: dict) -> dict | None:
    """Рабочая функция для перепроверки одной книги из карантина.

    Args:
        book_dict: Словарь с данными о книге из карантина

    Returns:
        book_dict если книга исправлена, None если нет
    """
    try:
        details = book_dict.get("details", {})
        archive_path = details.get("archive_path")
        book_filename = details.get("book_filename")

        if not archive_path or not book_filename:
            return None

        # Используем предварительно инициализированные компоненты воркера
        # Это устраняет накладные расходы на создание объектов для каждой книги
        storage_manager = worker_storage_manager
        book_validator = worker_book_validator
        parser_dispatcher = worker_parser_dispatcher

        # Fallback на случай проблем с инициализацией воркера
        if storage_manager is None or book_validator is None or parser_dispatcher is None:
            # УНИФИКАЦИЯ: Используем синглтон даже в fallback
            from tools.utils import get_components

            components = get_components()
            if components.has_full_functionality():
                storage_manager = components.storage_manager
                book_validator = components.book_validator
                parser_dispatcher = components.parser_dispatcher
            else:
                # Последний fallback - прямое создание (только если синглтон недоступен)
                from app.processing.book_validator import BookValidator
                from app.processing.parser_dispatcher import ParserDispatcher
                from app.storage import LocalStorageManager

                storage_manager = LocalStorageManager()
                book_validator = BookValidator()
                parser_dispatcher = ParserDispatcher()

        # Читаем файл из архива
        try:
            book_stream = storage_manager.read_file_from_archive(archive_path, book_filename)
        except Exception:
            return None

        # Используем ОПТИМИЗИРОВАННЫЙ парсер для детекции аномалий с предварительно инициализированным dispatcher
        canonical_book, parsing_report = get_canonical_book_for_anomaly_detection(
            book_stream, book_filename, None, parser_dispatcher
        )

        # Проверяем на аномалии используя parsing_report
        anomalies = book_validator.validate(canonical_book, parsing_report)

        # Если блокирующих аномалий нет - книга исправлена
        if not anomalies:
            return book_dict

        return None

    except Exception:
        return None


class AnomalyOrchestrator:
    """Основной класс-оркестратор для анализа аномалий.

    Инкапсулирует всю логику определения режима работы и выполнения
    соответствующих операций с максимальным переиспользованием
    существующих компонентов проекта.
    """

    def __init__(self):
        """Инициализирует оркестратор с необходимыми компонентами."""
        # Реестр аномалий (всегда доступен)
        self.registry_path = Path(__file__).parent / "result_anomalies_registry.json"
        self.anomaly_registry = AnomalyPathRegistry(self.registry_path)

        # Основные компоненты (ленивая инициализация для избежания deadlock)
        # Эти компоненты будут созданы только при первом обращении к ним
        self.parser_dispatcher = None
        self.book_validator = None
        self.storage_manager = None
        self.queue_manager = None

        # Блокировки для потокобезопасности
        self.registry_lock = threading.Lock()
        self.stats_lock = threading.Lock()

        # Статистика
        self.stats = {
            "processed_files": 0,
            "anomalies_found": 0,
            "anomalies_fixed": 0,
            "errors": 0,
        }

        # Детальная статистика по типам аномалий
        self.anomaly_counts = {
            "trial_fragments": 0,
            "small_content": 0,
            "few_chapters": 0,
            "anthology_books": 0,
            "broken_footnotes": 0,
        }

        # Список файлов с ошибками для вывода в конце
        self.error_files = []

        # Отслеживание времени выполнения
        self.start_time = None
        self.end_time = None

    def _count_total_tasks(self, archives_to_scan: list[str]) -> int:
        """Подсчитывает общее количество задач без их материализации.

        Args:
            archives_to_scan: Список путей к архивам

        Returns:
            Общее количество задач для обработки
        """
        total_count = 0

        # Ленивая инициализация storage_manager для избежания deadlock
        if self.storage_manager is None:
            if FULL_FUNCTIONALITY:
                # УНИФИКАЦИЯ: Используем синглтон компонентов
                from tools.utils import get_components

                components = get_components()
                if components.has_full_functionality():
                    self.storage_manager = components.storage_manager
                else:
                    raise RuntimeError("Синглтон компонентов не имеет полной функциональности")
            else:
                raise RuntimeError("LocalStorageManager недоступен")

        for archive_path in archives_to_scan:
            try:
                # Получаем список книжных файлов в архиве
                book_files = self.storage_manager.list_books_in_archive(archive_path)

                # Подсчитываем задачи для каждой книги
                for book_filename in book_files:
                    # Проверяем, не исключен ли файл
                    if not self.anomaly_registry.is_excluded(archive_path, book_filename):
                        total_count += 1

            except Exception as e:
                # Логируем ошибку архива, но продолжаем работу с другими архивами
                archive_name = Path(archive_path).name
                with self.stats_lock:
                    self.stats["errors"] += 1
                    self.error_files.append(f"{archive_name} - Ошибка архива: {e}")

        return total_count

    def _generate_book_tasks(self, archives_to_scan: list[str]) -> Generator[tuple[str, str], None, None]:
        """Генератор задач для потоковой обработки книг.

        Args:
            archives_to_scan: Список путей к архивам

        Yields:
            Кортежи (archive_path, book_filename)
        """
        # Ленивая инициализация storage_manager для избежания deadlock
        if self.storage_manager is None:
            if FULL_FUNCTIONALITY:
                # УНИФИКАЦИЯ: Используем синглтон компонентов
                from tools.utils import get_components

                components = get_components()
                if components.has_full_functionality():
                    self.storage_manager = components.storage_manager
                else:
                    raise RuntimeError("Синглтон компонентов не имеет полной функциональности")
            else:
                raise RuntimeError("LocalStorageManager недоступен")

        for archive_path in archives_to_scan:
            try:
                # Получаем список книжных файлов в архиве
                book_files = self.storage_manager.list_books_in_archive(archive_path)

                # Генерируем задачи для каждой книги
                for book_filename in book_files:
                    # Проверяем, не исключен ли файл
                    if not self.anomaly_registry.is_excluded(archive_path, book_filename):
                        yield (archive_path, book_filename)

            except Exception as e:
                # Логируем ошибку архива, но продолжаем работу с другими архивами
                archive_name = Path(archive_path).name
                with self.stats_lock:
                    self.stats["errors"] += 1
                    self.error_files.append(f"{archive_name} - Ошибка архива: {e}")

    def _process_tasks_with_sliding_window(
        self, task_generator, worker_function, executor, results_handler=None, total_tasks=None
    ):
        """Универсальный обработчик задач с паттерном Sliding Window.

        Поддерживает константное количество активных Future объектов для предотвращения
        переполнения памяти при обработке больших объемов данных.

        Args:
            task_generator: Генератор задач (tuple или dict)
            worker_function: Функция-обработчик для воркеров
            executor: ProcessPoolExecutor instance
            results_handler: Опциональная функция для кастомной обработки результатов
            total_tasks: Общее количество задач для прогресс-бара (опционально)
        """
        # Импортируем tqdm для прогресс-бара
        has_real_tqdm = False
        try:
            from tqdm import tqdm

            has_real_tqdm = True
        except ImportError:
            # Простая заглушка для tqdm
            class SimpleTqdm:
                def __init__(self, total, desc):
                    self.total = total
                    self.desc = desc
                    self.count = 0

                def update(self, n=1):
                    self.count += n
                    if self.count % 50 == 0:  # Логируем каждые 50 файлов
                        print(f"{self.desc}: {self.count}/{self.total if self.total else '?'}")

                def close(self):
                    if self.total and self.count < self.total:
                        print(f"{self.desc}: {self.count}/{self.total}")

            tqdm = SimpleTqdm
        # Размер окна: max_workers * 2 для оптимальной утилизации CPU
        window_size = min(DEFAULT_WORKER_THREADS * 2, 200)  # Ограничиваем максимум

        # Активные Future объекты
        active_futures = {}  # {future: task_args}

        # Используем переданное количество задач или пытаемся определить автоматически
        if total_tasks is None:
            if hasattr(task_generator, "__len__"):
                total_tasks = len(task_generator)
            elif hasattr(task_generator, "__iter__"):
                # Для генераторов пытаемся получить размер из атрибутов
                if hasattr(task_generator, "total_count"):
                    total_tasks = task_generator.total_count

        # Инициализируем прогресс-бар
        progress_desc = "📚 Обработка книг"
        if has_real_tqdm:
            pbar = tqdm(total=total_tasks, desc=progress_desc, unit="книг")
        else:
            pbar = SimpleTqdm(total=total_tasks, desc=progress_desc)

        # Заполняем начальное окно
        task_iter = iter(task_generator)

        try:
            # Запускаем начальный набор задач до размера окна
            for _ in range(window_size):
                try:
                    task_args = next(task_iter)
                    future = executor.submit(worker_function, task_args)
                    active_futures[future] = task_args
                except StopIteration:
                    break

            # Главный цикл обработки с sliding window
            while active_futures:
                # Ждем завершения любой из активных задач
                for completed_future in as_completed(active_futures.keys()):
                    task_args = active_futures.pop(completed_future)

                    try:
                        # Получаем результат
                        result = completed_future.result()

                        # Используем кастомный обработчик или стандартный
                        if results_handler:
                            results_handler(result, task_args)
                        else:
                            self._handle_standard_result(result, task_args, has_real_tqdm)

                    except Exception as e:
                        # Обрабатываем ошибку выполнения
                        with self.stats_lock:
                            self.stats["errors"] += 1
                            archive_path = task_args[0] if isinstance(task_args, tuple) else "unknown"
                            archive_name = Path(archive_path).name if archive_path != "unknown" else "unknown"
                            self.error_files.append(f"{archive_name} - Ошибка выполнения: {e}")

                    # Обновляем прогресс-бар
                    pbar.update(1)

                    # Пытаемся запустить новую задачу на освободившееся место
                    try:
                        next_task_args = next(task_iter)
                        new_future = executor.submit(worker_function, next_task_args)
                        active_futures[new_future] = next_task_args
                    except StopIteration:
                        # Генератор исчерпан, просто продолжаем с уменьшающимся окном
                        pass

                    # Принудительно выводим буферы для немедленного отображения логов
                    sys.stdout.flush()

                    # Продолжаем обрабатывать все завершившиеся задачи без break
                    # Это обеспечивает максимальную утилизацию пула воркеров

        except Exception as e:
            # Критическая ошибка в главном цикле
            logger.error(f"❌ Критическая ошибка в обработчике sliding window: {e}")
        finally:
            # Закрываем прогресс-бар
            pbar.close()

    def _handle_standard_result(self, result, task_args, has_real_tqdm=False):
        """Стандартная обработка результатов для режимов exploration и tuning."""
        if result["status"] == "ok":
            # Печатаем лог с учетом прогресс-бара
            if result["log_entry"]:
                if has_real_tqdm:
                    # Используем tqdm.write() для корректного отображения
                    from tqdm import tqdm

                    tqdm.write(result["log_entry"])
                else:
                    print(result["log_entry"])

            # Обновляем реестр и статистику
            archive_path, book_filename = task_args
            anomalies = result["anomalies"]

            with self.registry_lock:
                # Удаляем старые записи для этого файла
                for anomaly_type in self.anomaly_registry.registry_data.get("anomalies", {}):
                    self.anomaly_registry.remove_anomaly_path(anomaly_type, archive_path, book_filename)

                # Добавляем новые аномалии
                for anomaly_type in anomalies:
                    self.anomaly_registry.add_anomaly_path(anomaly_type, archive_path, book_filename)

            with self.stats_lock:
                self.stats["processed_files"] += 1
                self.stats["anomalies_found"] += len(anomalies)

                # Обновляем счетчики по типам аномалий
                for anomaly_type in anomalies:
                    if anomaly_type in self.anomaly_counts:
                        self.anomaly_counts[anomaly_type] += 1
        else:
            # Обрабатываем ошибку
            with self.stats_lock:
                self.stats["errors"] += 1
                if result["error_message"]:
                    self.error_files.append(result["error_message"])

    def _handle_fix_mode_result(self, result, task_args, fixed_books_list):
        """Кастомный обработчик результатов для режима исправления."""
        with self.stats_lock:
            self.stats["processed_files"] += 1

        if result is not None:
            fixed_books_list.append(result)
            with self.stats_lock:
                self.stats["anomalies_fixed"] += 1

    def run(self, args: argparse.Namespace) -> int:
        """Главная точка входа - определяет режим и запускает соответствующую логику.

        Args:
            args: Аргументы командной строки

        Returns:
            Код возврата (0 = успех, 1 = ошибка)
        """
        try:
            # Определяем режим работы
            if args.path or args.from_file:
                return self._run_exploration_mode(args)
            elif args.fix:
                return self._run_fix_mode(args)
            else:
                return self._run_tuning_mode(args)

        except KeyboardInterrupt:
            print("🛑 Прервано пользователем")
            return 1
        except Exception as e:
            logger.error(f"❌ Критическая ошибка: {e}")
            return 1

    def _run_exploration_mode(self, args: argparse.Namespace) -> int:
        """Режим Разведка: сканирует новые файлы по указанным путям.

        Args:
            args: Аргументы командной строки

        Returns:
            Код возврата
        """
        if not FULL_FUNCTIONALITY:
            logger.error("❌ Режим Разведка требует полной функциональности")
            return 1

        print("🔍 Режим: Разведка - сканирование новых файлов")

        # Собираем пути для сканирования
        paths_to_scan = []

        if args.path:
            paths_to_scan.extend(args.path)

        if args.from_file:
            try:
                with open(args.from_file, "r", encoding="utf-8") as f:
                    file_paths = [line.strip() for line in f if line.strip()]
                    paths_to_scan.extend(file_paths)
            except FileNotFoundError:
                logger.error(f"❌ Файл не найден: {args.from_file}")
                return 1

        if not paths_to_scan:
            logger.error("❌ Не указаны пути для сканирования")
            return 1

        # Загружаем реестр для получения excluded_files
        self.anomaly_registry.load_registry()

        print(f"📁 Сканирование {len(paths_to_scan)} путей...")

        # Собираем архивы для сканирования
        archives_to_scan = []
        for path_str in paths_to_scan:
            path = Path(path_str)
            if path.is_file() and path.suffix.lower() == ".zip":
                archives_to_scan.append(str(path))
            elif path.is_dir():
                # Ищем ZIP архивы в директории
                zip_files = list(path.glob("*.zip"))
                archives_to_scan.extend([str(f) for f in zip_files])

        if not archives_to_scan:
            logger.warning("🛑 Не найдено ZIP архивов для сканирования")
            return 0

        # Подсчитываем общее количество файлов в архивах
        total_files = 0
        for archive_path in archives_to_scan:
            try:
                import zipfile

                with zipfile.ZipFile(archive_path, "r") as zip_ref:
                    fb2_files = [f for f in zip_ref.namelist() if f.lower().endswith(".fb2")]
                    total_files += len(fb2_files)
            except Exception:
                pass  # Игнорируем ошибки подсчета

        print(f"📦 Найдено {len(archives_to_scan)} архивов для сканирования ({total_files} файлов)")
        print("")  # Пустая строка

        # Запускаем таймер
        self.start_time = time.time()

        # Подсчитываем общее количество задач для прогресс-бара
        total_tasks = self._count_total_tasks(archives_to_scan)
        logger.info(f"📦 Найдено {len(archives_to_scan)} архивов, всего {total_tasks} задач для обработки.")

        # Создаем генератор задач
        task_generator = self._generate_book_tasks(archives_to_scan)

        # Используем ProcessPoolExecutor для параллельной обработки с инициализацией воркеров
        with ProcessPoolExecutor(max_workers=DEFAULT_WORKER_THREADS, initializer=init_worker) as executor:
            self._process_tasks_with_sliding_window(
                task_generator, _process_single_book_task, executor, total_tasks=total_tasks
            )

        # Останавливаем таймер
        self.end_time = time.time()

        # Сохраняем обновленный реестр
        self.anomaly_registry.save_registry()

        print("✅ Разведка завершена")
        print("")  # Пустая строка
        self._print_stats()
        return 0

    def _run_tuning_mode(self, args: argparse.Namespace) -> int:
        """Режим Тюнинг: перепроверяет файлы из result_anomalies_registry.json.

        Args:
            args: Аргументы командной строки

        Returns:
            Код возврата
        """
        print("🔧 Режим: Тюнинг - перепроверка известных аномалий")

        # Первичная синхронизация не требует полной функциональности
        if not self.registry_path.exists():
            print("📋 Реестр аномалий не найден, выполняем первичную синхронизацию...")
            return self._perform_initial_sync()

        # Остальная функциональность требует полных компонентов
        if not FULL_FUNCTIONALITY:
            logger.error("❌ Режим Тюнинг (перепроверка) требует полной функциональности")
            print("💡 Доступна только первичная синхронизация")
            return 1

        # Загружаем реестр
        if not self.anomaly_registry.load_registry():
            logger.error("❌ Не удалось загрузить реестр аномалий")
            return 1

        # Получаем список файлов для перепроверки
        files_to_check = self._get_files_for_tuning(args.type)

        if not files_to_check:
            print("📋 Нет файлов для перепроверки")
            return 0

        # Подсчитываем количество уникальных архивов
        unique_archives = set()
        for archive_path, _ in files_to_check:
            unique_archives.add(archive_path)

        print(f"🔍 Перепроверка {len(unique_archives)} архивов и {len(files_to_check)} файлов...")
        print("")  # Пустая строка

        # Запускаем таймер
        self.start_time = time.time()

        # Используем ProcessPoolExecutor для параллельной перепроверки с инициализацией воркеров
        with ProcessPoolExecutor(max_workers=DEFAULT_WORKER_THREADS, initializer=init_worker) as executor:
            self._process_tasks_with_sliding_window(
                files_to_check, _process_single_book_task, executor, total_tasks=len(files_to_check)
            )

        # Останавливаем таймер
        self.end_time = time.time()

        # Сохраняем обновленный реестр
        self.anomaly_registry.save_registry()

        print("✅ Тюнинг завершен")
        self._print_stats()
        return 0

    def _run_fix_mode(self, args: argparse.Namespace) -> int:
        """Режим Исправление: применяет исправления для книг из карантина.

        Args:
            args: Аргументы командной строки

        Returns:
            Код возврата
        """
        if not FULL_FUNCTIONALITY:
            logger.error("❌ Режим Исправление требует полной функциональности")
            return 1

        # Формируем описание примененных фильтров
        filter_info = []
        if args.type:
            filter_info.append(f"тип: {args.type}")
        if args.since_days:
            filter_info.append(f"за {args.since_days} дней")

        filter_str = f" (фильтры: {', '.join(filter_info)})" if filter_info else ""
        print(f"🔧 Режим: Исправление - анализ карантинных книг{filter_str}")

        try:
            # Получаем книги из карантина с применением фильтров
            quarantined_books = get_all_quarantined_books(filter_type=args.type, since_days=args.since_days)

            if not quarantined_books:
                no_books_msg = "📋 Карантин пуст - нет книг для проверки"
                if filter_info:
                    no_books_msg += f" (с учетом фильтров: {', '.join(filter_info)})"
                print(no_books_msg)
                return 0

            print(f"📋 Найдено {len(quarantined_books)} книг в карантине{filter_str}")

            # Анализируем карантинные книги параллельно с ProcessPoolExecutor
            fixed_books: list[dict[str, Any]] = []

            # Создаем кастомный results_handler с замыканием для доступа к fixed_books
            def fix_mode_handler(result, task_args):
                self._handle_fix_mode_result(result, task_args, fixed_books)

            with ProcessPoolExecutor(max_workers=DEFAULT_WORKER_THREADS, initializer=init_worker) as executor:
                self._process_tasks_with_sliding_window(
                    quarantined_books,
                    _recheck_quarantined_book_task,
                    executor,
                    results_handler=fix_mode_handler,
                    total_tasks=len(quarantined_books),
                )

            if not fixed_books:
                print("📋 Нет книг готовых к исправлению")
                return 0

            # Показываем план действий
            print(f"✅ Найдено {len(fixed_books)} книг готовых к исправлению:")
            for book in fixed_books[:10]:  # Показываем первые 10
                details = book.get("details", {})
                book_filename = details.get("book_filename", "N/A")
                print(f"  📖 {book['source_type']}:{book['source_id']} - {book_filename}")

            if len(fixed_books) > 10:
                print(f"  ... и еще {len(fixed_books) - 10} книг")

            # Запрашиваем подтверждение если не указан --yes
            if not args.yes:
                response = input(f"\n🔧 Применить исправления для {len(fixed_books)} книг? [y/N]: ")
                if response.lower() not in ["y", "yes", "да"]:
                    print("🛑 Исправление отменено пользователем")
                    return 0

            # Применяем исправления
            success_count = self._apply_fixes(fixed_books)

            print(f"✅ Исправление завершено: {success_count}/{len(fixed_books)} книг")
            return 0

        except Exception as e:
            logger.error(f"❌ Ошибка при работе с карантином: {e}")
            return 1

    def _perform_initial_sync(self) -> int:
        """Выполняет первичную синхронизацию реестра с карантином PostgreSQL.

        Returns:
            Код возврата
        """
        try:
            print("🔄 Синхронизация с карантином PostgreSQL...")

            # Получаем все книги из карантина
            quarantined_books = get_all_quarantined_books()

            # Очищаем и пересоздаем реестр
            self.anomaly_registry.clear_and_rebuild()

            # Добавляем аномалии в реестр
            for book in quarantined_books:
                details = book.get("details", {})
                archive_path = details.get("archive_path", "")
                book_filename = details.get("book_filename", "")
                quarantine_type = book["primary_quarantine_type"]

                if archive_path and book_filename:
                    self.anomaly_registry.add_anomaly_path(quarantine_type, archive_path, book_filename)

            # Сохраняем реестр
            self.anomaly_registry.save_registry()

            print(f"✅ Синхронизация завершена: {len(quarantined_books)} записей")
            print(f"📋 Реестр сохранен: {self.registry_path}")

            return 0

        except Exception as e:
            logger.error(f"❌ Ошибка синхронизации: {e}")
            return 1

    def _get_files_for_tuning(self, anomaly_type_filter: Optional[str]) -> list[tuple[str, str]]:
        """Получает список файлов для перепроверки в режиме тюнинга.

        Args:
            anomaly_type_filter: Фильтр по типу аномалии (опционально)

        Returns:
            Список кортежей (archive_path, fb2_filename)
        """
        files_to_check = []
        anomalies = self.anomaly_registry.registry_data.get("anomalies", {})

        for anomaly_type, file_paths in anomalies.items():
            # Применяем фильтр если указан
            if anomaly_type_filter and anomaly_type != anomaly_type_filter:
                continue

            for file_path in file_paths:
                if "::" in file_path:
                    archive_path, fb2_filename = file_path.split("::", 1)
                    files_to_check.append((archive_path, fb2_filename))

        return files_to_check

    def _apply_fixes(self, fixed_books: list[dict[str, Any]]) -> int:
        """Применяет исправления: удаляет из карантина и ставит в очередь.

        Args:
            fixed_books: Список исправленных книг

        Returns:
            Количество успешно исправленных книг
        """
        # Ленивая инициализация queue_manager для избежания deadlock
        if self.queue_manager is None:
            if FULL_FUNCTIONALITY:
                # УНИФИКАЦИЯ: Используем синглтон компонентов
                from tools.utils import get_components

                components = get_components()
                if components.has_full_functionality():
                    self.queue_manager = components.queue_manager
                else:
                    raise RuntimeError("Синглтон компонентов не имеет полной функциональности")
            else:
                raise RuntimeError("TaskQueueManager недоступен")

        success_count = 0

        for book in fixed_books:
            try:
                source_type = book["source_type"]
                source_id = book["source_id"]
                details = book.get("details", {})

                # Удаляем из карантина
                was_deleted = delete_from_quarantine(source_type, source_id)

                if was_deleted:
                    # Формируем task_data для постановки в очередь
                    task_data = {
                        "source_type": source_type,
                        "source_id": source_id,
                        "archive_path": details.get("archive_path"),
                        "book_filename": details.get("book_filename"),
                        "archive_mtime": details.get("archive_mtime", 0.0),
                    }

                    # Ставим в очередь парсинга
                    if self.queue_manager.enqueue_parsing_task(task_data):
                        success_count += 1
                        logger.debug(f"✅ Исправлено: {source_type}:{source_id}")
                    else:
                        logger.warning(f"⚠️ Удалено из карантина, но не поставлено в очередь: {source_type}:{source_id}")
                else:
                    logger.warning(f"⚠️ Книга не была в карантине: {source_type}:{source_id}")

            except Exception as e:
                logger.error(f"❌ Ошибка исправления {book['source_type']}:{book['source_id']}: {e}")

        return success_count

    def _format_anomalies_summary(self) -> str:
        """Форматирует сводку по типам аномалий."""
        parts = []
        type_names = get_anomaly_type_translations_plural()

        for anomaly_type, count in self.anomaly_counts.items():
            if count > 0:
                type_name = type_names.get(anomaly_type, anomaly_type)
                parts.append(f"{type_name}: {count}")

        return ", ".join(parts) if parts else "0"

    def _print_stats(self) -> None:
        """Выводит статистику работы."""
        # Выводим ошибки перед финальной статистикой
        if self.error_files:
            print(f"❌ Ошибки обработки: {len(self.error_files)}")
            for error_info in self.error_files:
                print(f"  {error_info}")

        print("")  # Пустая строка
        print("📊 Статистика:")
        print(f"  📁 Обработано файлов: {self.stats['processed_files']}")

        # Детальная статистика по аномалиям
        anomalies_summary = self._format_anomalies_summary()
        print(f"  🔍 Найдено аномалий: {anomalies_summary}")

        # Статистика исправлений с детализацией
        if self.stats["anomalies_fixed"] > 0:
            print(f"  ✅ Исправлено книг: {self.stats['anomalies_fixed']} ({anomalies_summary})")
        else:
            print("  ✅ Исправлено книг: 0")

        # Показываем скорость обработки
        if self.start_time and self.end_time and self.stats["processed_files"] > 0:
            elapsed_time = self.end_time - self.start_time
            if elapsed_time > 0:
                speed = self.stats["processed_files"] / elapsed_time
                print(f"  🚀 Скорость: {speed:.2f} книг/сек")


def setup_logging(debug: bool = False) -> None:
    """Настраивает логирование для инструмента."""
    level = logging.DEBUG if debug else logging.INFO

    # Настраиваем корневой логгер
    logging.basicConfig(
        level=level,
        format="%(asctime)s - %(levelname)s - %(message)s",
        datefmt="%H:%M:%S",
    )

    # Отключаем подробное логирование парсеров и других компонентов
    if not debug:
        # Отключаем все логи от парсеров кроме CRITICAL
        logging.getLogger("app.processing.parsers").setLevel(logging.CRITICAL)
        logging.getLogger("app.processing.parser_dispatcher").setLevel(logging.CRITICAL)
        logging.getLogger("app.processing.parsers.fb2").setLevel(logging.CRITICAL)
        logging.getLogger("app.processing.parsers.fb2.fb2_parser").setLevel(logging.CRITICAL)
        logging.getLogger("app.processing.parsers.fb2.fb2_transformer").setLevel(logging.CRITICAL)
        logging.getLogger("app.processing").setLevel(logging.CRITICAL)


def main() -> int:
    """Главная функция инструмента."""
    parser = argparse.ArgumentParser(
        description="Профессиональный инструмент для тюнинга детекторов аномалий",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Режимы работы:
  Разведка:    --path /p1 /p2  или  --from-file file.txt
  Тюнинг:      (без аргументов)  или  --type trial
  Исправление: --fix [--yes] [--since-days N]

Примеры:
  %(prog)s                           # Тюнинг всех аномалий
  %(prog)s --type trial              # Тюнинг только trial аномалий
  %(prog)s --path /data/books        # Разведка новых файлов
  %(prog)s --fix                     # Исправление карантинных книг
  %(prog)s --fix --yes               # Исправление без подтверждения
  %(prog)s --fix --since-days 7      # Исправление книг за последние 7 дней
        """,
    )

    # Режим Разведка
    parser.add_argument("--path", nargs="+", help="Пути для сканирования новых файлов (режим Разведка)")
    parser.add_argument("--from-file", help="Файл с путями для сканирования (режим Разведка)")

    # Режим Тюнинг
    parser.add_argument("--type", help="Тип аномалий для фокусного тюнинга (trial, small_content, etc.)")

    # Режим Исправление
    parser.add_argument("--fix", action="store_true", help="Режим исправления карантинных книг")
    parser.add_argument(
        "--yes",
        action="store_true",
        help="Пропустить интерактивное подтверждение для --fix",
    )
    parser.add_argument(
        "--since-days",
        type=int,
        help="Фильтр карантина: книги обновленные за последние N дней (только с --fix)",
    )

    # Общие опции
    parser.add_argument("--debug", action="store_true", help="Включить отладочный режим")

    args = parser.parse_args()

    # Валидация аргументов
    if args.path and args.type:
        parser.error("--path несовместим с --type")
    if args.path and args.fix:
        parser.error("--path несовместим с --fix")
    if args.from_file and args.type:
        parser.error("--from-file несовместим с --type")
    if args.from_file and args.fix:
        parser.error("--from-file несовместим с --fix")
    if args.yes and not args.fix:
        parser.error("--yes работает только с --fix")
    if args.since_days and not args.fix:
        parser.error("--since-days работает только с --fix")

    # Настройка логирования
    setup_logging(args.debug)

    # Запуск оркестратора
    orchestrator = AnomalyOrchestrator()
    return orchestrator.run(args)


if __name__ == "__main__":
    # Устанавливаем метод запуска 'spawn' для решения проблем с lxml и fork()
    # force=True необходимо, если контекст уже был инициализирован
    multiprocessing.set_start_method("spawn", force=True)

    sys.exit(main())
