# 🧪 Регрессионное тестирование логики разбиения на главы

## Назначение

Защищает от поломок логики парсинга при добавлении новых эвристик или изменении существующих.

## Быстрый старт

```bash
# Запуск всех тестов
python test_chapter_parsing_regression.py

# Подробный вывод
python test_chapter_parsing_regression.py --verbose

# Показать только провалившиеся тесты (минимальный вывод)
python test_chapter_parsing_regression.py --only-failed
```

## Добавление нового тестового случая

### Способ 1: Интерактивно
```bash
python test_chapter_parsing_regression.py --add
```

### Способ 2: Вручную
Отредактируйте `TEST_CASES` в файле `test_chapter_parsing_regression.py`:

```python
TEST_CASES = [
    # Существующие тесты...
    
    TestCase(
        name="Описательное название",
        file_path="/path/to/archive.zip::book.fb2",
        expected_chapters=42,
        description="Особенности разметки",
        strategy="HEURISTIC"  # опционально
    ),
]
```

## Когда добавлять тесты

1. **После исправления проблемной книги** - добавьте её как тест
2. **Перед изменением логики** - добавьте существующие рабочие случаи
3. **При обнаружении регрессии** - воспроизведите проблему в тесте

## Пример использования

```bash
# 1. Нашли проблемную книгу
python tools/run_diagnostic_tool.py --input "/path/to/problem.zip::book.fb2"

# 2. Исправили логику парсинга
# ... изменения в коде ...

# 3. Добавили тест для защиты от регрессии
python test_chapter_parsing_regression.py --add

# 4. Проверили, что все тесты проходят
python test_chapter_parsing_regression.py

# 5. Коммитим изменения с уверенностью
```

## Структура тестового случая

```python
@dataclass
class TestCase:
    name: str                    # Человекочитаемое имя
    file_path: str              # Путь к файлу (archive.zip::book.fb2)
    expected_chapters: int      # Ожидаемое количество глав
    description: str = ""       # Описание особенностей
    strategy: Optional[str] = None  # Ожидаемая стратегия
```

## Советы

- **Используйте описательные имена** для тестов
- **Указывайте стратегию** если важно, какая именно сработала
- **Добавляйте описание** особенностей разметки
- **Запускайте тесты** перед каждым коммитом изменений в логику

## Интеграция с CI/CD

Добавьте в ваш pipeline:

```bash
# Проверка регрессий перед деплоем
python test_chapter_parsing_regression.py || exit 1
```
