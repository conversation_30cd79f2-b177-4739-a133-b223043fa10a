#!/usr/bin/env python3
"""
Диагностический инструмент для комплексного анализа книг.

АРХИТЕКТУРА (2025-07-16):
Инструмент использует BookProcessor в диагностическом режиме для точной имитации
производственного пайплайна обработки. Это гарантирует полную совместимость
результатов диагностики с реальным поведением системы.

КЛЮЧЕВЫЕ ОСОБЕННОСТИ:
- Использует BookProcessor с diagnostic_mode=True - БЕЗ дублирования логики
- Собирает ВСЕ аномалии без прерывания обработки на первой ошибке
- Автоматическая синхронизация с изменениями в производственном пайплайне
- Безопасное тестирование: НЕ записывает в БД, НЕ сохраняет артефакты

ВОЗМОЖНОСТИ:
- Анализ книг из локальных файлов и архивов (поддержка формата archive_path::file_in_archive)
- Обнаружение всех типов аномалий через единую логику валидации
- Формирование детального JSON-отчета с диагностической информацией
- Полная имитация производственного пайплайна (парсинг → валидация → хэширование)
- Статистика и метрики обработки

ТЕХНИЧЕСКИЕ ДЕТАЛИ:
- Для локальных файлов создается временный ZIP архив (автоматически удаляется)
- BookProcessor.detected_anomalies содержит все найденные аномалии
- Ошибки БД ожидаемы и нормальны в диагностическом режиме

ИСПОЛЬЗОВАНИЕ:
    python tools/run_diagnostic_tool.py --input path/to/book.fb2
    python tools/run_diagnostic_tool.py --input archive.zip::book.fb2
    python tools/run_diagnostic_tool.py --input book.fb2 --debug

ВЫХОДНЫЕ ДАННЫЕ:
- Консольная сводка с ключевыми метриками
- Детальный JSON-отчет в tools/result_diagnostic_report.json
"""

import argparse
import io
import logging
import sys
from pathlib import Path

# Добавление корневой директории проекта в sys.path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Импорты проектных компонентов для диагностики
# Импорты модулей анализа
from analysis import (
    DiagnosticReportBuilder,
    parse_input_path,
    print_console_summary,
    save_report,
)
from analysis.serialization import create_summary_stats, serialize_book_for_database
from app.processing.date_extractor import extract_best_date
from app.processing.pruner import prune_book

# Константы для выходных файлов
DIAGNOSTIC_REPORT_PATH = Path(__file__).parent / "result_diagnostic_report.json"

# Настройка логирования
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


def run_diagnostic(input_path: str) -> dict:
    """Выполняет полную диагностику книги с последовательными этапами обработки.

    Функция имитирует полный пайплайн обработки книги без записи в базу данных
    и файловую систему. Все ошибки логируются, но не прерывают выполнение.

    АРХИТЕКТУРА: Использует оригинальную логику диагностики для парсинга и извлечения,
    но применяет BookProcessor в diagnostic_mode для точной валидации аномалий.

    Args:
        input_path: Путь к файлу или archive_path::file_in_archive

    Returns:
        Словарь с полным диагностическим отчетом
    """
    # Инициализация построителя отчетов
    report_builder = DiagnosticReportBuilder(input_path)

    try:
        # Инициализация системных компонентов
        logger.debug("📋 Инициализация системных компонентов...")
        try:
            sys.path.insert(0, str(Path(__file__).parent.parent))
            from app.processing.book_processor import BookProcessor

            from tools.utils import get_components

            components = get_components()
            if not components.has_full_functionality():
                raise RuntimeError("Синглтон компонентов не имеет полной функциональности")

            parser_dispatcher = components.parser_dispatcher
            storage_manager = components.storage_manager

            # Создаем BookProcessor в диагностическом режиме ТОЛЬКО для валидации
            diagnostic_book_processor = BookProcessor(storage_manager=storage_manager, diagnostic_mode=True)

            # HashComputer создаем отдельно
            from app.processing.hash_computer import HashComputer

            hash_computer = HashComputer()
        except Exception as e:
            error_msg = f"Ошибка инициализации компонентов: {e}"
            logger.error(error_msg)
            report_builder.add_error(error_msg)
            return report_builder.finalize()

        report_builder.add_processing_stage("✅ Системные компоненты инициализированы")

        # Этап 1: Парсинг входного пути и извлечение содержимого файла
        logger.debug("📁 Извлечение содержимого файла...")
        archive_path, book_path = parse_input_path(input_path)

        if archive_path:
            # Чтение из архива через LocalStorageManager
            try:
                book_stream = storage_manager.read_file_from_archive(archive_path, book_path)
                # Получаем время модификации архива для передачи в парсер
                mtime = Path(archive_path).stat().st_mtime
                report_builder.add_processing_stage(f"✅ Файл извлечен из архива: {archive_path}::{book_path}")
            except Exception as e:
                error_msg = f"Ошибка чтения из архива {archive_path}::{book_path}: {e}"
                logger.error(error_msg)
                report_builder.add_error(error_msg)
                return report_builder.finalize()
        else:
            # Чтение локального файла
            try:
                with open(book_path, "rb") as f:
                    book_stream = io.BytesIO(f.read())
                mtime = Path(book_path).stat().st_mtime
                report_builder.add_processing_stage(f"✅ Локальный файл прочитан: {book_path}")
            except Exception as e:
                error_msg = f"Ошибка чтения локального файла {book_path}: {e}"
                logger.error(error_msg)
                report_builder.add_error(error_msg)
                return report_builder.finalize()

        # Этап 2: Парсинг в каноническую модель CanonicalBook
        logger.debug("📖 Парсинг в каноническую модель...")
        try:
            filename = book_path.split("/")[-1] if "/" in book_path else book_path
            canonical_book, parsing_report = parser_dispatcher.parse_to_canonical(
                source=book_stream, source_filename=filename, file_mtime=mtime
            )

            # Извлекаем информацию о стратегии разбиения на главы
            # Используем детализированную информацию если доступна, иначе fallback на базовую
            chapter_strategy = "unknown"
            if parsing_report:
                detailed_heuristic = parsing_report.diagnostics.get("detailed_chapter_heuristic")
                if detailed_heuristic and detailed_heuristic != "unknown":
                    chapter_strategy = detailed_heuristic
                else:
                    # Fallback на базовую информацию
                    chapter_strategy = parsing_report.diagnostics.get("chapter_heuristic", "unknown")
            chapters_count = len(canonical_book.chapters)

            report_builder.add_processing_stage(f"✅ Книга распарсена: '{canonical_book.title}'")
            logger.debug(f"Книга успешно распарсена: {canonical_book.title}")
            logger.info(f"📊 Главы: {chapters_count}, стратегия разбиения: {chapter_strategy}")
        except Exception as e:
            error_msg = f"Ошибка парсинга книги: {e}"
            logger.error(error_msg)
            report_builder.add_error(error_msg)
            return report_builder.finalize()

        # Этап 3: Обнаружение аномалий через диагностический BookProcessor
        logger.debug("🔍 Анализ аномалий через BookProcessor в диагностическом режиме...")
        try:
            # Используем _check_fragment() из диагностического BookProcessor
            # который собирает аномалии вместо выбрасывания исключений
            diagnostic_book_processor.detected_anomalies = []  # Сброс
            diagnostic_book_processor._last_parsing_report = parsing_report

            # Вызываем _check_fragment который в diagnostic_mode соберет аномалии
            diagnostic_book_processor._check_fragment(canonical_book)

            # Получаем собранные аномалии
            anomalies = diagnostic_book_processor.detected_anomalies
            report_builder.set_anomalies(anomalies)

            if anomalies:
                anomaly_types = [a["type"] for a in anomalies]
                report_builder.add_processing_stage(f"⚠️ Обнаружены аномалии: {', '.join(anomaly_types)}")
                logger.warning(f"Обнаружены аномалии: {anomaly_types}")
            else:
                report_builder.add_processing_stage("✅ Аномалий не обнаружено")
                logger.info("Аномалий не обнаружено")
        except Exception as e:
            error_msg = f"Ошибка при анализе аномалий: {e}"
            logger.error(error_msg)
            report_builder.add_error(error_msg)
            # Продолжаем обработку даже при ошибке валидации

        # Этап 4: Хэширование и генерация ID
        logger.debug("🔐 Вычисление хэшей и генерация ID...")
        try:
            # Вычисляем хэш метаданных
            hashes = hash_computer.compute_hashes(canonical_book)
            metadata_hash = hashes["metadata_hash"]

            # Извлекаем дату и генерируем book_id
            best_date, date_source = extract_best_date(canonical_book.raw_source_model, mtime)

            # Используем централизованную функцию генерации UUID v7
            from app.utils.uuid_generator import (
                create_book_signature_for_diagnostic,
                generate_book_uuid7,
            )

            book_signature = create_book_signature_for_diagnostic(canonical_book)
            book_id = generate_book_uuid7(best_date, book_signature)

            # Сохраняем информацию о дате в модели
            canonical_book.book_id_generation_date = best_date.isoformat()
            canonical_book.book_id_date_source = date_source

            report_builder.add_processing_stage(f"✅ ID сгенерирован: {book_id}")
            logger.info(f"ID книги сгенерирован: {book_id}")
        except Exception as e:
            error_msg = f"Ошибка генерации ID: {e}"
            logger.error(error_msg)
            report_builder.add_error(error_msg)
            # Используем заглушку для продолжения
            book_id = "error-generating-id"
            metadata_hash = "error-generating-hash"

        # Этап 5: Очистка данных
        logger.debug("🧹 Очистка канонической модели...")
        try:
            prune_book(canonical_book)
            report_builder.add_processing_stage("✅ Каноническая модель очищена")
        except Exception as e:
            error_msg = f"Ошибка очистки: {e}"
            logger.error(error_msg)
            report_builder.add_error(error_msg)

        # Этап 5.5: Финализация рендеринга глав
        logger.debug("🎯 Финализация рендеринга глав...")
        try:
            canonical_book.render_and_finalize_chapters()
            report_builder.add_processing_stage("✅ Все главы отрендерены и финализированы")
        except Exception as e:
            error_msg = f"Ошибка финализации глав: {e}"
            logger.error(error_msg)
            report_builder.add_error(error_msg)

        # Этап 6: Формирование database_payload
        logger.debug("📊 Формирование database payload...")
        try:
            database_payload = serialize_book_for_database(canonical_book, book_id, metadata_hash)
            report_builder.set_database_payload(database_payload)
            report_builder.add_processing_stage("✅ Database payload сформирован")
        except Exception as e:
            error_msg = f"Ошибка формирования database payload: {e}"
            logger.error(error_msg)
            report_builder.add_error(error_msg)

        # Этап 7: Формирование artifact_payload
        logger.debug("📦 Формирование artifact payload...")
        report_builder.build_artifact_payload(book_id, canonical_book, archive_path, filename)
        report_builder.add_processing_stage("✅ Artifact payload сформирован")

        # Этап 8: Формирование итоговой статистики
        logger.debug("📈 Подсчет итоговой статистики...")
        try:
            import time

            current_time = time.time()
            start_time = report_builder.report_data["diagnostic_info"]["start_time"]
            processing_time = current_time - start_time
            anomalies_count = report_builder.get_anomalies_count()

            # Передаем parsing_report для статистики по сноскам
            summary_stats = create_summary_stats(canonical_book, anomalies_count, processing_time, parsing_report)
            report_builder.set_summary_stats(summary_stats)
            report_builder.add_processing_stage("✅ Статистика собрана")
        except Exception as e:
            error_msg = f"Ошибка подсчета статистики: {e}"
            logger.error(error_msg)
            report_builder.add_error(error_msg)

    except Exception as e:
        # Обработка критических ошибок на верхнем уровне
        error_msg = f"Критическая ошибка диагностики: {e}"
        logger.error(error_msg, exc_info=True)
        report_builder.add_error(error_msg)

    # Финализация и возврат отчета
    return report_builder.finalize()


def main():
    """Главная функция диагностического инструмента."""
    # Настройка аргументов командной строки
    parser = argparse.ArgumentParser(
        description="Диагностический инструмент для комплексного анализа книг",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Примеры использования:
  %(prog)s --input /path/to/book.fb2
  %(prog)s --input archive.zip::book.fb2  
  %(prog)s --input book.fb2 --debug
        """,
    )

    parser.add_argument(
        "--input",
        required=True,
        help="Путь к файлу книги или формат archive_path::file_in_archive",
    )

    parser.add_argument("--debug", action="store_true", help="Включить детальное логирование")

    args = parser.parse_args()

    # Настройка уровня логирования
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.debug("Включен режим детального логирования")

    try:
        # Выполнение диагностики
        logger.debug(f"🚀 Начинаем диагностику: {args.input}")
        report_data = run_diagnostic(args.input)

        # Сохранение полного отчета в JSON
        save_report(report_data, DIAGNOSTIC_REPORT_PATH)

        # Вывод краткой сводки в консоль
        print_console_summary(report_data, args.input, DIAGNOSTIC_REPORT_PATH)

        # Возвращаем код ошибки если были критические проблемы
        errors_count = len(report_data.get("diagnostic_info", {}).get("errors", []))
        return 1 if errors_count > 0 else 0

    except Exception as e:
        logger.error(f"Критическая ошибка: {e}", exc_info=True)
        print(f"❌ Критическая ошибка: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
