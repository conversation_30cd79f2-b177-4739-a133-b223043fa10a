#!/usr/bin/env python3
"""
🤖 LLM XML Pipeline - Полуручной инструмент для исправления сломанных XML файлов с помощью LLM.
# Обычный режим
python tools/llm_xml_pipeline.py --path "/path/to/file.fb2"

# Итеративный режим  
python tools/llm_xml_pipeline.py --iterative --path "/path/to/archive.zip::file.fb2"

# С настройкой количества итераций
python tools/llm_xml_pipeline.py --iterative --max-iterations 5 --path "/path/to/file.fb2"


📋 КАК ПОЛЬЗОВАТЬСЯ:

🔄 ИТЕРАТИВНЫЙ РЕЖИМ (РЕКОМЕНДУЕТСЯ):
1. Запусти: python3 tools/llm_xml_pipeline.py --iterative --path "/path/to/file.fb2"
3. Pipeline создаст промпт в tmp/llm_XXXXX_prompt.json
4. Отправь содержимое промпта в LLM (ChatGPT/Claude)
5. Получи JSON ответ с исправлениями
6. Pipeline автоматически применит исправления и проверит результат
7. При неудаче создаст новый промпт с учетом предыдущих попыток
8. Повторяй до успеха или достижения лимита итераций

📋 ОБЫЧНЫЙ РЕЖИМ:
1. Запусти: python3 tools/llm_xml_pipeline.py --path "/path/to/file.fb2"
3. Pipeline создаст промпт в tmp/llm_XXXXX_prompt.json
4. Отправь содержимое промпта в LLM (ChatGPT/Claude)
5. Получи JSON ответ с bash командами для исправления
6. Примени bash команды к файлу tmp/llm_XXXXX_file.fb2
7. Проверь результат: python tools/run_diagnostic_tool.py --input исправленный_файл
8. При успехе замени оригинал с сохранением времени: touch -r оригинал исправленный

📁 ПОДДЕРЖИВАЕМЫЕ ФОРМАТЫ:
- Файлы в архивах: "/path/to/archive.zip::file.fb2"
- Обычные файлы: "/path/to/file.fb2"

🔧 ЭТАПЫ РАБОТЫ:
1. Pipeline копирует проблемный файл в tmp/llm_XXXXX_file.fb2
2. Анализирует XML ошибки и создает промпт tmp/llm_XXXXX_prompt.json
3. В итеративном режиме: автоматически применяет исправления и проверяет
4. В обычном режиме: ТЫ применяешь bash команды к файлу tmp/llm_XXXXX_file.fb2
5. ТЫ проверяешь результат: python tools/run_diagnostic_tool.py --input файл
6. ТЫ заменяешь оригинал: touch -r оригинал исправленный

⚠️ ВАЖНО: В итеративном режиме pipeline автоматически применяет и проверяет исправления!
"""

import argparse
import hashlib
import json
import re
import shutil
import subprocess
import xml.etree.ElementTree as ET
import zipfile
from pathlib import Path
from typing import Any

# 🎯 НАСТРОЙКИ - SOURCE_PATH теперь передается через --path параметр

# 📝 ПРИМЕР JSON ОТВЕТА ОТ LLM (для справки):
EXAMPLE_LLM_JSON = """
{
  "bash_commands": [
    "sed -i '37s|.*|<p><emphasis>исправленная строка</emphasis></p>|' tmp/llm_XXXXX_file.fb2",
    "sed -i '38s|.*|<p>другая исправленная строка</p>|' tmp/llm_XXXXX_file.fb2"
  ],
  "explanation": "краткое объяснение что было исправлено",
  "validation": "После применения команд запустите: python tools/run_diagnostic_tool.py --input tmp/llm_XXXXX_file.fb2"
}
"""

# 🔧 BASH КОМАНДЫ ДЛЯ ИСПРАВЛЕНИЯ XML:
# sed -i 'Ns|.*|новое_содержимое|' файл    - заменить строку N
# sed -i 'Na\новая_строка' файл           - вставить после строки N
# sed -i 'Nd' файл                        - удалить строку N
# touch -r оригинал исправленный          - сохранить время файла

# 📁 ЗАМЕНА ОРИГИНАЛА С СОХРАНЕНИЕМ ДАТЫ:
# Для файлов в архивах:
#   1. Извлечь: unzip -j архив.zip файл.fb2 -d tmp/
#   2. Исправить временный файл bash командами
#   3. Заменить в архиве: zip -u архив.zip -j tmp/файл.fb2
#   4. Восстановить дату архива: touch -r backup.zip архив.zip
# Для обычных файлов:
#   1. Создать backup: cp оригинал оригинал.backup
#   2. Исправить временный файл bash командами
#   3. Заменить: cp tmp/исправленный оригинал
#   4. Восстановить дату: touch -r оригинал.backup оригинал


class IterationContext:
    """Контекст для итеративного исправления XML файлов"""

    def __init__(self):
        self.iteration_number = 0
        self.applied_changes = []  # Список примененных изменений
        self.remaining_errors = []  # Оставшиеся ошибки
        self.diagnostic_results = []  # Результаты диагностических проверок
        self.xml_validity_history = []  # История валидности XML

    def add_iteration(self, changes, xml_valid, diagnostic_result, remaining_errors):
        """Добавляет информацию об итерации"""
        self.iteration_number += 1
        self.applied_changes.append(
            {
                "iteration": self.iteration_number,
                "changes": changes,
                "xml_valid": xml_valid,
                "diagnostic_success": diagnostic_result.get("success", False) if diagnostic_result else False,
                "chapters_count": diagnostic_result.get("chapters_count", 0) if diagnostic_result else 0,
                "anomalies": diagnostic_result.get("anomalies", []) if diagnostic_result else [],
            }
        )
        self.xml_validity_history.append(xml_valid)
        self.remaining_errors = remaining_errors

    def get_summary(self):
        """Возвращает краткую сводку по итерациям"""
        return {
            "total_iterations": self.iteration_number,
            "xml_validity_progress": self.xml_validity_history,
            "last_diagnostic": self.applied_changes[-1] if self.applied_changes else None,
            "remaining_errors": self.remaining_errors,
        }


class LLMXMLPipeline:
    def __init__(self, work_dir="tmp"):
        self.work_dir = Path(work_dir)
        self.work_dir.mkdir(exist_ok=True)

    def step1_extract_to_tmp(self, source_path):
        """Шаг 1: Копируем проблемный файл в tmp/"""

        # Генерируем понятный ID для файла
        file_id = hashlib.md5(source_path.encode(), usedforsecurity=False).hexdigest()[:8]

        if "::" in source_path:
            # Файл в архиве
            archive_path, file_in_archive = source_path.split("::")
            tmp_file = self.work_dir / f"llm_{file_id}_{file_in_archive}"

            # Проверяем, существует ли уже временный файл (не перезаписываем изменения)
            if not tmp_file.exists():
                with zipfile.ZipFile(archive_path, "r") as zip_file:
                    with zip_file.open(file_in_archive) as source:
                        with open(tmp_file, "wb") as target:
                            target.write(source.read())
                print(f"📁 Файл извлечен из архива: {tmp_file}")
            else:
                print(f"📁 Используется существующий временный файл: {tmp_file}")
        else:
            # Обычный файл
            source_file = Path(source_path)
            tmp_file = self.work_dir / f"llm_{file_id}_{source_file.name}"

            # Проверяем, существует ли уже временный файл (не перезаписываем изменения)
            if not tmp_file.exists():
                shutil.copy2(source_file, tmp_file)
                print(f"📁 Файл скопирован: {tmp_file}")
            else:
                print(f"📁 Используется существующий временный файл: {tmp_file}")

        return tmp_file, file_id

    def step2_analyze_and_create_prompt(self, tmp_file, file_id, source_path=None):
        """Шаг 2: Анализ и создание промпта для LLM"""

        # Читаем файл
        with open(tmp_file, "r", encoding="utf-8") as f:
            lines = f.readlines()

        # Анализируем XML
        error_str = ""
        try:
            ET.parse(tmp_file)  # nosec B314 - инструментальный код для анализа локальных файлов
            print("✅ XML валидный - исправление не требуется")
            print(f"Проверить: python tools/run_diagnostic_tool.py --input {tmp_file}")
            print(f"ЕСЛИ глав больше 1, запустить: python tools/llm_xml_update_orig.py {file_id}")
            if source_path:
                print(f"Проверить оригинал: python tools/run_diagnostic_tool.py --input {source_path}")
            return None, None
        except ET.ParseError as e:
            error_str = str(e)

        # Извлекаем номер строки из ошибки
        error_line = None
        if "line" in error_str:
            line_match = re.search(r"line (\d+)", error_str)
            if line_match:
                error_line = int(line_match.group(1))

        # Извлекаем ID файла из имени tmp файла для промпта
        archive_file_id = tmp_file.stem.split("_")[-1]  # llm_hash_8357 -> 8357

        # Создаем промпт
        prompt_data = {
            "task": "Исправить сломанный XML в FB2 файле",
            "file_info": {
                "lines_total": len(lines),
                "size_chars": sum(len(line) for line in lines),
                "error": error_str,
                "error_line": error_line,
            },
            "problem_area": self._extract_problem_area(lines, error_line),
            "requirements": {
                "format": "JSON",
                "fields": {
                    "correct_solution": "исправленные строки с правильным XML (включая номера строк)",
                    "explanation": "краткое объяснение проблемы и решения",
                    "changes": ["список конкретных изменений"],
                },
            },
            "instruction": "Ответь ТОЛЬКО в формате JSON, без дополнительных комментариев!",
        }

        # Сохраняем промпт с ID файла в имени
        prompt_file = self.work_dir / f"llm_{file_id}_{archive_file_id}.json"
        with open(prompt_file, "w", encoding="utf-8") as f:
            json.dump(prompt_data, f, ensure_ascii=False, indent=2)

        return prompt_file, error_line

    def _extract_problem_area(self, lines, error_line):
        """Извлекает проблемную область вокруг ошибки"""
        if not error_line:
            # Если нет XML ошибки, ищем секцию body для анализа структуры
            return self._extract_body_structure(lines)

        start = max(0, error_line - 6)
        end = min(len(lines), error_line + 5)

        problem_lines = []
        for i in range(start, end):
            marker = ">>> " if i == error_line - 1 else "    "
            problem_lines.append(f"{marker}{i + 1:3d}: {lines[i].rstrip()}")

        return "\n".join(problem_lines)

    def _extract_body_structure(self, lines):
        """Извлекает структуру body для анализа отсутствующих глав"""
        body_start = None
        body_end = None
        description_start = None
        description_end = None

        # Ищем секции body и description
        for i, line in enumerate(lines):
            if "<description>" in line or "<description " in line:
                description_start = i
            elif "</description>" in line:
                description_end = i
            elif "<body>" in line or "<body " in line:
                body_start = i
            elif "</body>" in line and body_start is not None:
                body_end = i
                break

        # Проверяем, находится ли body внутри description
        if (
            body_start is not None
            and description_start is not None
            and description_end is not None
            and body_start > description_start
            and body_end is not None
            and body_end < description_end
        ):
            return "❌ ПРОБЛЕМА: Секция <body> находится внутри <description>. Нужно переместить </description> перед <body>."

        if body_start is None:
            return "❌ ПРОБЛЕМА: Не найдена секция <body> в файле. FB2 файл должен содержать секцию <body> с главами."

        if body_end is None:
            body_end = min(len(lines), body_start + 20)  # Показываем первые 20 строк body

        # Извлекаем содержимое body
        start = max(0, body_start)
        end = min(len(lines), body_end + 1)

        problem_lines = [
            "❌ ПРОБЛЕМА: Файл не содержит глав. FB2 файл должен иметь структуру с секциями <section> внутри <body>.",
            "",
            "📋 ТЕКУЩАЯ СТРУКТУРА <body>:",
        ]

        for i in range(start, end):
            marker = ">>> " if i == body_start else "    "
            problem_lines.append(f"{marker}{i + 1:3d}: {lines[i].rstrip()}")

        if body_end < len(lines) - 1:
            problem_lines.append("    ... (остальная часть файла)")

        problem_lines.extend(
            ["", "💡 НУЖНО ДОБАВИТЬ: Секции <section> с заголовками <title> и содержимым глав внутри <body>."]
        )

        return "\n".join(problem_lines)

    def _check_original_xml_validity(self, source_path):
        """Проверяет валидность оригинального файла без создания временного"""
        try:
            if "::" in str(source_path):
                # Файл в архиве - извлекаем во временный буфер для проверки
                archive_path, file_in_archive = str(source_path).split("::", 1)
                with zipfile.ZipFile(archive_path, "r") as zip_ref:
                    with zip_ref.open(file_in_archive) as file_in_zip:
                        content = file_in_zip.read()
                        # Проверяем XML валидность
                        ET.fromstring(content)  # nosec B314 - инструментальный код
                        return True
            else:
                # Обычный файл
                ET.parse(source_path)  # nosec B314 - инструментальный код
                return True
        except (ET.ParseError, zipfile.BadZipFile, KeyError):
            return False
        except Exception:
            return False

    def _extract_file_id(self, source_path):
        """Извлекает ID файла для именования временного файла"""
        return hashlib.md5(str(source_path).encode(), usedforsecurity=False).hexdigest()[:8]

    def _extract_archive_file_id(self, source_path):
        """Извлекает имя файла из архива для именования временного файла"""
        if "::" in str(source_path):
            _, file_in_archive = str(source_path).split("::", 1)
            return file_in_archive
        else:
            return Path(source_path).name

    def step3_apply_llm_response(self, tmp_file, file_id, llm_json_response):
        """Шаг 3: Применяем JSON ответ от LLM"""
        print("🤖 Шаг 3: Применение решения от LLM")

        try:
            llm_data = json.loads(llm_json_response)
        except json.JSONDecodeError as e:
            print(f"❌ Ошибка парсинга JSON: {e}")
            return None

        print(f"📝 LLM объяснение: {llm_data.get('explanation', 'Не указано')}")

        # Применяем исправления из correct_solution
        if "correct_solution" in llm_data:
            return self._apply_correct_solution(tmp_file, file_id, llm_data["correct_solution"])
        else:
            print("❌ В JSON ответе отсутствует поле 'correct_solution'")
            return None

    def _apply_correct_solution(self, tmp_file, file_id, correct_solution):
        """Применяет исправленное решение к файлу"""
        try:
            # Читаем оригинальный файл
            with open(tmp_file, "r", encoding="utf-8") as f:
                original_lines = f.readlines()

            # Создаем исправленный файл
            fixed_file = self.work_dir / f"llm_{file_id}_fixed.fb2"

            # Если correct_solution содержит номера строк, извлекаем только содержимое
            if ":" in correct_solution and any(
                line.strip().split(":")[0].strip().isdigit() for line in correct_solution.split("\n") if ":" in line
            ):
                # Это фрагмент с номерами строк - извлекаем содержимое
                corrected_lines = []
                for line in correct_solution.split("\n"):
                    if ":" in line and line.strip():
                        # Проверяем, начинается ли строка с номера
                        parts = line.split(":", 1)
                        if len(parts) > 1 and parts[0].strip().isdigit():
                            content = parts[1]
                            if content:
                                corrected_lines.append(content + "\n")
                        else:
                            # Строка без номера - добавляем как есть
                            corrected_lines.append(line + "\n")
                    elif line.strip():
                        # Строка без двоеточия - добавляем как есть
                        corrected_lines.append(line + "\n")

                # Применяем исправления к проблемной области
                # Находим строки для замены (примерно в районе ошибки)
                error_line = self._find_error_line(tmp_file)
                if error_line and corrected_lines:
                    start_line = max(0, error_line - 6)
                    end_line = min(len(original_lines), error_line + 5)

                    # Заменяем проблемную область
                    new_lines = original_lines[:start_line] + corrected_lines + original_lines[end_line:]

                    with open(fixed_file, "w", encoding="utf-8") as f:
                        f.writelines(new_lines)
                else:
                    # Если не удалось определить область, просто копируем оригинал
                    with open(fixed_file, "w", encoding="utf-8") as f:
                        f.writelines(original_lines)
            else:
                # Это исправленная строка или фрагмент - заменяем проблемную строку
                error_line = self._find_error_line(tmp_file)
                if error_line:
                    # Заменяем конкретную строку с ошибкой
                    new_lines = original_lines.copy()
                    new_lines[error_line - 1] = correct_solution + "\n"

                    with open(fixed_file, "w", encoding="utf-8") as f:
                        f.writelines(new_lines)
                else:
                    # Если не удалось найти ошибку, записываем как полный файл
                    with open(fixed_file, "w", encoding="utf-8") as f:
                        f.write(correct_solution)

            print(f"🔧 Исправленный файл создан: {fixed_file}")
            return fixed_file

        except Exception as e:
            print(f"❌ Ошибка применения исправлений: {e}")
            return None

    def _find_error_line(self, tmp_file):
        """Находит номер строки с ошибкой"""
        try:
            ET.parse(tmp_file)  # nosec B314 - инструментальный код для анализа локальных файлов
            return None
        except ET.ParseError as e:
            error_str = str(e)
            if "line" in error_str:
                line_match = re.search(r"line (\d+)", error_str)
                if line_match:
                    return int(line_match.group(1))
        return None

    def validate_fix_automatically(self, tmp_file) -> dict[str, Any]:
        """Автоматическая проверка исправления"""
        results: dict[str, Any] = {
            "xml_valid": False,
            "chapters_count": 0,
            "anomalies": [],
            "diagnostic_success": False,
            "errors": [],
        }

        try:
            # 1. Проверка XML валидности
            xml_result = subprocess.run(
                ["xmllint", "--noout", str(tmp_file)], capture_output=True, text=True, timeout=30
            )
            results["xml_valid"] = xml_result.returncode == 0

            if not results["xml_valid"]:
                results["errors"].append(f"XML невалидный: {xml_result.stderr}")
                return results

            # 2. Диагностическая проверка
            diagnostic_result = subprocess.run(
                ["python", "tools/run_diagnostic_tool.py", "--input", str(tmp_file)],
                capture_output=True,
                text=True,
                timeout=60,
            )

            results["diagnostic_success"] = diagnostic_result.returncode == 0

            # Парсинг вывода для извлечения информации
            if results["diagnostic_success"]:
                output = diagnostic_result.stdout
                # Ищем количество глав в выводе
                chapter_match = re.search(r"Главы:\s*(\d+)", output)
                if chapter_match:
                    results["chapters_count"] = int(chapter_match.group(1))

                # Ищем аномалии
                if "аномалий не обнаружено" not in output.lower():
                    anomaly_match = re.search(r"аномалии:\s*(.+)", output, re.IGNORECASE)
                    if anomaly_match:
                        results["anomalies"] = [a.strip() for a in anomaly_match.group(1).split(",")]
            else:
                results["errors"].append(f"Диагностика неуспешна: {diagnostic_result.stderr}")

        except subprocess.TimeoutExpired:
            results["errors"].append("Таймаут при проверке")
        except Exception as e:
            results["errors"].append(f"Ошибка проверки: {e}")

        return results

    def is_fix_successful(self, validation_results):
        """Определяет успешность исправления"""
        return (
            validation_results["xml_valid"]
            and validation_results["diagnostic_success"]
            and validation_results["chapters_count"] >= 1
            and len(validation_results["anomalies"]) == 0
        )

    def create_iterative_prompt(self, tmp_file, file_id, context: IterationContext):
        """Создает промпт с учетом предыдущих итераций"""

        # Читаем файл
        with open(tmp_file, "r", encoding="utf-8") as f:
            lines = f.readlines()

        # Анализируем XML
        error_str = ""
        try:
            ET.parse(tmp_file)  # nosec B314 - инструментальный код для анализа локальных файлов
            print("✅ XML валидный - исправление не требуется")
            print(f"Проверить: python tools/run_diagnostic_tool.py --input {tmp_file}")
            print(f"ЕСЛИ глав больше 1, запустить: python tools/llm_xml_update_orig.py {file_id}")
            return None, None
        except ET.ParseError as e:
            error_str = str(e)

        # Извлекаем номер строки из ошибки
        error_line = None
        if "line" in error_str:
            line_match = re.search(r"line (\d+)", error_str)
            if line_match:
                error_line = int(line_match.group(1))

        # Извлекаем ID файла из имени tmp файла для промпта
        archive_file_id = tmp_file.stem.split("_")[-1]  # llm_hash_8357 -> 8357

        # Создаем базовый промпт
        prompt_data = {
            "task": "Исправить сломанный XML в FB2 файле",
            "file_info": {
                "lines_total": len(lines),
                "size_chars": sum(len(line) for line in lines),
                "error": error_str,
                "error_line": error_line,
            },
            "problem_area": self._extract_problem_area(lines, error_line),
            "requirements": {
                "format": "JSON",
                "fields": {
                    "correct_solution": "исправленные строки с правильным XML (включая номера строк)",
                    "explanation": "краткое объяснение проблемы и решения",
                    "changes": ["список конкретных изменений"],
                },
            },
            "instruction": "Ответь ТОЛЬКО в формате JSON, без дополнительных комментариев!",
        }

        # Добавляем контекст итераций если есть
        if context.iteration_number > 0:
            prompt_data["iteration_context"] = {
                "iteration_number": context.iteration_number + 1,
                "previous_attempts": context.applied_changes,
                "remaining_issues": context.remaining_errors,
                "instruction_update": "Учти предыдущие исправления и сосредоточься на оставшихся проблемах. Предыдущие попытки не решили проблему полностью.",
            }

        # Сохраняем промпт с ID файла в имени
        iteration_suffix = f"_iter{context.iteration_number + 1}" if context.iteration_number > 0 else ""
        prompt_file = self.work_dir / f"llm_{file_id}_{archive_file_id}{iteration_suffix}.json"
        with open(prompt_file, "w", encoding="utf-8") as f:
            json.dump(prompt_data, f, ensure_ascii=False, indent=2)

        return prompt_file, error_line

    def step4_verify_fix(self, fixed_file):
        """Шаг 4: Проверяем результат исправления"""
        print("✅ Шаг 4: Проверка результата")

        try:
            ET.parse(fixed_file)  # nosec B314 - инструментальный код для валидации локальных файлов
            print("✅ Исправленный XML валидный!")
            return True
        except ET.ParseError as e:
            print(f"❌ Исправленный XML все еще содержит ошибки: {e}")
            return False

    def step5_replace_original(self, source_path, fixed_file):
        """Шаг 5: Заменяем содержимое в оригинале"""
        print("🔄 Шаг 5: Замена содержимого в оригинале")

        if "::" in source_path:
            # Файл в архиве
            archive_path, file_in_archive = source_path.split("::")
            return self._replace_in_archive(archive_path, file_in_archive, fixed_file)
        else:
            # Обычный файл
            shutil.copy2(fixed_file, source_path)
            print(f"✅ Файл заменен: {source_path}")
            return True

    def _replace_in_archive(self, archive_path, file_in_archive, fixed_file):
        """Заменяет файл в архиве"""
        # TODO: Реализовать замену в архиве
        print("⚠️ Замена в архиве пока не реализована")
        return False

    def run_pipeline(self, source_path, llm_json_response=None):
        """Запускает полный pipeline"""

        try:
            # Шаг 0: Проверяем оригинал ДО создания временного файла
            file_id = self._extract_file_id(source_path)
            tmp_file = Path(f"tmp/llm_{file_id}_{self._extract_archive_file_id(source_path)}.fb2")

            # Если временный файл уже существует, работаем с ним
            if tmp_file.exists():
                print(f"📁 Используется существующий временный файл: {tmp_file}")
            else:
                # Проверяем оригинал на XML валидность
                if self._check_original_xml_validity(source_path):
                    print("✅ Оригинальный файл уже валидный - исправление не требуется")
                    print(f"Проверить: python tools/run_diagnostic_tool.py --input {source_path}")
                    return True

                # Оригинал невалидный, создаем временный файл
                print("⚠️ Оригинал невалидный, создаем временный файл")
                tmp_file, file_id = self.step1_extract_to_tmp(source_path)

            # Шаг 2: Анализ и промпт
            prompt_file, error_line = self.step2_analyze_and_create_prompt(tmp_file, file_id, source_path)

            if not prompt_file:
                return True

            if not llm_json_response:
                return False

            # Шаг 3: Применение LLM решения
            fixed_file = self.step3_apply_llm_response(tmp_file, file_id, llm_json_response)

            if not fixed_file:
                print("❌ Не удалось применить решение LLM")
                return False

            # Шаг 4: Проверка
            if not self.step4_verify_fix(fixed_file):
                print("❌ Исправление не прошло проверку")
                return False

            # Шаг 5: Замена оригинала
            if self.step5_replace_original(source_path, fixed_file):
                print("🎉 Pipeline успешно завершен!")
                return True
            else:
                print("❌ Не удалось заменить оригинал")
                return False

        except Exception as e:
            print(f"❌ Ошибка в pipeline: {e}")
            return False

    def run_iterative_pipeline(self, source_path, max_iterations=3):
        """Запускает итеративный pipeline с автоматической проверкой"""

        print(f"🔄 Запуск итеративного pipeline (макс. {max_iterations} итераций)")
        context = IterationContext()

        try:
            # Шаг 1: Извлечение
            tmp_file, file_id = self.step1_extract_to_tmp(source_path)
            print(f"📁 Временный файл: {tmp_file}")

            for iteration in range(max_iterations):
                print(f"\n🔄 === ИТЕРАЦИЯ {iteration + 1} ===")

                # Создаем промпт с учетом контекста
                prompt_file, error_line = self.create_iterative_prompt(tmp_file, file_id, context)

                if not prompt_file:
                    print("✅ XML уже валидный, исправление не требуется")
                    return True

                print(f"📝 Промпт создан: {prompt_file}")
                print("⏳ Ожидание JSON ответа от LLM...")
                print("💡 Отправьте содержимое промпта в LLM и введите JSON ответ:")

                # Ожидаем ввод JSON от пользователя
                print("\n" + "=" * 50)
                print("ВВЕДИТЕ JSON ОТВЕТ ОТ LLM (завершите пустой строкой):")
                print("=" * 50)

                json_lines = []
                while True:
                    line = input()
                    if line.strip() == "":
                        break
                    json_lines.append(line)

                llm_json_response = "\n".join(json_lines)

                if not llm_json_response.strip():
                    print("❌ Пустой ответ, прерываем итерацию")
                    break

                # Применяем исправления
                fixed_file = self.step3_apply_llm_response(tmp_file, file_id, llm_json_response)

                if not fixed_file:
                    print("❌ Не удалось применить исправления")
                    continue

                # Заменяем временный файл исправленным для следующей итерации
                shutil.copy2(fixed_file, tmp_file)

                # Автоматическая проверка
                print("🔍 Автоматическая проверка результата...")
                validation_results = self.validate_fix_automatically(tmp_file)

                # Извлекаем информацию об изменениях из JSON
                try:
                    llm_data = json.loads(llm_json_response)
                    changes = llm_data.get("changes", ["Изменения не указаны"])
                except json.JSONDecodeError:
                    changes = ["Ошибка парсинга изменений"]

                # Обновляем контекст
                context.add_iteration(
                    changes=changes,
                    xml_valid=validation_results["xml_valid"],
                    diagnostic_result=validation_results,
                    remaining_errors=validation_results.get("errors", []),
                )

                # Выводим результаты проверки
                print(f"📊 XML валидный: {'✅' if validation_results['xml_valid'] else '❌'}")
                print(f"📊 Диагностика успешна: {'✅' if validation_results['diagnostic_success'] else '❌'}")
                print(f"📊 Количество глав: {validation_results['chapters_count']}")
                print(f"📊 Аномалии: {validation_results['anomalies'] if validation_results['anomalies'] else 'Нет'}")

                if validation_results["errors"]:
                    print(f"⚠️ Ошибки: {'; '.join(validation_results['errors'])}")

                # Проверяем успех
                if self.is_fix_successful(validation_results):
                    print(f"🎉 Успех на итерации {iteration + 1}!")
                    print(f"✅ Файл успешно исправлен: {tmp_file}")

                    # Предлагаем обновить оригинал
                    if validation_results["chapters_count"] > 1:
                        print(f"💡 Рекомендация: запустите python tools/llm_xml_update_orig.py {file_id}")

                    return True
                else:
                    print(f"⚠️ Итерация {iteration + 1} не решила все проблемы")
                    if iteration < max_iterations - 1:
                        print("🔄 Переходим к следующей итерации...")

            # Если достигли лимита итераций
            print(f"\n❌ Достигнут лимит итераций ({max_iterations})")
            print("📊 Сводка по итерациям:")
            summary = context.get_summary()
            for i, attempt in enumerate(summary.get("xml_validity_progress", []), 1):
                print(f"  Итерация {i}: XML валидный = {'✅' if attempt else '❌'}")

            print("\n💡 Рекомендации:")
            print("1. Проанализируйте оставшиеся ошибки")
            print("2. Попробуйте ручное исправление")
            print("3. Обратитесь к эксперту по XML")

            return False

        except Exception as e:
            print(f"❌ Ошибка в итеративном pipeline: {e}")
            return False


def quick_fix_with_json(source_path, llm_json_response):
    """
    🚀 Быстрое исправление файла с готовым JSON ответом от LLM

    Args:
        source_path: Путь к файлу (может быть в архиве)
        llm_json_response: JSON строка с ответом от LLM

    Returns:
        bool: True если успешно исправлено
    """
    pipeline = LLMXMLPipeline()
    return pipeline.run_pipeline(source_path, llm_json_response)


def main():
    """Главная функция с поддержкой аргументов командной строки"""
    parser = argparse.ArgumentParser(
        description="LLM XML Pipeline - Инструмент для исправления сломанных XML файлов",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Примеры использования:
  %(prog)s                           # Обычный режим
  %(prog)s --iterative               # Итеративный режим (рекомендуется)
  %(prog)s --iterative --max-iterations 5  # Итеративный режим с 5 попытками
        """,
    )

    parser.add_argument(
        "--iterative", action="store_true", help="Включить итеративный режим с автоматической проверкой"
    )

    parser.add_argument(
        "--max-iterations", type=int, default=3, help="Максимальное количество итераций (по умолчанию: 3)"
    )

    parser.add_argument(
        "--path",
        type=str,
        required=True,
        help="Путь к файлу для исправления (например: /path/to/archive.zip::file.fb2)",
    )

    args = parser.parse_args()

    pipeline = LLMXMLPipeline()

    if args.iterative:
        # Итеративный режим
        print("🔄 Запуск в итеративном режиме")
        success = pipeline.run_iterative_pipeline(args.path, args.max_iterations)
    else:
        # Обычный режим
        print("📋 Запуск в обычном режиме")
        success = pipeline.run_pipeline(args.path)

        if not success:
            # Показываем только конкретные файлы для работы
            prompt_files = list(Path("tmp").glob("llm_*_*.json"))
            fb2_files = list(Path("tmp").glob("llm_*.fb2"))

            if prompt_files:
                print(f"\n📝 Промпт создан: {prompt_files[-1]}")
            if fb2_files:
                print(f"📁 Файл для исправления: {fb2_files[-1]}")

            # Генерируем инструкцию прямо здесь
            if prompt_files and fb2_files:
                prompt_file = prompt_files[-1]
                fb2_file = fb2_files[-1]

                # Читаем информацию об ошибке
                try:
                    with open(prompt_file, "r", encoding="utf-8") as f:
                        prompt_data = json.load(f)
                    error_info = prompt_data.get("file_info", {}).get("error", "XML ошибка")
                    error_line = prompt_data.get("file_info", {}).get("error_line", "неизвестно")
                except Exception:
                    error_info = "XML ошибка"
                    error_line = "неизвестно"

                # Извлекаем file_id для дальнейших команд
                file_id = fb2_file.stem.split("_")[1]  # llm_XXXXX_8357.fb2 -> XXXXX
                archive_file_id = fb2_file.stem.split("_")[2]  # llm_XXXXX_8357.fb2 -> 8357
                source_path = args.path  # Используем путь из аргументов

                # Выводим ТОЛЬКО инструкцию
                print(f"""
# ЗАДАЧА: Примени bash исправления к {fb2_file}

## КОНТЕКСТ:
- **Проблемный файл:** `{fb2_file}`
- **Ошибка:** {error_info} (строка {error_line})

## ЧТО МНЕ ДЕЛАТЬ:
1. Применить bash команды из JSON (в конце сообщения) к `{fb2_file}`
2. Проверить валидность XML (оптимально: `xmllint --noout {fb2_file}`)
3. **ЕСЛИ XML НЕ ВАЛИДНЫЙ:** перезапустить `python tools/llm_xml_pipeline.py`, получить новый JSON и повторить пункты 1-2
4. **ПОВТОРЯТЬ пункты 1-3 до полной валидности XML** (xmllint выдает код возврата 0)
5. Проверить результат: python tools/run_diagnostic_tool.py --input {fb2_file}
6. Показать количество глав и отсутствие аномалий
7. ЕСЛИ глав больше 1, запустить: python tools/llm_xml_update_orig.py {file_id}
8. Проверить оригинал: python tools/run_diagnostic_tool.py --input {source_path}
9. После успешной проверки оригинала удалить временные файлы: rm tmp/llm_{file_id}_{archive_file_id}.*

---
JSON ОТ LLM ЗДЕСЬ ↓""")
            else:
                print("\n⚠️ Не удалось создать промпт или найти файлы для исправления")
                print(f"Проверьте путь к файлу: {args.path}")
        else:
            print("\n🎉 Файл не требует исправления!")


if __name__ == "__main__":
    main()
