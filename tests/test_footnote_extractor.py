#!/usr/bin/env python3
"""
Unit-тесты для FootnoteExtractor.

Проверяет функциональность извлечения сносок из FB2 файлов.
"""

import unittest
from lxml import etree as ET

from app.processing.parsers.fb2.footnote_extractor import FootnoteExtractor


class TestFootnoteExtractor(unittest.TestCase):
    """Тесты для FootnoteExtractor."""

    def setUp(self):
        """Настройка тестов."""
        self.extractor = FootnoteExtractor()

    def test_extract_standard_footnotes(self):
        """Тест извлечения стандартных сносок из <body name="notes">."""
        xml_str = '''
        <FictionBook>
            <body>
                <section>
                    <p>Main text with <a type="note" href="#note1">footnote</a></p>
                </section>
            </body>
            <body name="notes">
                <section id="note1">
                    <p>This is footnote 1</p>
                </section>
                <section id="note2">
                    <p>This is footnote 2</p>
                </section>
            </body>
        </FictionBook>
        '''
        
        root = ET.fromstring(xml_str)
        footnotes = self.extractor.extract(root)
        
        self.assertEqual(len(footnotes), 2)
        self.assertIn('note1', footnotes)
        self.assertIn('note2', footnotes)
        self.assertEqual(footnotes['note1'], 'This is footnote 1')
        self.assertEqual(footnotes['note2'], 'This is footnote 2')

    def test_extract_footnotes_from_paragraphs(self):
        """Тест извлечения сносок из параграфов с id."""
        xml_str = '''
        <FictionBook>
            <body>
                <section>
                    <p>Main text</p>
                </section>
            </body>
            <body name="notes">
                <section>
                    <p id="n1">1. First footnote text</p>
                    <p id="n2">2. Second footnote text</p>
                </section>
            </body>
        </FictionBook>
        '''
        
        root = ET.fromstring(xml_str)
        footnotes = self.extractor.extract(root)
        
        self.assertEqual(len(footnotes), 2)
        self.assertIn('n1', footnotes)
        self.assertIn('n2', footnotes)
        self.assertEqual(footnotes['n1'], 'First footnote text')
        self.assertEqual(footnotes['n2'], 'Second footnote text')

    def test_extract_footnotes_by_section_title(self):
        """Тест извлечения сносок по заголовку секции."""
        xml_str = '''
        <FictionBook>
            <body>
                <section>
                    <p>Main text</p>
                </section>
                <section>
                    <title><p>Примечания</p></title>
                    <section id="note1">
                        <p>Footnote by title</p>
                    </section>
                </section>
            </body>
        </FictionBook>
        '''
        
        root = ET.fromstring(xml_str)
        footnotes = self.extractor.extract(root)
        
        self.assertEqual(len(footnotes), 1)
        self.assertIn('note1', footnotes)
        self.assertEqual(footnotes['note1'], 'Footnote by title')

    def test_extract_footnotes_by_id_pattern(self):
        """Тест извлечения сносок по паттерну ID."""
        xml_str = '''
        <FictionBook>
            <body>
                <section>
                    <p>Main text</p>
                </section>
                <section id="n_1">
                    <p>Footnote with pattern n_1</p>
                </section>
                <section id="note_2">
                    <p>Footnote with pattern note_2</p>
                </section>
                <section id="fn_3">
                    <p>Footnote with pattern fn_3</p>
                </section>
                <section id="regular_section">
                    <p>Regular section, not a footnote</p>
                </section>
            </body>
        </FictionBook>
        '''
        
        root = ET.fromstring(xml_str)
        footnotes = self.extractor.extract(root)
        
        self.assertEqual(len(footnotes), 3)
        self.assertIn('n_1', footnotes)
        self.assertIn('note_2', footnotes)
        self.assertIn('fn_3', footnotes)
        self.assertNotIn('regular_section', footnotes)

    def test_extract_footnotes_by_paragraph_sequence(self):
        """Тест извлечения сносок как последовательности параграфов."""
        xml_str = '''
        <FictionBook>
            <body>
                <section>
                    <p>Main text</p>
                    <p><strong>notes</strong></p>
                    <p><strong>1</strong></p>
                    <p>First footnote text</p>
                    <p><strong>2</strong></p>
                    <p>Second footnote text</p>
                    <p>More text for second footnote</p>
                </section>
            </body>
        </FictionBook>
        '''
        
        root = ET.fromstring(xml_str)
        footnotes = self.extractor.extract(root)
        
        self.assertEqual(len(footnotes), 2)
        self.assertIn('n_1', footnotes)
        self.assertIn('n_2', footnotes)
        self.assertEqual(footnotes['n_1'], 'First footnote text')
        self.assertEqual(footnotes['n_2'], 'Second footnote text More text for second footnote')

    def test_extract_no_footnotes(self):
        """Тест случая когда сносок нет."""
        xml_str = '''
        <FictionBook>
            <body>
                <section>
                    <p>Main text without footnotes</p>
                </section>
            </body>
        </FictionBook>
        '''
        
        root = ET.fromstring(xml_str)
        footnotes = self.extractor.extract(root)
        
        self.assertEqual(len(footnotes), 0)
        self.assertEqual(footnotes, {})

    def test_extract_complex_footnote_content(self):
        """Тест извлечения сносок со сложным содержимым."""
        xml_str = '''
        <FictionBook>
            <body name="notes">
                <section id="complex_note">
                    <p>First paragraph of footnote</p>
                    <p>Second paragraph with <strong>bold</strong> text</p>
                    <p>Third paragraph with <emphasis>italic</emphasis> text</p>
                </section>
            </body>
        </FictionBook>
        '''
        
        root = ET.fromstring(xml_str)
        footnotes = self.extractor.extract(root)
        
        self.assertEqual(len(footnotes), 1)
        self.assertIn('complex_note', footnotes)
        
        # Проверяем что весь текст извлечен
        footnote_text = footnotes['complex_note']
        self.assertIn('First paragraph', footnote_text)
        self.assertIn('Second paragraph', footnote_text)
        self.assertIn('bold', footnote_text)
        self.assertIn('Third paragraph', footnote_text)
        self.assertIn('italic', footnote_text)

    def test_extract_footnotes_priority(self):
        """Тест приоритета стратегий извлечения сносок."""
        # Если есть стандартные сноски, они должны иметь приоритет
        xml_str = '''
        <FictionBook>
            <body>
                <section id="n_1">
                    <p>Pattern-based footnote</p>
                </section>
            </body>
            <body name="notes">
                <section id="standard_note">
                    <p>Standard footnote</p>
                </section>
            </body>
        </FictionBook>
        '''
        
        root = ET.fromstring(xml_str)
        footnotes = self.extractor.extract(root)
        
        # Должна быть найдена только стандартная сноска (приоритет выше)
        self.assertEqual(len(footnotes), 1)
        self.assertIn('standard_note', footnotes)
        self.assertNotIn('n_1', footnotes)

    def test_get_full_text_content(self):
        """Тест извлечения полного текстового содержимого."""
        xml_str = '''
        <section>
            Text before
            <p>Paragraph text <strong>bold</strong> more text</p>
            Text after
        </section>
        '''
        
        element = ET.fromstring(xml_str)
        text = self.extractor._get_full_text_content(element)
        
        # Должен извлечь весь текст включая вложенные элементы
        self.assertIn('Text before', text)
        self.assertIn('Paragraph text', text)
        self.assertIn('bold', text)
        self.assertIn('more text', text)
        self.assertIn('Text after', text)

    def test_extract_footnotes_with_different_body_names(self):
        """Тест извлечения сносок из body с разными именами."""
        xml_str = '''
        <FictionBook>
            <body name="footnotes">
                <section id="footnote1">
                    <p>Footnote from footnotes body</p>
                </section>
            </body>
            <body name="notes">
                <section id="footnote2">
                    <p>Footnote from notes body</p>
                </section>
            </body>
        </FictionBook>
        '''
        
        root = ET.fromstring(xml_str)
        footnotes = self.extractor.extract(root)
        
        # Должны быть найдены сноски из обоих body
        self.assertEqual(len(footnotes), 2)
        self.assertIn('footnote1', footnotes)
        self.assertIn('footnote2', footnotes)


if __name__ == '__main__':
    unittest.main()
