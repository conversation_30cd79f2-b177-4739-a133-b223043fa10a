#!/usr/bin/env python3
"""
Unit-тесты для FB2Repairer.

Проверяет функциональность исправления поврежденного XML в FB2 файлах.
"""

import io
import unittest
from pathlib import Path

from app.processing.parsers.fb2.fb2_repairer import FB2Repairer


class TestFB2Repairer(unittest.TestCase):
    """Тесты для FB2Repairer."""

    def setUp(self):
        """Настройка тестов."""
        self.repairer = FB2Repairer()

    def test_repair_valid_xml(self):
        """Тест исправления валидного XML (должен остаться без изменений)."""
        valid_xml = b'<?xml version="1.0" encoding="utf-8"?><FictionBook><description><title-info><book-title>Test</book-title></title-info></description></FictionBook>'
        
        result = self.repairer.repair(io.BytesIO(valid_xml))
        
        # Результат должен быть похож на исходный XML
        self.assertIn(b'<FictionBook>', result)
        self.assertIn(b'<book-title>Test</book-title>', result)

    def test_repair_encoding_issues(self):
        """Тест исправления проблем с кодировкой."""
        # XML с кодировкой cp1251
        cp1251_xml = '<?xml version="1.0" encoding="cp1251"?><FictionBook><description><title-info><book-title>Тест</book-title></title-info></description></FictionBook>'.encode('cp1251')
        
        result = self.repairer.repair(io.BytesIO(cp1251_xml))
        
        # Результат должен быть в UTF-8
        self.assertIn('Тест'.encode('utf-8'), result)

    def test_repair_namespace_issues(self):
        """Тест исправления проблем с namespace."""
        xml_with_namespace_issue = b'''<?xml version="1.0" encoding="utf-8"?>
<FictionBook xmlns="http://www.gribuser.ru/xml/fictionbook/2.0">
    <description>
        <title-info>
            <book-title>Test</book-title>
        </title-info>
    </description>
    <body>
        <section>
            <p><a l:href="#note1">Link</a></p>
        </section>
    </body>
</FictionBook>'''
        
        result = self.repairer.repair(io.BytesIO(xml_with_namespace_issue))
        
        # Должно добавить объявление namespace для l:
        result_str = result.decode('utf-8')
        self.assertIn('xmlns:l=', result_str)

    def test_repair_invalid_xml_chars(self):
        """Тест исправления невалидных символов в XML."""
        xml_with_invalid_chars = b'<?xml version="1.0" encoding="utf-8"?><FictionBook><description><title-info><book-title>Test\x00\x08</book-title></title-info></description></FictionBook>'
        
        result = self.repairer.repair(io.BytesIO(xml_with_invalid_chars))
        
        # Невалидные символы должны быть удалены
        result_str = result.decode('utf-8')
        self.assertNotIn('\x00', result_str)
        self.assertNotIn('\x08', result_str)
        self.assertIn('<book-title>Test</book-title>', result_str)

    def test_repair_unescaped_ampersands(self):
        """Тест исправления неэкранированных амперсандов."""
        xml_with_ampersands = b'<?xml version="1.0" encoding="utf-8"?><FictionBook><description><title-info><book-title>Tom & Jerry</book-title></title-info></description></FictionBook>'
        
        result = self.repairer.repair(io.BytesIO(xml_with_ampersands))
        
        # Амперсанды должны быть экранированы
        result_str = result.decode('utf-8')
        self.assertIn('Tom &amp; Jerry', result_str)

    def test_repair_paragraph_nesting(self):
        """Тест исправления неправильной вложенности параграфов."""
        xml_with_nested_p = b'''<?xml version="1.0" encoding="utf-8"?>
<FictionBook>
    <description><title-info><book-title>Test</book-title></title-info></description>
    <body>
        <section>
            </p><p>Content</p></p>
        </section>
    </body>
</FictionBook>'''
        
        result = self.repairer.repair(io.BytesIO(xml_with_nested_p))
        
        # Неправильная вложенность должна быть исправлена
        result_str = result.decode('utf-8')
        self.assertIn('<p>Content</p>', result_str)
        # Не должно быть лишних закрывающих тегов
        self.assertNotIn('</p></p>', result_str)

    def test_repair_empty_paragraphs(self):
        """Тест удаления пустых самозакрывающихся параграфов."""
        xml_with_empty_p = b'''<?xml version="1.0" encoding="utf-8"?>
<FictionBook>
    <description><title-info><book-title>Test</book-title></title-info></description>
    <body>
        <section>
            <p/>
            <p>Real content</p>
            <p />
        </section>
    </body>
</FictionBook>'''
        
        result = self.repairer.repair(io.BytesIO(xml_with_empty_p))
        
        # Пустые параграфы должны быть удалены
        result_str = result.decode('utf-8')
        self.assertNotIn('<p/>', result_str)
        self.assertNotIn('<p />', result_str)
        self.assertIn('<p>Real content</p>', result_str)

    def test_repair_structural_issues(self):
        """Тест исправления структурных проблем."""
        xml_with_extra_closing = b'''<?xml version="1.0" encoding="utf-8"?>
<FictionBook>
    <description><title-info><book-title>Test</book-title></title-info></description>
    <body>
        <section>
            <p>Content</p>
        </section>
    </body>
</FictionBook>
</FictionBook>'''
        
        result = self.repairer.repair(io.BytesIO(xml_with_extra_closing))
        
        # Лишние закрывающие теги должны быть удалены
        result_str = result.decode('utf-8')
        # Должен быть только один закрывающий тег FictionBook
        self.assertEqual(result_str.count('</FictionBook>'), 1)

    def test_xml_fixes_stats(self):
        """Тест сбора статистики исправлений."""
        xml_with_issues = b'''<?xml version="1.0" encoding="utf-8"?>
<FictionBook>
    <description><title-info><book-title>Test & More</book-title></title-info></description>
    <body>
        <section>
            <p/>
            <p>Content</p>
        </section>
    </body>
</FictionBook>'''
        
        self.repairer.repair(io.BytesIO(xml_with_issues))
        
        stats = self.repairer.get_xml_fixes_stats()
        
        # Должна быть статистика исправлений
        self.assertIsInstance(stats, dict)
        self.assertIn('total_fixes_applied', stats)
        self.assertIn('fixes_details', stats)

    def test_reset_stats(self):
        """Тест сброса статистики."""
        # Сначала применим исправления с более проблемным XML
        xml_with_issues = b'<FictionBook><description><title-info><book-title>Test & More</book-title></title-info></description><body><section><p/><p>Content</p></section></body></FictionBook>'
        self.repairer.repair(io.BytesIO(xml_with_issues))

        # Проверим что есть статистика
        stats_before = self.repairer.get_xml_fixes_stats()
        self.assertGreaterEqual(stats_before['total_fixes_applied'], 0)

        # Сбросим статистику
        self.repairer.reset_xml_fixes_stats()

        # Проверим что статистика сброшена
        stats_after = self.repairer.get_xml_fixes_stats()
        self.assertEqual(stats_after['total_fixes_applied'], 0)
        self.assertEqual(len(stats_after['fixes_details']), 0)


if __name__ == '__main__':
    unittest.main()
