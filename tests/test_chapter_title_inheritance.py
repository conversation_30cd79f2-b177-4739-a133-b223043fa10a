# tests/test_chapter_title_inheritance.py

"""
Тесты для новой логики наследования заголовков в ChapterStateMachine.

Проверяет корректность реализации логики "забываем заголовок после обнаружения следующего заголовка":
- Передача заголовка всем дочерним секциям без заголовков
- Прекращение передачи после обнаружения секции с собственным заголовком
- Обработка пустых секций
- Различные комбинации структур
"""

import unittest
from unittest.mock import Mock

from app.processing.parsers.fb2.chapter_state_machine import ChapterStateMachine
from app.processing.parsers.fb2.fb2_model import Section, Annotation, Paragraph


class TestChapterTitleInheritance(unittest.TestCase):
    """Тесты для логики наследования заголовков."""

    def setUp(self):
        """Настройка тестового окружения."""
        self.markdown_renderer = Mock()
        self.state_machine = ChapterStateMachine(
            markdown_renderer=self.markdown_renderer,
            strategy_mode="structural"
        )

    def _create_section_with_title(self, title_text: str) -> Section:
        """Создает секцию с заголовком."""
        title_annotation = Annotation(elements=[
            Paragraph(content=[title_text])
        ])
        return Section(title=title_annotation, content=[
            Paragraph(content=["Контент секции"])
        ])

    def _create_section_without_title(self) -> Section:
        """Создает секцию без заголовка."""
        return Section(title=None, content=[
            Paragraph(content=["Контент секции без заголовка"])
        ])

    def _create_empty_section(self) -> Section:
        """Создает пустую секцию."""
        return Section(title=None, content=[])

    def test_title_inheritance_basic_case(self):
        """Тест: Базовый случай - заголовок передается всем секциям без заголовков."""
        # Создаем родительскую секцию с заголовком
        parent_section = Section(
            title=Annotation(elements=[
                Paragraph(content=["Картер БРАУН"]),
                Paragraph(content=["СМЕРТЕЛЬНОЕ ПОСЛАНИЕ"])
            ]),
            content=[
                self._create_section_without_title(),  # Должна получить заголовок
                self._create_section_without_title(),  # Должна получить заголовок
                self._create_section_without_title(),  # Должна получить заголовок
            ]
        )

        # Обрабатываем секцию
        self.state_machine.process_element(parent_section)
        chapters = self.state_machine.finalize()

        # Проверяем результат
        self.assertEqual(len(chapters), 3)
        expected_title = "Картер БРАУН. СМЕРТЕЛЬНОЕ ПОСЛАНИЕ"
        for chapter in chapters:
            self.assertEqual(chapter.title, expected_title)

    def test_title_inheritance_stops_at_own_title(self):
        """Тест: Наследование прекращается при обнаружении собственного заголовка."""
        parent_section = Section(
            title=Annotation(elements=[
                Paragraph(content=["СМЕРТЕЛЬНОЕ ПОСЛАНИЕ"])
            ]),
            content=[
                self._create_section_without_title(),  # Должна получить "СМЕРТЕЛЬНОЕ ПОСЛАНИЕ"
                self._create_section_without_title(),  # Должна получить "СМЕРТЕЛЬНОЕ ПОСЛАНИЕ"
                self._create_section_with_title("Эпилог"),  # Использует свой заголовок
                self._create_section_without_title(),  # Должна получить "Глава" (наследование прекращено)
            ]
        )

        # Обрабатываем секцию
        self.state_machine.process_element(parent_section)
        chapters = self.state_machine.finalize()

        # Проверяем результат
        self.assertEqual(len(chapters), 4)
        self.assertEqual(chapters[0].title, "СМЕРТЕЛЬНОЕ ПОСЛАНИЕ")
        self.assertEqual(chapters[1].title, "СМЕРТЕЛЬНОЕ ПОСЛАНИЕ")
        self.assertEqual(chapters[2].title, "Эпилог")
        self.assertEqual(chapters[3].title, "Глава")  # Fallback после прекращения наследования

    def test_title_inheritance_skips_empty_sections(self):
        """Тест: Пустые секции не влияют на наследование заголовков."""
        parent_section = Section(
            title=Annotation(elements=[
                Paragraph(content=["СМЕРТЕЛЬНОЕ ПОСЛАНИЕ"])
            ]),
            content=[
                self._create_empty_section(),  # Пустая - пропускается
                self._create_section_without_title(),  # Должна получить заголовок
                self._create_empty_section(),  # Пустая - пропускается
                self._create_section_without_title(),  # Должна получить заголовок
            ]
        )

        # Обрабатываем секцию
        self.state_machine.process_element(parent_section)
        chapters = self.state_machine.finalize()

        # Проверяем результат (только секции с контентом)
        self.assertEqual(len(chapters), 2)
        for chapter in chapters:
            self.assertEqual(chapter.title, "СМЕРТЕЛЬНОЕ ПОСЛАНИЕ")

    def test_no_title_inheritance_when_parent_has_content(self):
        """Тест: Заголовок не передается если у родительской секции есть прямой контент."""
        parent_section = Section(
            title=Annotation(elements=[
                Paragraph(content=["СМЕРТЕЛЬНОЕ ПОСЛАНИЕ"])
            ]),
            content=[
                Paragraph(content=["Прямой контент родительской секции"]),  # Прямой контент
                self._create_section_without_title(),  # Не должна получить заголовок
            ]
        )

        # Обрабатываем секцию
        self.state_machine.process_element(parent_section)
        chapters = self.state_machine.finalize()

        # Проверяем результат
        self.assertEqual(len(chapters), 2)
        self.assertEqual(chapters[0].title, "СМЕРТЕЛЬНОЕ ПОСЛАНИЕ")  # Родительская секция
        self.assertEqual(chapters[1].title, "Глава")  # Дочерняя секция без наследования

    def test_title_inheritance_complex_structure(self):
        """Тест: Сложная структура с несколькими уровнями вложенности."""
        parent_section = Section(
            title=Annotation(elements=[
                Paragraph(content=["ОСНОВНОЙ ЗАГОЛОВОК"])
            ]),
            content=[
                self._create_section_without_title(),  # Получит "ОСНОВНОЙ ЗАГОЛОВОК"
                Section(  # Вложенная секция с заголовком
                    title=Annotation(elements=[Paragraph(content=["ПОДРАЗДЕЛ"])]),
                    content=[
                        self._create_section_without_title(),  # Получит "ПОДРАЗДЕЛ"
                        self._create_section_without_title(),  # Получит "ПОДРАЗДЕЛ"
                    ]
                ),
                self._create_section_without_title(),  # Получит "Глава" (наследование прекращено)
            ]
        )

        # Обрабатываем секцию
        self.state_machine.process_element(parent_section)
        chapters = self.state_machine.finalize()

        # Проверяем результат
        self.assertEqual(len(chapters), 4)
        self.assertEqual(chapters[0].title, "ОСНОВНОЙ ЗАГОЛОВОК")
        self.assertEqual(chapters[1].title, "ПОДРАЗДЕЛ")
        self.assertEqual(chapters[2].title, "ПОДРАЗДЕЛ")
        self.assertEqual(chapters[3].title, "Глава")


if __name__ == '__main__':
    unittest.main()
