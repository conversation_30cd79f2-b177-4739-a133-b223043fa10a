#!/usr/bin/env python3
"""
Интеграционные тесты для FB2Parser после рефакторинга.

Проверяет что FB2Parser корректно работает с новыми компонентами
FB2Repairer и FootnoteExtractor.
"""

import io
import unittest

from app.processing.parsers.fb2.fb2_parser import FB2Parser


class TestFB2ParserIntegration(unittest.TestCase):
    """Интеграционные тесты для FB2Parser."""

    def setUp(self):
        """Настройка тестов."""
        self.parser = FB2Parser()

    def test_parse_simple_fb2(self):
        """Тест парсинга простого FB2 файла."""
        fb2_content = '''<?xml version="1.0" encoding="utf-8"?>
<FictionBook xmlns="http://www.gribuser.ru/xml/fictionbook/2.0">
    <description>
        <title-info>
            <book-title>Test Book</book-title>
            <author>
                <first-name>Test</first-name>
                <last-name>Author</last-name>
            </author>
        </title-info>
    </description>
    <body>
        <section>
            <title><p>Chapter 1</p></title>
            <p>This is the first paragraph.</p>
            <p>This is the second paragraph.</p>
        </section>
    </body>
</FictionBook>'''.encode('utf-8')

        result = self.parser.parse(io.BytesIO(fb2_content))
        
        # Проверяем основную структуру
        self.assertIsNotNone(result)
        self.assertIsNotNone(result.description)
        self.assertIsNotNone(result.description.title_info)
        self.assertEqual(result.description.title_info.book_title, "Test Book")
        self.assertEqual(len(result.description.title_info.authors), 1)
        self.assertEqual(result.description.title_info.authors[0].first_name, "Test")
        self.assertEqual(result.description.title_info.authors[0].last_name, "Author")
        
        # Проверяем тело книги
        self.assertEqual(len(result.bodies), 1)
        body = result.bodies[0]
        self.assertEqual(len(body.sections), 1)
        section = body.sections[0]
        self.assertIsNotNone(section.title)

    def test_parse_fb2_with_footnotes(self):
        """Тест парсинга FB2 с сносками."""
        fb2_content = '''<?xml version="1.0" encoding="utf-8"?>
<FictionBook xmlns="http://www.gribuser.ru/xml/fictionbook/2.0">
    <description>
        <title-info>
            <book-title>Test Book with Footnotes</book-title>
        </title-info>
    </description>
    <body>
        <section>
            <p>Text with <a type="note" href="#note1">footnote</a></p>
        </section>
    </body>
    <body name="notes">
        <section id="note1">
            <p>This is a footnote</p>
        </section>
    </body>
</FictionBook>'''.encode('utf-8')

        result = self.parser.parse(io.BytesIO(fb2_content))
        
        # Проверяем что сноски извлечены
        self.assertIsNotNone(result.footnotes)
        self.assertEqual(len(result.footnotes), 1)
        self.assertIn('note1', result.footnotes)
        self.assertEqual(result.footnotes['note1'], 'This is a footnote')

    def test_parse_fb2_with_xml_issues(self):
        """Тест парсинга FB2 с проблемами XML."""
        fb2_content = '''<?xml version="1.0" encoding="utf-8"?>
<FictionBook xmlns="http://www.gribuser.ru/xml/fictionbook/2.0">
    <description>
        <title-info>
            <book-title>Test & Repair</book-title>
        </title-info>
    </description>
    <body>
        <section>
            <p/>
            <p>Valid paragraph</p>
            </p><p>Nested paragraph issue</p></p>
        </section>
    </body>
</FictionBook>'''.encode('utf-8')

        # Парсинг должен пройти успешно благодаря FB2Repairer
        result = self.parser.parse(io.BytesIO(fb2_content))
        
        self.assertIsNotNone(result)
        self.assertEqual(result.description.title_info.book_title, "Test & Repair")
        
        # Проверяем что статистика исправлений доступна
        stats = self.parser.get_xml_fixes_stats()
        self.assertIsInstance(stats, dict)
        self.assertIn('total_fixes_applied', stats)

    def test_parse_fb2_with_complex_formatting(self):
        """Тест парсинга FB2 со сложным форматированием."""
        fb2_content = '''<?xml version="1.0" encoding="utf-8"?>
<FictionBook xmlns="http://www.gribuser.ru/xml/fictionbook/2.0">
    <description>
        <title-info>
            <book-title>Complex Formatting</book-title>
        </title-info>
    </description>
    <body>
        <section>
            <p>Text with <strong>bold</strong> and <emphasis>italic</emphasis></p>
            <p><strong><emphasis>Bold italic</emphasis></strong> text</p>
            <p>Text with <a href="http://example.com">external link</a></p>
        </section>
    </body>
</FictionBook>'''.encode('utf-8')

        result = self.parser.parse(io.BytesIO(fb2_content))
        
        self.assertIsNotNone(result)
        self.assertEqual(len(result.bodies), 1)
        body = result.bodies[0]
        self.assertEqual(len(body.sections), 1)
        section = body.sections[0]
        
        # Проверяем что параграфы с форматированием обработаны
        self.assertGreater(len(section.content), 0)

    def test_xml_fixes_stats_integration(self):
        """Тест интеграции статистики исправлений XML."""
        # Сначала сбрасываем статистику
        self.parser.reset_xml_fixes_stats()
        stats_before = self.parser.get_xml_fixes_stats()
        self.assertEqual(stats_before['total_fixes_applied'], 0)
        
        # Парсим файл с проблемами
        fb2_content = '''<?xml version="1.0" encoding="utf-8"?>
<FictionBook>
    <description>
        <title-info>
            <book-title>Test & Issues</book-title>
        </title-info>
    </description>
    <body>
        <section>
            <p/>
            <p>Content</p>
        </section>
    </body>
</FictionBook>'''.encode('utf-8')

        self.parser.parse(io.BytesIO(fb2_content))
        
        # Проверяем что статистика обновилась
        stats_after = self.parser.get_xml_fixes_stats()
        self.assertGreaterEqual(stats_after['total_fixes_applied'], 0)

    def test_parse_empty_footnotes(self):
        """Тест парсинга FB2 без сносок."""
        fb2_content = '''<?xml version="1.0" encoding="utf-8"?>
<FictionBook xmlns="http://www.gribuser.ru/xml/fictionbook/2.0">
    <description>
        <title-info>
            <book-title>No Footnotes</book-title>
        </title-info>
    </description>
    <body>
        <section>
            <p>Simple text without footnotes</p>
        </section>
    </body>
</FictionBook>'''.encode('utf-8')

        result = self.parser.parse(io.BytesIO(fb2_content))
        
        # Сноски должны быть пустым словарем
        self.assertIsNotNone(result.footnotes)
        self.assertEqual(len(result.footnotes), 0)
        self.assertEqual(result.footnotes, {})

    def test_parse_multiple_bodies(self):
        """Тест парсинга FB2 с несколькими body (исключая notes)."""
        fb2_content = '''<?xml version="1.0" encoding="utf-8"?>
<FictionBook xmlns="http://www.gribuser.ru/xml/fictionbook/2.0">
    <description>
        <title-info>
            <book-title>Multiple Bodies</book-title>
        </title-info>
    </description>
    <body>
        <section>
            <p>First body content</p>
        </section>
    </body>
    <body name="notes">
        <section id="note1">
            <p>This should be extracted as footnote</p>
        </section>
    </body>
    <body>
        <section>
            <p>Second body content</p>
        </section>
    </body>
</FictionBook>'''.encode('utf-8')

        result = self.parser.parse(io.BytesIO(fb2_content))
        
        # Должно быть 2 body (notes исключается)
        self.assertEqual(len(result.bodies), 2)
        
        # Сноски должны быть извлечены
        self.assertEqual(len(result.footnotes), 1)
        self.assertIn('note1', result.footnotes)

    def test_component_integration(self):
        """Тест интеграции всех компонентов."""
        # FB2 с проблемами XML и сносками
        fb2_content = '''<?xml version="1.0" encoding="utf-8"?>
<FictionBook>
    <description>
        <title-info>
            <book-title>Integration Test & More</book-title>
        </title-info>
    </description>
    <body>
        <section>
            <p/>
            <p>Text with <a type="note" href="#note1">footnote</a></p>
            </p><p>Broken nesting</p></p>
        </section>
    </body>
    <body name="notes">
        <section id="note1">
            <p>Footnote text</p>
        </section>
    </body>
</FictionBook>'''.encode('utf-8')

        result = self.parser.parse(io.BytesIO(fb2_content))
        
        # Проверяем что все компоненты сработали
        self.assertIsNotNone(result)
        self.assertEqual(result.description.title_info.book_title, "Integration Test & More")
        
        # FB2Repairer должен был исправить XML
        stats = self.parser.get_xml_fixes_stats()
        self.assertGreaterEqual(stats['total_fixes_applied'], 0)
        
        # FootnoteExtractor должен был извлечь сноски
        self.assertEqual(len(result.footnotes), 1)
        self.assertIn('note1', result.footnotes)
        self.assertEqual(result.footnotes['note1'], 'Footnote text')


if __name__ == '__main__':
    unittest.main()
