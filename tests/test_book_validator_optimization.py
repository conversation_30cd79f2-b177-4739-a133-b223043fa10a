# tests/test_book_validator_optimization.py

"""
Регрессионные тесты для оптимизации производительности BookValidator.

КРИТИЧЕСКИ ВАЖНО: Эти тесты защищают от регрессий в оптимизации early exit.
Удаление этого файла может привести к незаметной деградации производительности.

Проверяет:
1. Консистентность результатов между validate() и get_first_blocking_anomaly()
2. Корректность early exit для каждого приоритета аномалий
3. Сохранение порядка приоритетов при множественных аномалиях
4. Фактическую экономию вычислений через моки

КОГДА ЗАПУСКАТЬ:
- При любых изменениях в детекторах аномалий
- При рефакторинге BookValidator или BookValidationContext
- При добавлении новых типов аномалий
- Перед релизами для проверки производительности

КОГДА МОЖНО УДАЛИТЬ:
- Если система аномалий будет полностью переписана
- Если появятся более комплексные интеграционные тесты производительности
- Через 1-2 года стабильной работы без регрессий (не раньше!)
"""

import unittest
from unittest.mock import Mock, patch

from app.processing.book_validator import BookValidator
from app.processing.canonical_model import CanonicalBook, CanonicalAuthor, CanonicalChapter
from app.processing.parsing_report import ParsingReport


class TestBookValidatorOptimization(unittest.TestCase):
    """Тесты оптимизации производительности BookValidator."""

    def setUp(self):
        """Настройка тестового окружения."""
        self.validator = BookValidator()

        # Создаем тестовую книгу с достаточным контентом для прохождения всех проверок
        # Учитываем текущие пороги: min_chapters=7, min_content_chars=50000
        long_content = "Это очень длинный тестовый контент главы. " * 500  # ~20000 символов на главу
        self.test_book = CanonicalBook(
            title="Тестовая книга",
            lang="ru",
            authors=[CanonicalAuthor(first_name="Тест", middle_name=None, last_name="Автор")],
            chapters=[
                CanonicalChapter(title="Глава 1", content_elements=[long_content]),
                CanonicalChapter(title="Глава 2", content_elements=[long_content]),
                CanonicalChapter(title="Глава 3", content_elements=[long_content]),
                CanonicalChapter(title="Глава 4", content_elements=[long_content]),
                CanonicalChapter(title="Глава 5", content_elements=[long_content]),
                CanonicalChapter(title="Глава 6", content_elements=[long_content]),
                CanonicalChapter(title="Глава 7", content_elements=[long_content]),
                CanonicalChapter(title="Глава 8", content_elements=[long_content]),
            ]
        )
        # Итого: 8 глав * 20000 символов = 160000 символов (> 50000, >= 7 глав)

    def test_no_anomalies_consistency(self):
        """Тест: Книга без аномалий - оба метода возвращают одинаковый результат."""
        # Полная валидация
        full_anomalies = self.validator.validate(self.test_book)
        
        # Оптимизированная валидация
        first_anomaly = self.validator.get_first_blocking_anomaly(self.test_book)
        
        # Проверяем консистентность
        self.assertEqual(len(full_anomalies), 0)
        self.assertIsNone(first_anomaly)

    def test_fragment_anomaly_early_exit(self):
        """Тест: Фрагмент (приоритет 1) - early exit работает корректно."""
        # Создаем книгу с маркером фрагмента в последней главе
        long_content = "Это очень длинный тестовый контент главы. " * 100  # ~4000 символов
        fragment_book = CanonicalBook(
            title="Фрагмент книги",
            lang="ru",
            authors=[CanonicalAuthor(first_name="Тест", middle_name=None, last_name="Автор")],
            chapters=[
                CanonicalChapter(title="Глава 1", content_elements=[long_content]),
                CanonicalChapter(title="Глава 2", content_elements=[long_content]),
                CanonicalChapter(title="Глава 3", content_elements=[long_content]),
                CanonicalChapter(title="Последняя", content_elements=["Конец ознакомительного фрагмента"]),
            ]
        )

        # Полная валидация
        full_anomalies = self.validator.validate(fragment_book)
        
        # Оптимизированная валидация
        first_anomaly = self.validator.get_first_blocking_anomaly(fragment_book)
        
        # Проверяем консистентность
        self.assertGreaterEqual(len(full_anomalies), 1)
        self.assertEqual(full_anomalies[0]["type"], "trial_fragments")
        self.assertIsNotNone(first_anomaly)
        self.assertEqual(first_anomaly["type"], "trial_fragments")
        self.assertEqual(first_anomaly["reason"], full_anomalies[0]["reason"])

    def test_small_content_anomaly_early_exit(self):
        """Тест: Малый контент (приоритет 2) - early exit работает корректно."""
        # Создаем книгу с недостаточным объемом контента
        small_content = "Мало"  # Очень мало символов
        small_book = CanonicalBook(
            title="Маленькая книга",
            lang="ru",
            authors=[CanonicalAuthor(first_name="Тест", middle_name=None, last_name="Автор")],
            chapters=[
                CanonicalChapter(title="Глава 1", content_elements=[small_content]),
                CanonicalChapter(title="Глава 2", content_elements=[small_content]),
                CanonicalChapter(title="Глава 3", content_elements=[small_content]),
                CanonicalChapter(title="Глава 4", content_elements=[small_content]),
            ]
        )

        # Полная валидация
        full_anomalies = self.validator.validate(small_book)
        
        # Оптимизированная валидация
        first_anomaly = self.validator.get_first_blocking_anomaly(small_book)
        
        # Проверяем консистентность
        self.assertGreaterEqual(len(full_anomalies), 1)
        self.assertEqual(full_anomalies[0]["type"], "small_content")
        self.assertIsNotNone(first_anomaly)
        self.assertEqual(first_anomaly["type"], "small_content")
        self.assertEqual(first_anomaly["reason"], full_anomalies[0]["reason"])

    def test_few_chapters_anomaly_early_exit(self):
        """Тест: Мало глав (приоритет 2) - early exit работает корректно."""
        # Создаем книгу с достаточным объемом контента (>50000), но малым количеством глав (<7)
        very_long_content = "Достаточно контента для прохождения проверки объема. " * 1000  # ~50000 символов
        few_chapters_book = CanonicalBook(
            title="Книга с малым количеством глав",
            lang="ru",
            authors=[CanonicalAuthor(first_name="Тест", middle_name=None, last_name="Автор")],
            chapters=[
                CanonicalChapter(title="Глава 1", content_elements=[very_long_content]),
                CanonicalChapter(title="Глава 2", content_elements=[very_long_content]),
                CanonicalChapter(title="Глава 3", content_elements=[very_long_content]),
            ]
        )
        # Итого: 3 главы (< 7) * 50000 символов = 150000 символов (> 50000)

        # Полная валидация
        full_anomalies = self.validator.validate(few_chapters_book)
        
        # Оптимизированная валидация
        first_anomaly = self.validator.get_first_blocking_anomaly(few_chapters_book)
        
        # Проверяем консистентность
        self.assertGreaterEqual(len(full_anomalies), 1)
        self.assertEqual(full_anomalies[0]["type"], "few_chapters")
        self.assertIsNotNone(first_anomaly)
        self.assertEqual(first_anomaly["type"], "few_chapters")
        self.assertEqual(first_anomaly["reason"], full_anomalies[0]["reason"])

    def test_anthology_anomaly_early_exit(self):
        """Тест: Антология (приоритет 3) - early exit работает корректно."""
        # Создаем книгу-антологию с достаточным объемом и количеством глав
        long_content = "Достаточно контента для прохождения проверок. " * 500  # ~20000 символов
        anthology_book = CanonicalBook(
            title="Сборник рассказов",  # Ключевое слово "рассказов"
            lang="ru",
            authors=[CanonicalAuthor(first_name="Тест", middle_name=None, last_name="Автор")],
            chapters=[
                CanonicalChapter(title="Глава 1", content_elements=[long_content]),
                CanonicalChapter(title="Глава 2", content_elements=[long_content]),
                CanonicalChapter(title="Глава 3", content_elements=[long_content]),
                CanonicalChapter(title="Глава 4", content_elements=[long_content]),
                CanonicalChapter(title="Глава 5", content_elements=[long_content]),
                CanonicalChapter(title="Глава 6", content_elements=[long_content]),
                CanonicalChapter(title="Глава 7", content_elements=[long_content]),
                CanonicalChapter(title="Глава 8", content_elements=[long_content]),
            ]
        )
        # Итого: 8 глав (>= 7) * 20000 символов = 160000 символов (> 50000)

        # Полная валидация
        full_anomalies = self.validator.validate(anthology_book)
        
        # Оптимизированная валидация
        first_anomaly = self.validator.get_first_blocking_anomaly(anthology_book)
        
        # Проверяем консистентность
        self.assertGreaterEqual(len(full_anomalies), 1)
        self.assertEqual(full_anomalies[0]["type"], "anthology_books")
        self.assertIsNotNone(first_anomaly)
        self.assertEqual(first_anomaly["type"], "anthology_books")
        self.assertEqual(first_anomaly["reason"], full_anomalies[0]["reason"])

    def test_broken_footnotes_anomaly_early_exit(self):
        """Тест: Сломанные сноски (приоритет 4) - early exit работает корректно."""
        # Создаем книгу без других аномалий (достаточный объем и количество глав)
        long_content = "Достаточно контента для прохождения проверок. " * 500  # ~20000 символов
        normal_book = CanonicalBook(
            title="Обычная книга",
            lang="ru",
            authors=[CanonicalAuthor(first_name="Тест", middle_name=None, last_name="Автор")],
            chapters=[
                CanonicalChapter(title="Глава 1", content_elements=[long_content]),
                CanonicalChapter(title="Глава 2", content_elements=[long_content]),
                CanonicalChapter(title="Глава 3", content_elements=[long_content]),
                CanonicalChapter(title="Глава 4", content_elements=[long_content]),
                CanonicalChapter(title="Глава 5", content_elements=[long_content]),
                CanonicalChapter(title="Глава 6", content_elements=[long_content]),
                CanonicalChapter(title="Глава 7", content_elements=[long_content]),
                CanonicalChapter(title="Глава 8", content_elements=[long_content]),
            ]
        )
        # Итого: 8 глав (>= 7) * 20000 символов = 160000 символов (> 50000)

        # Создаем parsing_report с сломанными сносками
        parsing_report = Mock(spec=ParsingReport)
        parsing_report.has_broken_footnotes.return_value = True
        parsing_report.broken_footnotes = ["note1", "note2"]

        # Полная валидация
        full_anomalies = self.validator.validate(normal_book, parsing_report)
        
        # Оптимизированная валидация
        first_anomaly = self.validator.get_first_blocking_anomaly(normal_book, parsing_report)
        
        # Проверяем консистентность
        self.assertGreaterEqual(len(full_anomalies), 1)
        self.assertEqual(full_anomalies[0]["type"], "broken_footnotes")
        self.assertIsNotNone(first_anomaly)
        self.assertEqual(first_anomaly["type"], "broken_footnotes")
        self.assertEqual(first_anomaly["reason"], full_anomalies[0]["reason"])

    def test_multiple_anomalies_priority_order(self):
        """Тест: Множественные аномалии - проверка приоритета."""
        # Создаем книгу с несколькими аномалиями
        small_content = "Мало"  # Малый контент (приоритет 2)
        multi_anomaly_book = CanonicalBook(
            title="Сборник рассказов",  # Антология (приоритет 3)
            lang="ru",
            authors=[CanonicalAuthor(first_name="Тест", middle_name=None, last_name="Автор")],
            chapters=[
                CanonicalChapter(title="Глава 1", content_elements=[small_content]),
                CanonicalChapter(title="Последняя", content_elements=["Конец ознакомительного фрагмента"]),  # Фрагмент (приоритет 1)
            ]
        )

        # Полная валидация
        full_anomalies = self.validator.validate(multi_anomaly_book)
        
        # Оптимизированная валидация
        first_anomaly = self.validator.get_first_blocking_anomaly(multi_anomaly_book)
        
        # Проверяем что возвращается аномалия с наивысшим приоритетом
        self.assertGreaterEqual(len(full_anomalies), 1)
        self.assertEqual(full_anomalies[0]["type"], "trial_fragments")  # Приоритет 1
        self.assertIsNotNone(first_anomaly)
        self.assertEqual(first_anomaly["type"], "trial_fragments")  # Приоритет 1
        self.assertEqual(first_anomaly["reason"], full_anomalies[0]["reason"])

    def test_performance_early_exit_logging(self):
        """Тест: Проверка что early exit действительно экономит вычисления."""
        # Создаем книгу с фрагментом (приоритет 1)
        fragment_book = CanonicalBook(
            title="Фрагмент",
            lang="ru",
            authors=[CanonicalAuthor(first_name="Тест", middle_name=None, last_name="Автор")],
            chapters=[
                CanonicalChapter(title="Последняя", content_elements=["Конец ознакомительного фрагмента"]),
            ]
        )

        # Мокаем детекторы для подсчета вызовов
        with patch.object(self.validator.fragment_detector, 'is_fragment', return_value=True) as mock_fragment, \
             patch.object(self.validator.fragment_detector, 'get_fragment_reason', return_value="Test fragment") as mock_fragment_reason, \
             patch.object(self.validator.small_book_detector, 'check_book_structure') as mock_small, \
             patch.object(self.validator.anthology_detector, 'is_anthology') as mock_anthology:

            # Оптимизированная валидация
            result = self.validator.get_first_blocking_anomaly(fragment_book)
            
            # Проверяем что фрагмент детектор вызван
            self.assertTrue(mock_fragment.called)
            self.assertTrue(mock_fragment_reason.called)

            # Проверяем что остальные детекторы НЕ вызваны (early exit)
            self.assertFalse(mock_small.called)
            self.assertFalse(mock_anthology.called)
            
            # Проверяем результат
            self.assertIsNotNone(result)
            self.assertEqual(result["type"], "trial_fragments")


if __name__ == '__main__':
    unittest.main()
