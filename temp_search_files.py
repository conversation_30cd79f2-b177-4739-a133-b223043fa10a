#!/usr/bin/env python3
"""
Временный скрипт для поиска данных в statistical_report.csv
"""

import csv

# Список файлов для поиска
target_files = [
    "1752.fb2", "7285.fb2", "7824.fb2", "7820.fb2", "7828.fb2", "7822.fb2", 
    "7823.fb2", "7819.fb2", "7840.fb2", "5853.fb2", "5852.fb2", "2155.fb2", 
    "6659.fb2", "6658.fb2", "6919.fb2", "548.fb2", "550.fb2", "8041.fb2", 
    "8352.fb2", "8353.fb2", "8354.fb2", "8357.fb2", "8355.fb2", "8361.fb2", 
    "1752.fb2", "7834.fb2", "7826.fb2", "7824.fb2", "7833.fb2", "7820.fb2", 
    "7828.fb2", "7825.fb2", "7818.fb2", "7836.fb2", "7832.fb2", "7822.fb2", 
    "7835.fb2", "7823.fb2", "7839.fb2", "7819.fb2", "7838.fb2", "7830.fb2", 
    "7840.fb2"
]

results = []

try:
    with open('tools/statistical_report.csv', 'r', encoding='utf-8') as file:
        reader = csv.reader(file, delimiter=';')
        header = next(reader)  # Пропускаем заголовок
        
        for row in reader:
            if len(row) >= 5:  # Убеждаемся что есть достаточно колонок
                filename = row[1]  # Колонка B (индекс 1)
                chapters_count = row[3]  # Колонка D (индекс 3) 
                total_chars = row[4]  # Колонка E (индекс 4)
                
                if filename in target_files:
                    results.append({
                        'filename': filename,
                        'chapters_count': chapters_count,
                        'total_chars': total_chars
                    })

    # Выводим результаты
    print("Найденные файлы:")
    print("Файл | Количество глав (D) | Общее количество символов (E)")
    print("-" * 60)
    
    for result in results:
        print(f"{result['filename']} | {result['chapters_count']} | {result['total_chars']}")
        
    print(f"\nВсего найдено: {len(results)} файлов")
    
except Exception as e:
    print(f"Ошибка: {e}")